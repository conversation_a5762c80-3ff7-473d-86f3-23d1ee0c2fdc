{"name": "soybean-admin", "type": "module", "version": "1.0.0", "packageManager": "pnpm@8.15.4", "description": "A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite3、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "naive-ui", "naive-ui-admin", "ant-design-vue v4", "UnoCSS"], "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "dev": "vite --mode dev", "dev:prod": "vite --mode prod", "dev:test": "vite --mode test", "gen-route": "sa gen-route", "lint": "eslint . --fix", "preview": "vite preview", "release": "sa release", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/vue": "4.1.1", "@province-city-china/level": "^8.5.8", "@sa/axios": "workspace:*", "@sa/color-palette": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@tinymce/tinymce-vue": "^5.1.1", "@vicons/antd": "^0.12.0", "@vicons/carbon": "^0.12.0", "@vicons/fa": "^0.12.0", "@vicons/ionicons4": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vicons/material": "^0.12.0", "@vueuse/core": "10.9.0", "clipboard": "2.0.11", "copy-to-clipboard": "^3.3.3", "dayjs": "1.11.10", "decimal.js": "^10.5.0", "echarts": "5.5.0", "lodash-es": "4.17.21", "naive-ui": "^2.41.0", "nprogress": "0.2.0", "pinia": "2.1.7", "tinymce": "^7.0.0", "vue": "3.4.21", "vue-draggable-plus": "0.3.5", "vue-i18n": "9.10.1", "vue-router": "4.3.0", "vue3-video-play": "1.3.1-beta.6", "vuedraggable": "^4.1.0"}, "devDependencies": {"@elegant-router/vue": "0.3.6", "@iconify/json": "2.2.189", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.2.3", "@types/lodash-es": "4.17.12", "@types/node": "20.11.25", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "0.58.5", "@unocss/preset-icons": "0.58.5", "@unocss/preset-uno": "0.58.5", "@unocss/transformer-directives": "0.58.5", "@unocss/transformer-variant-group": "0.58.5", "@unocss/vite": "0.58.5", "@vicons/fluent": "^0.12.0", "@vitejs/plugin-vue": "5.0.4", "@vitejs/plugin-vue-jsx": "3.1.0", "eslint": "8.57.0", "eslint-plugin-vue": "9.22.0", "lint-staged": "15.2.2", "sass": "1.71.1", "simple-git-hooks": "2.10.0", "tsx": "4.7.1", "typescript": "5.4.2", "unplugin-icons": "0.18.5", "unplugin-vue-components": "0.26.0", "vite": "5.1.5", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.0.16", "vue-eslint-parser": "9.4.2", "vue-tsc": "2.0.6"}}