import { request } from "@/service/request";


export function fetchOrgList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.Org>>({
    url: '/orgs',
    method: 'get',
    params
  });
}

export function createOrg(data: object) {
  return request<Api.Org.Org>({
    url: '/orgs',
    method: 'post',
    data
  });
}

export function updateOrg(id: number, data: object) {
  return request<Api.Org.Org>({
    url: `/orgs/${id}`,
    method: 'put',
    data
  });
}

export function deleteOrg(id: number) {
  return request({
    url: `/orgs/${id}`,
    method: 'delete'
  });
}

export function searchOrg(keyword: string) {
  return request<Api.Org.Org[]>({
    url: '/orgs/search',
    method: 'get',
    params: { keyword }
  });
}

/** 创建机构管理员 */
export function createOrgAdmin(orgId: number, data: object) {
  return request<Api.Org.Admin>({
    url: `/org/${orgId}/admins`,
    method: 'post',
    data
  });
}

/** 更新机构管理员密码 */
export function updateOrgAdminPassword(orgId: number, adminId: number, data: object) {
  return request<Api.Org.Admin>({
    url: `/org/${orgId}/admins/password/${adminId}`,
    method: 'put',
    data
  });
}

/** 获取机构余额记录 */
export function fetchOrgBalanceRecords(params: {
  org_id: number;
  current: number;
  size: number;
}) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.BalanceRecord>>({
    url: `/org/${params?.org_id}/balances`,
    method: 'get',
    params
  });
}

/** 设置机构余额 */
export function updateOrgBalance(
  orgId: number,
  data: {
    type: 'income' | 'expense';
    amount: number;
    remark: string;
  }
) {
  return request({
    url: `/org/${orgId}/balances`,
    method: 'post',
    data
  });
}

/** 获取机构课程列表 */
export function fetchOrgCourses(params: { org_id: number; current: number; size: number; keywords?: string }) {
  return request<Api.Org.CourseListData>({
    url: `/org/${params?.org_id}/courses`,
    method: 'get',
    params
  });
}

/** 获取机构题库列表 */
export function fetchOrgTopics(params?: { org_id: number; current: number; size: number; keywords?: string }) {
  return request<Api.Org.TopicListData>({
    url: `/org/${params?.org_id}/topics`,
    method: 'get',
    params
  });
}

/** 获取机构题库列表 */
export function fetchOrgCoursePacks(params?: { org_id: number; current: number; size: number; keywords?: string }) {
  return request<Api.Org.CoursePackListData>({
    url: `/org/${params?.org_id}/course-packs`,
    method: 'get',
    params
  });
}

/** 添加机构课程 */
export function addOrgCourses(
  org_id: number,
  data: {
    courses: Array<{
      id: number;
      price: number;
    }>;
  }
) {
  return request({
    url: `/org/${org_id}/courses`,
    method: 'post',
    data
  });
}

/** 添加机构题库 */
export function addOrgTopics(
  org_id: number,
  data: {
    topics: Array<{
      id: number;
      price30: number;
      price60: number;
    }>;
  }
) {
  return request({
    url: `/org/${org_id}/topics`,
    method: 'post',
    data
  });
}

/** 添加机构题库 */
export function addOrgCoursePacks(
  org_id: number,
  data: {
    course_packs: Array<{
      id: number;
      price: number;
    }>;
  }
) {
  return request({
    url: `/org/${org_id}/course-packs`,
    method: 'post',
    data
  });
}

/** 删除机构课程 */
export function deleteOrgCourse(org_id: number, id: number) {
  return request({
    url: `/org/${org_id}/courses/${id}`,
    method: 'delete'
  });
}

/** 删除机构题库 */
export function deleteOrgTopic(org_id: number, id: number) {
  return request({
    url: `/org/${org_id}/topics/${id}`,
    method: 'delete'
  });
}

/** 删除机构题库 */
export function deleteOrgCoursePack(org_id: number, id: number) {
  return request({
    url: `/org/${org_id}/course-packs/${id}`,
    method: 'delete'
  });
}

/** 更新机构课程价格 */
export function updateOrgCoursePrice(org_id: number, course_id: number, price: number) {
  return request({
    url: `/org/${org_id}/courses/${course_id}`,
    method: 'put',
    data: { price }
  });
}

/**
 * 更新机构题库价格
 */
export function updateOrgTopicPrice(org_id: number, topic_id: number, price: number, field: string) {
  return request({
    url: `/org/${org_id}/topics/${topic_id}`,
    method: 'put',
    data: { price, field}
  });
}

/** 更新机构题库价格 */
export function updateOrgCoursePackPrice(org_id: number, course_pack_id: number, price: number) {
  return request({
    url: `/org/${org_id}/course-packs/${course_pack_id}`,
    method: 'put',
    data: { price }
  });
}
