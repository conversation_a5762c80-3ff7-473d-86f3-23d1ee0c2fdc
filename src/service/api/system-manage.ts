import { request } from '../request';

/** get role list */
export function fetchGetRoleList(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.RoleList>({
    url: '/roles',
    method: 'get',
    params
  });
}

export function searchRole(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.Role[]>({
    url: '/roles/search',
    method: 'get',
    params
  });
}

export function createRole(data: object) {
  return request<Api.SystemManage.Role>({
    url: '/roles',
    method: 'post',
    data
  });
}

export function updateRole(id: number, data: object) {
  return request<Api.SystemManage.Role>({
    url: '/roles/' + id,
    method: 'put',
    data
  });
}

export function deleteRole(id: number) {
  return request({
    url: `/roles/${id}`,
    method: 'delete'
  });
}

/**
 * get all roles
 *
 * these roles are all enabled
 */
export function fetchGetAllRoles(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.Role[]>({
    url: '/roles/search',
    method: 'get',
    params
  });
}

/** get user list */
export function fetchGetUserList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserList>({
    url: '/systemManage/getUserList',
    method: 'get',
    params
  });
}

/** get menu list */
export function fetchGetMenuList(params?: object) {
  return request<Api.SystemManage.Menu[]>({
    url: '/menus',
    method: 'get',
    params
  });
}

export function createMenu(data: object) {
  return request<Api.SystemManage.Menu>({
    url: '/menus',
    method: 'post',
    data
  });
}

export function updateMenu(id: number, data: object) {
  return request<Api.SystemManage.Menu>({
    url: '/menus/' + id,
    method: 'put',
    data
  });
}

export function deleteMenu(id: number) {
  return request({
    url: `/menus/${id}`,
    method: 'delete'
  });
}

/** 管理员 */
export function fetchAdminList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.SystemManage.Admin>>({
    url: '/admins',
    method: 'get',
    params
  });
}

export function getAdmin(id: number) {
  return request<Api.SystemManage.Admin>({
    url: `/admins/${id}`,
    method: 'get'
  });
}


export function createAdmin(data: object) {
  return request<Api.SystemManage.Admin>({
    url: '/admins',
    method: 'post',
    data
  });
}

export function updateAdmin(id: number, data: object) {
  return request<Api.SystemManage.Admin>({
    url: '/admins/' + id,
    method: 'put',
    data
  });
}

export function updateAdminPassword(id: number, data: object) {
  return request<Api.SystemManage.Admin>({
    url: `/admins/${id}/updatePassword`,
    method: 'put',
    data
  });
}
