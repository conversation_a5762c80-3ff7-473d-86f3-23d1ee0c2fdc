import { request } from '../request';

export function fetchOpenCourseRecordList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.User.OpenCourseRecord>>({
    url: '/user/openCourseRecords',
    method: 'get',
    params
  });
}

export function fetchOpenCourseRecordTemplate(params?: object) {
  return request<string>({
    url: '/user/openCourseRecords/template',
    method: 'get',
    params
  });
}

export function fetchOpenCourseBatchList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.User.OpenCourseBatch>>({
    url: '/user/openCourseBatches',
    method: 'get',
    params
  });
}

export function fetchUserList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.User.User>>({
    url: '/users',
    method: 'get',
    params
  });
}

export function getUser(id: number) {
  return request<Api.User.User>({
    url: '/users/' + id,
    method: 'get'
  });
}

export function updateUser(id: number, data: object) {
  return request<Api.User.User>({
    url: '/users/' + id,
    method: 'put',
    data
  });
}

export function searchUser(keyword: string) {
  return request<Api.User.User[]>({
    url: '/users/search',
    method: 'get',
    params: { keyword }
  });
}

export function fetchUserBindList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.User.Bind>>({
    url: '/user/binds',
    method: 'get',
    params
  });
}

export function fetchUserOwnContentList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.User.OwnContent>>({
    url: '/user/ownContents',
    method: 'get',
    params
  });
}

export function removeUserOwnContent(id: number) {
  return request({
    url: `/user/ownContents/${id}`,
    method: 'delete'
  });
}

export function fetchUserOwnTopicList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.User.OwnTopic>>({
    url: '/user/ownTopics',
    method: 'get',
    params
  });
}

export function removeUserOwnTopic(id: number) {
  return request({
    url: `/user/ownTopics/${id}`,
    method: 'delete'
  });
}

export function fetchUserCreditLogList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.User.CreditLog>>({
    url: '/user/creditLogs',
    method: 'get',
    params
  });
}

export function fetchUserBalanceRecordList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.User.BalanceRecord>>({
    url: '/user/balanceRecords',
    method: 'get',
    params
  });
}

export function fetchInvitationList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.User.Invitation>>({
    url: '/user/invitations',
    method: 'get',
    params
  });
}

export function updateUserCredit(id: number, data: object) {
  return request<Api.User.User>({
    url: `/users/${id}/credit`,
    method: 'put',
    data
  });
}

export function updateUserBalance(id: number, data: object) {
  return request<Api.User.User>({
    url: `/users/${id}/balance`,
    method: 'put',
    data
  });
}

export function fetchSmsRecords(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.User.SmsRecord>>({
    url: '/sms',
    method: 'get',
    params
  });
}

export function fetchExpertList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Expert.Expert>>({
    url: '/experts',
    method: 'get',
    params
  });
}

export function getExpertConfig() {
  return request<Api.Expert.SelectOptions>({
    url: '/experts/config',
    method: 'get'
  });
}


export function createExpert(data: object) {
  return request<Api.Expert.Expert>({
    url: '/experts',
    method: 'post',
    data
  });
}

export function updateExpert(id: number, data: object) {
  return request<Api.Expert.Expert>({
    url: `/experts/${id}`,
    method: 'put',
    data
  });
}

export function rejectExpert(id: number, data: object) {
  return request<Api.Expert.Expert>({
    url: `/experts/${id}/reject`,
    method: 'put',
    data
  });
}

export function resolveExpert(id: number) {
  return request<Api.Expert.Expert>({
    url: `/experts/${id}/resolve`,
    method: 'put'
  });
}

