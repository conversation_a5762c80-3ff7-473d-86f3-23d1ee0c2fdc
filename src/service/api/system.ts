import { request } from '../request';

export function getUploadConfig(data?: object) {
  return request<Api.System.UploadConfig>({
    url: '/system/upload/config',
    method: 'post',
    data
  });
}

export function uploadFile(url: string, data: object, params?: any) {
  return request<Api.System.UploaderFile>({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    params,
    data
  });
}

export function fetchAttachmentFileList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.System.AttachmentFile>>({
    url: '/system/attachmentFiles',
    method: 'get',
    params
  });
}

export function fetchAttachmentRelationList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.System.AttachmentRelation>>({
    url: '/system/attachmentRelations',
    method: 'get',
    params
  });
}

export function fetchBoothList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.System.SettingBooth>>({
    url: '/system/booths',
    method: 'get',
    params
  });
}

export function createBooth(data: object) {
  return request<Api.System.SettingBooth>({
    url: '/system/booths',
    method: 'post',
    data
  });
}

export function updateBooth(id: number, data: object) {
  return request<Api.System.SettingBooth>({
    url: '/system/booths/' + id,
    method: 'put',
    data
  });
}

export function deleteBooth(id: number) {
  return request({
    url: `/system/booths/${id}`,
    method: 'delete'
  });
}
