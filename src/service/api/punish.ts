import { request } from '../request';

export function fetchPunishTestList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Punish.Test>>({
    url: '/punish/tests',
    method: 'get',
    params
  });
}

export function fetchPunishTestSubjectList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Punish.Test>>({
    url: '/punish/testSubjects',
    method: 'get',
    params
  });
}
