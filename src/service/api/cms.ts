import { request } from '../request';

export function fetchCategoryList(params?: object) {
  return request<Api.Cms.Category[]>({
    url: '/cms/categories',
    method: 'get',
    params
  });
}

export function createCategory(data: object) {
  return request<Api.Cms.Category>({
    url: '/cms/categories',
    method: 'post',
    data
  });
}

export function updateCategory(id: number, data: object) {
  return request<Api.Cms.Category>({
    url: `/cms/categories/${id}`,
    method: 'put',
    data
  });
}

export function deleteCategory(id: number) {
  return request({
    url: `/cms/categories/${id}`,
    method: 'delete'
  });
}

export function fetchContentList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Cms.Content>>({
    url: '/cms/contents',
    method: 'get',
    params
  });
}

export function getContent(id: number) {
  return request<Api.Cms.Content>({
    url: `/cms/contents/${id}`,
    method: 'get'
  });
}

export function searchContent(params: object) {
  return request<Api.Cms.Content[]>({
    url: '/cms/contents/search',
    method: 'get',
    params
  });
}

export function createContent(data: object) {
  return request<Api.Cms.Content>({
    url: '/cms/contents',
    method: 'post',
    data
  });
}

export function updateContent(id: number, data: object) {
  return request<Api.Cms.Content>({
    url: `/cms/contents/${id}`,
    method: 'put',
    data
  });
}

export function deleteContent(id: number) {
  return request({
    url: `/cms/contents/${id}`,
    method: 'delete'
  });
}

export function batchDeleteContent(data: object) {
  return request({
    url: '/cms/contents/batchDelete',
    method: 'delete',
    data
  });
}

export function getContentDoc(id: number) {
  return request<Api.Cms.ContentDoc>({
    url: `/cms/contentDocs/${id}`,
    method: 'get'
  });
}

export function updateContentDoc(id: number, data: object) {
  return request<Api.Cms.ContentDoc>({
    url: `/cms/contentDocs/${id}`,
    method: 'put',
    data
  });
}

export function createScreenshotTask(data: object) {
  return request<Api.System.AsyncTask>({
    url: '/cms/contentDocs/createScreenshotTask',
    method: 'post',
    data
  });
}

export function getScreenshotResult(data: object) {
  return request<{
    success: boolean;
    data: Api.System.UploaderFile[];
  }>({
    url: '/cms/contentDocs/getScreenshotResult',
    method: 'post',
    data
  });
}

export function searchContentVideo(params: object) {
  return request<Api.Cms.Content[]>({
    url: '/cms/contentVideos/search',
    method: 'get',
    params
  });
}

export function getContentVideo(id: number) {
  return request<Api.Cms.ContentVideo>({
    url: `/cms/contentVideos/${id}`,
    method: 'get'
  });
}

export function updateContentVideo(id: number, data: object) {
  return request<Api.Cms.ContentVideo>({
    url: `/cms/contentVideos/${id}`,
    method: 'put',
    data
  });
}

export function getContentRichText(id: number) {
  return request<Api.Cms.ContentRichText>({
    url: `/cms/contentRichTexts/${id}`,
    method: 'get'
  });
}

export function updateContentRichText(id: number, data: object) {
  return request<Api.Cms.ContentRichText>({
    url: `/cms/contentRichTexts/${id}`,
    method: 'put',
    data
  });
}

export function saveContentRichTextImage(data: object) {
  return request<Api.System.AttachmentFile>({
    url: '/cms/contentRichTexts/saveAttachment',
    method: 'post',
    data
  });
}

export function getContentCourse(id: number) {
  return request<Api.Cms.ContentCourse>({
    url: `/cms/contentCourses/${id}`,
    method: 'get'
  });
}

export function updateContentCourse(id: number, data: object) {
  return request<Api.Cms.ContentCourse>({
    url: `/cms/contentCourses/${id}`,
    method: 'put',
    data
  });
}

export function getContentCourseChapterChildren(params?: object) {
  return request<Api.Cms.ContentCourseChapterSection[]>({
    url: '/cms/contentCourseChapters/children',
    method: 'get',
    params
  });
}

export function createContentCourseChapter(data: object) {
  return request<Api.Cms.ContentCourseChapter>({
    url: '/cms/contentCourseChapters',
    method: 'post',
    data
  });
}

export function updateContentCourseChapter(id: number, data: object) {
  return request<Api.Cms.ContentCourseChapter>({
    url: `/cms/contentCourseChapters/${id}`,
    method: 'put',
    data
  });
}

export function deleteContentCourseChapter(id: number) {
  return request({
    url: `/cms/contentCourseChapters/${id}`,
    method: 'delete'
  });
}

export function createContentCourseSection(data: object) {
  return request<Api.Cms.ContentCourseSection>({
    url: '/cms/contentCourseSections',
    method: 'post',
    data
  });
}

export function updateContentCourseSection(id: number, data: object) {
  return request<Api.Cms.ContentCourseSection>({
    url: `/cms/contentCourseSections/${id}`,
    method: 'put',
    data
  });
}

export function deleteContentCourseSection(id: number) {
  return request({
    url: `/cms/contentCourseSections/${id}`,
    method: 'delete'
  });
}

export function fetchContentCourseDocList(params?: object) {
  return request<Api.Cms.ContentCourseDoc[]>({
    url: '/cms/contentCourseDocs',
    method: 'get',
    params
  });
}

export function createContentCourseDoc(data: object) {
  return request<Api.Cms.ContentCourseDoc>({
    url: '/cms/contentCourseDocs',
    method: 'post',
    data
  });
}

export function updateContentCourseDoc(id: number, data: object) {
  return request<Api.Cms.ContentCourseDoc>({
    url: `/cms/contentCourseDocs/${id}`,
    method: 'put',
    data
  });
}

export function deleteContentCourseDoc(id: number) {
  return request({
    url: `/cms/contentCourseDocs/${id}`,
    method: 'delete'
  });
}

export function fetchSpecialList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Cms.Special>>({
    url: '/cms/specials',
    method: 'get',
    params
  });
}

export function getSpecial(id: number) {
  return request<Api.Cms.Special>({
    url: `/cms/specials/${id}`,
    method: 'get'
  });
}

export function createSpecial(data: object) {
  return request<Api.Cms.Special>({
    url: '/cms/specials',
    method: 'post',
    data
  });
}

export function updateSpecial(id: number, data: object) {
  return request<Api.Cms.Special>({
    url: `/cms/specials/${id}`,
    method: 'put',
    data
  });
}

export function recommendSpecial(id: number, recommend: number) {
  return request<Api.Cms.Special>({
    url: `/cms/specials/${id}/recommend`,
    method: 'put',
    data: {
      recommend
    }
  });
}

export function deleteSpecial(id: number) {
  return request({
    url: `/cms/specials/${id}`,
    method: 'delete'
  });
}

export function getCourseStatistic(params?: object) {
  return request<Api.Cms.CourseStatistic>({
    url: '/cms/contentCourseStatistics/statistic',
    method: 'get',
    params
  });
}

export function fetchCourseProgressList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Cms.CourseProgress>>({
    url: '/cms/contentCourseStatistics',
    method: 'get',
    params
  });
}

export function exportCourseProgress(params?: object) {
  return request<Api.Cms.CourseProgress>({
    url: '/cms/contentCourseStatistics/export',
    method: 'get',
    params,
    responseType: 'blob',
    timeout: 1000 * 1800
  });
}

export function getCoursePackage(id: number) {
  return request<Api.Cms.ContentCoursePack>({
    url: `/cms/content/coursePackages/${id}`,
    method: 'get'
  });
}

export function updateCoursePackage(id: number, data: object) {
  return request<Api.Cms.ContentCoursePack>({
    url: `/cms/content/coursePackages/${id}`,
    method: 'put',
    data
  });
}
