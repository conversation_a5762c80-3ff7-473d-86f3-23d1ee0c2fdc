import { request } from '../request';

export function fetchErsFormLibraryList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Ers.FormLibrary>>({
    url: '/ers/formLibraries',
    method: 'get',
    params
  });
}

export function searchErsFormLibrary() {
  return request<Api.Ers.FormLibrary[]>({
    url: '/ers/formLibraries/search',
    method: 'get'
  });
}

export function createErsFormLibrary(data: object) {
  return request<Api.Ers.FormLibrary>({
    url: '/ers/formLibraries',
    method: 'post',
    data
  });
}

export function updateErsFormLibrary(id: number, data: object) {
  return request<Api.Ers.FormLibrary>({
    url: '/ers/formLibraries/' + id,
    method: 'put',
    data
  });
}

export function deleteErsFormLibrary(id: number) {
  return request({
    url: '/ers/formLibraries/' + id,
    method: 'delete'
  });
}

export function searchErsIndustry() {
  return request<Api.Ers.Industry[]>({
    url: '/ers/industryCategories/search',
    method: 'get'
  });
}

export function createErsIndustry(data: object) {
  return request<Api.Ers.Industry>({
    url: '/ers/industryCategories',
    method: 'post',
    data
  });
}

export function updateErsIndustry(id: number, data: object) {
  return request<Api.Ers.Industry>({
    url: '/ers/industryCategories/' + id,
    method: 'put',
    data
  });
}

export function moveErsIndustry(fromId: number, toId: number) {
  return request<Api.Ers.Industry>({
    url: '/ers/industryCategories/move',
    method: 'put',
    data: {
      from_id: fromId,
      to_id: toId
    }
  });
}

export function deleteErsIndustry(id: number) {
  return request({
    url: '/ers/industryCategories/' + id,
    method: 'delete'
  });
}

export function searchErsEnterprise(params: object) {
  return request<Api.Ers.Enterprise[]>({
    url: '/ers/enterpriseCategories/search',
    method: 'get',
    params
  });
}

export function createErsEnterprise(data: object) {
  return request<Api.Ers.Enterprise>({
    url: '/ers/enterpriseCategories',
    method: 'post',
    data
  });
}

export function updateErsEnterprise(id: number, data: object) {
  return request<Api.Ers.Enterprise>({
    url: '/ers/enterpriseCategories/' + id,
    method: 'put',
    data
  });
}


export function moveErsEnterprise(fromId: number, toId: number) {
  return request<Api.Ers.Industry>({
    url: '/ers/enterpriseCategories/move',
    method: 'put',
    data: {
      from_id: fromId,
      to_id: toId
    }
  });
}

export function deleteErsEnterprise(id: number) {
  return request({
    url: '/ers/enterpriseCategories/' + id,
    method: 'delete'
  });
}

export function searchErsFlow() {
  return request<Api.Ers.Enterprise[]>({
    url: '/ers/flows/search',
    method: 'get'
  });
}

export function fetchErsProjectList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Ers.Project>>({
    url: '/ers/projects',
    method: 'get',
    params
  });
}

export function getErsProject(id: number) {
  return request<Api.Ers.Project>({
    url: '/ers/projects/' + id,
    method: 'get'
  });
}

export function searchErsProject() {
  return request<Api.Ers.Project[]>({
    url: '/ers/projects/search',
    method: 'get'
  });
}

export function createErsProject(data: object) {
  return request<Api.Ers.Project>({
    url: '/ers/projects',
    method: 'post',
    data
  });
}

export function updateErsProject(id: number, data: object) {
  return request<Api.Ers.Project>({
    url: '/ers/projects/' + id,
    method: 'put',
    data
  });
}

export function deleteErsProject(id: number) {
  return request({
    url: '/ers/projects/' + id,
    method: 'delete'
  });
}

export function getErsProjectForm(data: object) {
  return request<Api.Ers.ProjectForm>({
    url: '/ers/projectForms/getForm',
    method: 'post',
    data
  });
}

export function createErsProjectInput(data: object) {
  return request<Api.Ers.Project>({
    url: '/ers/projectInputs',
    method: 'post',
    data
  });
}

export function updateErsProjectInput(id: number, data: object) {
  return request<Api.Ers.Project>({
    url: '/ers/projectInputs/' + id,
    method: 'put',
    data
  });
}

export function deleteErsProjectInput(id: number) {
  return request({
    url: '/ers/projectInputs/' + id,
    method: 'delete'
  });
}

export function fetchErsOrderList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Ers.ServiceOrder>>({
    url: '/ers/serviceOrders',
    method: 'get',
    params
  });
}

export function getErsOrder(id: number) {
  return request<Api.Ers.ServiceOrder>({
    url: '/ers/serviceOrders/' + id,
    method: 'get'
  });
}

export function getErsOrderOperators() {
  return request<Api.Ers.ServiceOrder>({
    url: '/ers/serviceOrders/getOperators',
    method: 'post'
  });
}

export function finishErsOrder(orderId: number, data: object) {
  return request<Api.Ers.ServiceOrder>({
    url: `/ers/serviceOrders/${orderId}/finish`,
    method: 'post',
    data
  });
}

export function updateErsOrderOperators(data: object) {
  return request<Api.Ers.ServiceOrder>({
    url: '/ers/serviceOrders/updateOperator',
    method: 'post',
    data
  });
}

export function auditErsOrderFormData(orderId: number, stepId: number, data: object) {
  return request<Api.Ers.ServiceOrder>({
    url: `/ers/serviceOrders/${orderId}/form/${stepId}`,
    method: 'post',
    data
  });
}

export function setErsOrderPaymentAmount(orderId: number, stepId: number, data: object) {
  return request<Api.Ers.ServiceOrder>({
    url: `/ers/serviceOrders/${orderId}/payment/${stepId}`,
    method: 'post',
    data
  });
}

export function uploadErsOrderSolution(orderId: number, stepId: number, data: object) {
  return request<Api.Ers.ServiceOrder>({
    url: `/ers/serviceOrders/${orderId}/solutionPreview/${stepId}`,
    method: 'post',
    data
  });
}

export function sendErsOrderSolution(orderId: number, stepId: number) {
  return request<Api.Ers.ServiceOrder>({
    url: `/ers/serviceOrders/${orderId}/solutionDownload/${stepId}`,
    method: 'post'
  });
}

export function fetchErsClueList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<any>>({
    url: '/ers/customClues',
    method: 'get',
    params
  });
}
