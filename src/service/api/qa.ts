import { request } from '../request';

export function fetchQuestionList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Qa.Question>>({
    url: '/qa/questions',
    method: 'get',
    params
  });
}

export function fetchQuestionAnswers(id: number) {
  return request<Api.Qa.Question>({
    url: `/qa/questions/${id}/answers`,
    method: 'get'
  });
}


export function recommendQuestion(id: number, recommend: number) {
  return request<Api.Qa.Question>({
    url: `/qa/questions/${id}/recommend`,
    method: 'put',
    data: {
      recommend
    }
  });
}

export function deleteQuestion(id: number) {
  return request({
    url: '/qa/questions/' + id,
    method: 'delete'
  });
}

export function batchDeleteQuestion(ids: number[]) {
  return request({
    url: '/qa/questions/batchDelete',
    method: 'delete',
    data: { ids }
  });
}

export function fetchAnswerList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Qa.Answer>>({
    url: '/qa/answers',
    method: 'get',
    params
  });
}

export function deleteAnswer(id: number) {
  return request({
    url: '/qa/answers/' + id,
    method: 'delete'
  });
}

export function batchDeleteAnswer(ids: number[]) {
  return request({
    url: '/qa/answers/batchDelete',
    method: 'delete',
    data: { ids }
  });
}

export function fetchChatSessionList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Chat.Session>>({
    url: '/chat/sessions',
    method: 'get',
    params
  });
}

export function fetchChatMessageList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Chat.Message>>({
    url: '/chat/messages',
    method: 'get',
    params
  });
}
