import { request } from '../request';

export function fetchPaymentList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Order.Payment>>({
    url: '/order/payments',
    method: 'get',
    params
  });
}

export function fetchOrderList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Order.Order>>({
    url: '/order/orders',
    method: 'get',
    params
  });
}

export function fetchRefundList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Order.Refund>>({
    url: '/order/refunds',
    method: 'get',
    params
  });
}
