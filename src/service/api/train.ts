import { request } from '../request';

export function fetchTopicList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Train.Topic>>({
    url: '/train/topics',
    method: 'get',
    params
  });
}

export function searchTopic(params?: object) {
  return request<Api.Train.Topic[]>({
    url: '/train/topics/search',
    method: 'get',
    params
  });
}

export function createTopic(data: object) {
  return request<Api.Train.Topic>({
    url: '/train/topics',
    method: 'post',
    data
  });
}

export function updateTopic(id: number, data: object) {
  return request<Api.Train.Topic>({
    url: `/train/topics/${id}`,
    method: 'put',
    data
  });
}

export function importTopic(id: number, filepath: string) {
  return request<Api.Train.Topic>({
    url: `/train/topics/${id}/import`,
    method: 'put',
    data: {
      filepath
    },
    timeout: 1000 * 1800
  });
}

export function fetchChapterList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Train.Chapter>>({
    url: '/train/chapters',
    method: 'get',
    params
  });
}

export function searchChapter(params?: object) {
  return request<Api.Train.Chapter[]>({
    url: '/train/chapters/search',
    method: 'get',
    params
  });
}

export function createChapter(data: object) {
  return request<Api.Train.Chapter>({
    url: '/train/chapters',
    method: 'post',
    data
  });
}

export function updateChapter(id: number, data: object) {
  return request<Api.Train.Chapter>({
    url: `/train/chapters/${id}`,
    method: 'put',
    data
  });
}

export function deleteChapter(id: number) {
  return request({
    url: `/train/chapters/${id}`,
    method: 'delete'
  });
}

export function fetchSectionList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Train.Section>>({
    url: '/train/sections',
    method: 'get',
    params
  });
}

export function createSection(data: object) {
  return request<Api.Train.Section>({
    url: '/train/sections',
    method: 'post',
    data
  });
}

export function updateSection(id: number, data: object) {
  return request<Api.Train.Section>({
    url: `/train/sections/${id}`,
    method: 'put',
    data
  });
}

export function deleteSection(id: number) {
  return request({
    url: `/train/sections/${id}`,
    method: 'delete'
  });
}

export function fetchSubjectList(params?: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Train.Subject>>({
    url: '/train/subjects',
    method: 'get',
    params
  });
}

export function getSubject(id: number) {
  return request<Api.Train.Subject>({
    url: `/train/subjects/${id}`,
    method: 'get'
  });
}

export function createSubject(data: object) {
  return request<Api.Train.Subject>({
    url: '/train/subjects',
    method: 'post',
    data
  });
}

export function updateSubject(id: number, data: object) {
  return request<Api.Train.Subject>({
    url: `/train/subjects/${id}`,
    method: 'put',
    data
  });
}

export function deleteSubject(id: number) {
  return request({
    url: `/train/subjects/${id}`,
    method: 'delete'
  });
}

export function batchDeleteSubject(data: object) {
  return request({
    url: '/train/subjects/batchDelete',
    method: 'delete',
    data
  });
}

export function getSubjectTypeCount(params?: object) {
  return request<Api.Train.TopicSubjectTypeCount>({
    url: `/train/subjects/getTypeCount`,
    method: 'get',
    params
  });
}
