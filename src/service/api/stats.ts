import { request } from '../request';

export function getStatisticPermission() {
  return request<Api.Stat.Permission>({
    url: '/stat/dailyOverview/permission',
    method: 'get'
  });
}

/** 综合统计 */
export function getStatisticSubtotalData() {
  return request<Api.Stat.SubtotalOverview>({
    url: '/stat/dailyOverview/getSubtotalData',
    method: 'get'
  });
}

/** 每日统计 */
export function getStatisticDateList(params: object) {
  return request<Api.Stat.DailyOverview[]>({
    url: '/stat/dailyOverview/getDateList',
    method: 'get',
    params
  });
}

export function fetchPromoterList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.SystemManage.Admin>>({
    url: '/stat/dailyPromoter/promoters',
    method: 'get',
    params
  });
}

export function getPromoterStatisticSubtotalData(params?: object) {
  return request<Api.Stat.SubtotalPromoter>({
    url: '/stat/dailyPromoter/getSubtotalData',
    method: 'get',
    params
  });
}

export function getPromoterStatisticDateList(params: object) {
  return request<Api.Stat.DailyPromoter[]>({
    url: '/stat/dailyPromoter/getDateList',
    method: 'get',
    params
  });
}

export function getRankList(params: object) {
  return request<any>({
    url: '/stat/rankList',
    method: 'get',
    params
  });
}

export function permissionStat() {
  return request<any>({
    url: '/stat/dailyOverview/permission/stat',
    method: 'get',
  });
}

export function getContentDocSort(params: object) {
  return request<any>({
    url: '/stat/dailyPromoter/contentDownloadSort',
    method: 'get',
    params
  });
}

export function adminContentRank(params: object) {
  return request<Api.Cms.ContentDoc[]>({
    url: '/stat/adminContentRank',
    method: 'get',
    params
  });
}
