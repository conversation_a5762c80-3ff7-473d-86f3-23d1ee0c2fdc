import type { CustomRoute, ElegantConstRoute, ElegantRoute } from '@elegant-router/types';
import { syncPermissionMenus } from '@/utils/permission';
import { generatedRoutes } from '../elegant/routes';
import { layouts, views } from '../elegant/imports';
import { getRoutePath, transformElegantRoutesToVueRoutes } from '../elegant/transform';

export const ROOT_ROUTE: CustomRoute = {
  name: 'root',
  path: '/',
  redirect: getRoutePath(import.meta.env.VITE_ROUTE_HOME) || '/home',
  meta: {
    title: 'root',
    constant: true
  }
};

const customRoutes: CustomRoute[] = [
  ROOT_ROUTE,
  {
    name: 'not-found',
    path: '/:pathMatch(.*)*',
    component: 'layout.blank$view.404',
    meta: {
      title: 'not-found',
      constant: true
    }
  }
];

/** Create routes */
export function createRoutes() {
  const constantRoutes: ElegantRoute[] = [];

  let authRoutes: ElegantConstRoute[] = [];

  [...customRoutes, ...generatedRoutes].forEach(item => {
    if (item.meta?.constant) {
      constantRoutes.push(item);
    } else {
      authRoutes.push(item);
    }
  });

  authRoutes = syncPermissionMenus(authRoutes);

  const constantVueRoutes = transformElegantRoutesToVueRoutes(constantRoutes, layouts, views);

  return {
    constantVueRoutes,
    authRoutes
  };
}

/**
 * Get auth vue routes
 *
 * @param routes Elegant routes
 */
export function getAuthVueRoutes(routes: ElegantConstRoute[]) {
  return transformElegantRoutesToVueRoutes(routes, layouts, views);
}
