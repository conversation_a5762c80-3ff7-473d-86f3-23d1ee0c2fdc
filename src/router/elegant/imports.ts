/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  cms_category: () => import("@/views/cms/category/index.vue"),
  "cms_content-draft": () => import("@/views/cms/content-draft/index.vue"),
  "cms_content-recycle": () => import("@/views/cms/content-recycle/index.vue"),
  cms_content: () => import("@/views/cms/content/index.vue"),
  "cms_course-pack": () => import("@/views/cms/course-pack/index.vue"),
  "cms_course-statistic": () => import("@/views/cms/course-statistic/index.vue"),
  cms_course: () => import("@/views/cms/course/index.vue"),
  cms_material: () => import("@/views/cms/material/index.vue"),
  cms_news: () => import("@/views/cms/news/index.vue"),
  cms_special: () => import("@/views/cms/special/index.vue"),
  ers_category: () => import("@/views/ers/category/index.vue"),
  ers_clue: () => import("@/views/ers/clue/index.vue"),
  ers_flow: () => import("@/views/ers/flow/index.vue"),
  "ers_form-library": () => import("@/views/ers/form-library/index.vue"),
  "ers_order-detail": () => import("@/views/ers/order-detail/index.vue"),
  ers_order: () => import("@/views/ers/order/index.vue"),
  "ers_project-setting": () => import("@/views/ers/project-setting/index.vue"),
  ers_project: () => import("@/views/ers/project/index.vue"),
  expert_index: () => import("@/views/expert/index/index.vue"),
  home: () => import("@/views/home/<USER>"),
  manage_admin: () => import("@/views/manage/admin/index.vue"),
  manage_attachment: () => import("@/views/manage/attachment/index.vue"),
  manage_menu: () => import("@/views/manage/menu/index.vue"),
  manage_role: () => import("@/views/manage/role/index.vue"),
  "manage_user-detail": () => import("@/views/manage/user-detail/[id].vue"),
  manage_user: () => import("@/views/manage/user/index.vue"),
  operation_banner: () => import("@/views/operation/banner/index.vue"),
  order_index: () => import("@/views/order/index/index.vue"),
  order_payment: () => import("@/views/order/payment/index.vue"),
  order_refund: () => import("@/views/order/refund/index.vue"),
  org_index: () => import("@/views/org/index/index.vue"),
  org_resource: () => import("@/views/org/resource/index.vue"),
  punish_test: () => import("@/views/punish/test/index.vue"),
  qa_answer: () => import("@/views/qa/answer/index.vue"),
  qa_chat: () => import("@/views/qa/chat/index.vue"),
  qa_question: () => import("@/views/qa/question/index.vue"),
  "stat_content-rank": () => import("@/views/stat/content-rank/index.vue"),
  stat_index: () => import("@/views/stat/index/index.vue"),
  stat_promoter: () => import("@/views/stat/promoter/index.vue"),
  train_chapter: () => import("@/views/train/chapter/index.vue"),
  "train_subject-edit": () => import("@/views/train/subject-edit/index.vue"),
  train_subject: () => import("@/views/train/subject/index.vue"),
  train_topic: () => import("@/views/train/topic/index.vue"),
  "user_balance-records": () => import("@/views/user/balance-records/index.vue"),
  "user_credit-log": () => import("@/views/user/credit-log/index.vue"),
  user_detail: () => import("@/views/user/detail/index.vue"),
  user_index: () => import("@/views/user/index/index.vue"),
  user_invitation: () => import("@/views/user/invitation/index.vue"),
  "user_open-course-batches": () => import("@/views/user/open-course-batches/index.vue"),
  "user_open-course-records": () => import("@/views/user/open-course-records/index.vue"),
  "user_sms-records": () => import("@/views/user/sms-records/index.vue"),
};
