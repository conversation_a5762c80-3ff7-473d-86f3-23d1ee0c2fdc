/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true
    }
  },
  {
    name: 'cms',
    path: '/cms',
    component: 'layout.base',
    meta: {
      title: 'cms',
      i18nKey: 'route.cms',
      icon: 'bxs:book-content',
      order: 12,
      roles: []
    },
    children: [
      {
        name: 'cms_category',
        path: '/cms/category',
        component: 'view.cms_category',
        meta: {
          title: 'cms_category',
          i18nKey: 'route.cms_category',
          icon: 'carbon:category',
          order: 99,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'cms_content',
        path: '/cms/content',
        component: 'view.cms_content',
        meta: {
          title: 'cms_content',
          i18nKey: 'route.cms_content',
          icon: 'tabler:send',
          order: 99,
          roles: [],
          hideInMenu: true,
          keepAlive: false
        }
      },
      {
        name: 'cms_content-draft',
        path: '/cms/content-draft',
        component: 'view.cms_content-draft',
        meta: {
          title: 'cms_content-draft',
          i18nKey: 'route.cms_content-draft',
          icon: 'ri:draft-line',
          order: 99,
          roles: [],
          hideInMenu: true,
          keepAlive: true
        }
      },
      {
        name: 'cms_content-recycle',
        path: '/cms/content-recycle',
        component: 'view.cms_content-recycle',
        meta: {
          title: 'cms_content-recycle',
          i18nKey: 'route.cms_content-recycle',
          icon: 'streamline:recycle-bin-2',
          order: 99,
          roles: [],
          hideInMenu: true,
          keepAlive: true
        }
      },
      {
        name: 'cms_course',
        path: '/cms/course',
        component: 'view.cms_course',
        meta: {
          title: 'cms_course',
          i18nKey: 'route.cms_course',
          icon: 'tdesign:course',
          order: 3,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'cms_course-pack',
        path: '/cms/course-pack',
        component: 'view.cms_course-pack',
        meta: {
          title: 'cms_course-pack',
          i18nKey: 'route.cms_course-pack',
          order: 4,
          icon: 'material-symbols:package-2-outline'
        }
      },
      {
        name: 'cms_course-statistic',
        path: '/cms/course-statistic',
        component: 'view.cms_course-statistic',
        meta: {
          title: 'cms_course-statistic',
          i18nKey: 'route.cms_course-statistic',
          hideInMenu: true
        }
      },
      {
        name: 'cms_material',
        path: '/cms/material',
        component: 'view.cms_material',
        meta: {
          title: 'cms_material',
          i18nKey: 'route.cms_material',
          icon: 'iconoir:google-docs',
          order: 1,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'cms_news',
        path: '/cms/news',
        component: 'view.cms_news',
        meta: {
          title: 'cms_news',
          i18nKey: 'route.cms_news',
          icon: 'bx:news',
          order: 2,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'cms_special',
        path: '/cms/special',
        component: 'view.cms_special',
        meta: {
          title: 'cms_special',
          i18nKey: 'route.cms_special',
          icon: 'material-symbols:folder-special-outline',
          order: 4,
          roles: [],
          keepAlive: true
        }
      }
    ]
  },
  {
    name: 'ers',
    path: '/ers',
    component: 'layout.base',
    meta: {
      title: 'ers',
      i18nKey: 'route.ers',
      icon: 'wpf:security-checked',
      order: 81,
      roles: []
    },
    children: [
      {
        name: 'ers_category',
        path: '/ers/category',
        component: 'view.ers_category',
        meta: {
          title: 'ers_category',
          i18nKey: 'route.ers_category',
          icon: 'mingcute:classify-2-fill',
          order: 2,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'ers_clue',
        path: '/ers/clue',
        component: 'view.ers_clue',
        meta: {
          title: 'ers_clue',
          i18nKey: 'route.ers_clue',
          icon: 'fluent:contact-card-24-regular',
          order: 1,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'ers_flow',
        path: '/ers/flow',
        component: 'view.ers_flow',
        meta: {
          title: 'ers_flow',
          i18nKey: 'route.ers_flow',
          icon: 'hugeicons:flowchart-02',
          order: 3,
          roles: [],
          keepAlive: true,
          hideInMenu: true
        }
      },
      {
        name: 'ers_form-library',
        path: '/ers/form-library',
        component: 'view.ers_form-library',
        meta: {
          title: 'ers_form-library',
          i18nKey: 'route.ers_form-library',
          icon: 'mdi:form-outline',
          order: 9,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'ers_order',
        path: '/ers/order',
        component: 'view.ers_order',
        meta: {
          title: 'ers_order',
          i18nKey: 'route.ers_order',
          icon: 'material-symbols:order-approve-outline',
          order: 0,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'ers_order-detail',
        path: '/ers/order-detail',
        component: 'view.ers_order-detail',
        meta: {
          title: 'ers_order-detail',
          i18nKey: 'route.ers_order-detail',
          icon: 'material-symbols:order-approve-outline',
          order: 0,
          roles: [],
          hideInMenu: true
        }
      },
      {
        name: 'ers_project',
        path: '/ers/project',
        component: 'view.ers_project',
        meta: {
          title: 'ers_project',
          i18nKey: 'route.ers_project',
          icon: 'mdi:security',
          order: 1,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'ers_project-setting',
        path: '/ers/project-setting',
        component: 'view.ers_project-setting',
        meta: {
          title: 'ers_project-setting',
          i18nKey: 'route.ers_project-setting',
          icon: 'mdi:security',
          order: 1,
          hideInMenu: true
        }
      }
    ]
  },
  {
    name: 'expert',
    path: '/expert',
    component: 'layout.base',
    meta: {
      title: 'expert',
      i18nKey: 'route.expert',
      icon: 'icon-park-solid:data-user',
      order: 82,
      roles: []
    },
    children: [
      {
        name: 'expert_index',
        path: '/expert/index',
        component: 'view.expert_index',
        meta: {
          title: 'expert_index',
          i18nKey: 'route.expert_index',
          icon: 'vaadin:user-card',
          order: 0,
          keepAlive: true
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 0
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true
    }
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: 'manage',
      i18nKey: 'route.manage',
      icon: 'carbon:cloud-service-management',
      order: 99,
      roles: []
    },
    children: [
      {
        name: 'manage_admin',
        path: '/manage/admin',
        component: 'view.manage_admin',
        meta: {
          title: 'manage_admin',
          i18nKey: 'route.manage_admin',
          icon: 'ic:round-manage-accounts',
          order: 1,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'manage_attachment',
        path: '/manage/attachment',
        component: 'view.manage_attachment',
        meta: {
          title: 'manage_attachment',
          i18nKey: 'route.manage_attachment',
          icon: 'pepicons-pencil:file',
          order: 99,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'manage_menu',
        path: '/manage/menu',
        component: 'view.manage_menu',
        meta: {
          title: 'manage_menu',
          i18nKey: 'route.manage_menu',
          icon: 'material-symbols:route',
          order: 3,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          title: 'manage_role',
          i18nKey: 'route.manage_role',
          icon: 'carbon:user-role',
          order: 2,
          roles: []
        }
      },
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: 'manage_user',
          i18nKey: 'route.manage_user',
          icon: 'ic:round-manage-accounts',
          order: 1,
          roles: [],
          hideInMenu: true
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        props: true,
        meta: {
          title: 'manage_user-detail',
          i18nKey: 'route.manage_user-detail',
          hideInMenu: true,
          roles: [],
          activeMenu: 'manage_user'
        }
      }
    ]
  },
  {
    name: 'operation',
    path: '/operation',
    component: 'layout.base',
    meta: {
      title: 'operation',
      i18nKey: 'route.operation',
      icon: 'carbon:operations-record',
      order: 97,
      roles: []
    },
    children: [
      {
        name: 'operation_banner',
        path: '/operation/banner',
        component: 'view.operation_banner',
        meta: {
          title: 'operation_banner',
          i18nKey: 'route.operation_banner',
          icon: 'ri:slideshow-line',
          order: 1,
          roles: [],
          keepAlive: true
        }
      }
    ]
  },
  {
    name: 'order',
    path: '/order',
    component: 'layout.base',
    meta: {
      title: 'order',
      i18nKey: 'route.order',
      icon: 'ant-design:money-collect-outlined',
      order: 80,
      roles: []
    },
    children: [
      {
        name: 'order_index',
        path: '/order/index',
        component: 'view.order_index',
        meta: {
          title: 'order_index',
          i18nKey: 'route.order_index',
          icon: 'carbon:product',
          order: 1,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'order_payment',
        path: '/order/payment',
        component: 'view.order_payment',
        meta: {
          title: 'order_payment',
          i18nKey: 'route.order_payment',
          icon: 'ant-design:money-collect-outlined',
          order: 2,
          roles: [],
          keepAlive: false,
          hideInMenu: true
        }
      },
      {
        name: 'order_refund',
        path: '/order/refund',
        component: 'view.order_refund',
        meta: {
          title: 'order_refund',
          i18nKey: 'route.order_refund',
          icon: 'mingcute:card-refund-line',
          order: 3,
          roles: [],
          keepAlive: false,
          hideInMenu: true
        }
      }
    ]
  },
  {
    name: 'org',
    path: '/org',
    component: 'layout.base',
    meta: {
      title: 'org',
      i18nKey: 'route.org',
      order: 11,
      icon: 'gg:organisation'
    },
    children: [
      {
        name: 'org_index',
        path: '/org/index',
        component: 'view.org_index',
        meta: {
          title: 'org_index',
          i18nKey: 'route.org_index',
          icon: 'charm:organisation'
        }
      },
      {
        name: 'org_resource',
        path: '/org/resource',
        component: 'view.org_resource',
        meta: {
          title: 'org_resource',
          i18nKey: 'route.org_resource',
          icon: 'mdi:book-open-outline',
          hideInMenu: true
        }
      }
    ]
  },
  {
    name: 'punish',
    path: '/punish',
    component: 'layout.base',
    meta: {
      title: 'punish',
      i18nKey: 'route.punish',
      icon: 'material-symbols:action-key',
      order: 21,
      roles: []
    },
    children: [
      {
        name: 'punish_test',
        path: '/punish/test',
        component: 'view.punish_test',
        meta: {
          title: 'punish_test',
          i18nKey: 'route.punish_test',
          icon: 'healthicons:i-exam-multiple-choice-outline',
          order: 0,
          keepAlive: true
        }
      }
    ]
  },
  {
    name: 'qa',
    path: '/qa',
    component: 'layout.base',
    meta: {
      title: 'qa',
      i18nKey: 'route.qa',
      icon: 'fluent-mdl2:qand-a',
      order: 22,
      roles: []
    },
    children: [
      {
        name: 'qa_answer',
        path: '/qa/answer',
        component: 'view.qa_answer',
        meta: {
          title: 'qa_answer',
          i18nKey: 'route.qa_answer',
          icon: 'ic:outline-question-answer',
          order: 2,
          roles: [],
          keepAlive: false,
          hideInMenu: true
        }
      },
      {
        name: 'qa_chat',
        path: '/qa/chat',
        component: 'view.qa_chat',
        meta: {
          title: 'qa_chat',
          i18nKey: 'route.qa_chat',
          icon: 'line-md:chat',
          order: 3,
          roles: [],
          keepAlive: false
        }
      },
      {
        name: 'qa_question',
        path: '/qa/question',
        component: 'view.qa_question',
        meta: {
          title: 'qa_question',
          i18nKey: 'route.qa_question',
          icon: 'lucide:message-circle-question',
          order: 1,
          roles: [],
          keepAlive: true
        }
      }
    ]
  },
  {
    name: 'stat',
    path: '/stat',
    component: 'layout.base',
    meta: {
      title: 'stat',
      i18nKey: 'route.stat',
      icon: 'nimbus:stats',
      order: 98,
      roles: []
    },
    children: [
      {
        name: 'stat_content-rank',
        path: '/stat/content-rank',
        component: 'view.stat_content-rank',
        meta: {
          title: 'stat_content-rank',
          i18nKey: 'route.stat_content-rank',
          icon: 'fa6-solid:ranking-star',
          order: 1,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'stat_index',
        path: '/stat/index',
        component: 'view.stat_index',
        meta: {
          title: 'stat_index',
          i18nKey: 'route.stat_index',
          icon: 'oui:stats',
          order: 2,
          roles: [],
          keepAlive: false,
          hideInMenu: true
        }
      },
      {
        name: 'stat_promoter',
        path: '/stat/promoter',
        component: 'view.stat_promoter',
        meta: {
          title: 'stat_promoter',
          i18nKey: 'route.stat_promoter',
          icon: 'fa6-solid:chalkboard-user',
          order: 1,
          roles: [],
          keepAlive: true
        }
      }
    ]
  },
  {
    name: 'train',
    path: '/train',
    component: 'layout.base',
    meta: {
      title: 'train',
      i18nKey: 'route.train',
      icon: 'healthicons:i-training-class',
      order: 20,
      roles: []
    },
    children: [
      {
        name: 'train_chapter',
        path: '/train/chapter',
        component: 'view.train_chapter',
        meta: {
          title: 'train_chapter',
          i18nKey: 'route.train_chapter',
          icon: 'material-symbols-light:tv-options-edit-channels',
          order: 0,
          roles: [],
          keepAlive: false,
          hideInMenu: true
        }
      },
      {
        name: 'train_subject',
        path: '/train/subject',
        component: 'view.train_subject',
        meta: {
          title: 'train_subject',
          i18nKey: 'route.train_subject',
          icon: 'material-symbols-light:tv-options-edit-channels',
          order: 1,
          roles: [],
          keepAlive: false,
          hideInMenu: true
        }
      },
      {
        name: 'train_subject-edit',
        path: '/train/subject-edit',
        component: 'view.train_subject-edit',
        meta: {
          title: 'train_subject-edit',
          i18nKey: 'route.train_subject-edit',
          icon: 'material-symbols-light:tv-options-edit-channels',
          order: 2,
          roles: [],
          keepAlive: false,
          hideInMenu: true
        }
      },
      {
        name: 'train_topic',
        path: '/train/topic',
        component: 'view.train_topic',
        meta: {
          title: 'train_topic',
          i18nKey: 'route.train_topic',
          icon: 'clarity:library-line',
          order: 0,
          roles: [],
          keepAlive: true
        }
      }
    ]
  },
  {
    name: 'user',
    path: '/user',
    component: 'layout.base',
    meta: {
      title: 'user',
      i18nKey: 'route.user',
      icon: 'ooui:user-rights-ltr',
      order: 10,
      roles: []
    },
    children: [
      {
        name: 'user_balance-records',
        path: '/user/balance-records',
        component: 'view.user_balance-records',
        meta: {
          title: 'user_balance-records',
          i18nKey: 'route.user_balance-records',
          icon: 'heroicons:credit-card',
          order: 3,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'user_credit-log',
        path: '/user/credit-log',
        component: 'view.user_credit-log',
        meta: {
          title: 'user_credit-log',
          i18nKey: 'route.user_credit-log',
          icon: 'heroicons:credit-card',
          order: 2,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'user_detail',
        path: '/user/detail',
        component: 'view.user_detail',
        meta: {
          title: 'user_detail',
          i18nKey: 'route.user_detail',
          order: 0,
          hideInMenu: true
        }
      },
      {
        name: 'user_index',
        path: '/user/index',
        component: 'view.user_index',
        meta: {
          title: 'user_index',
          i18nKey: 'route.user_index',
          icon: 'majesticons:users-line',
          order: 1,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'user_invitation',
        path: '/user/invitation',
        component: 'view.user_invitation',
        meta: {
          title: 'user_invitation',
          i18nKey: 'route.user_invitation',
          icon: 'mingcute:invite-line',
          order: 4,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'user_open-course-batches',
        path: '/user/open-course-batches',
        component: 'view.user_open-course-batches',
        meta: {
          title: 'user_open-course-batches',
          i18nKey: 'route.user_open-course-batches',
          icon: 'material-symbols:list-alt-outline',
          order: 5,
          roles: [],
          keepAlive: true
        }
      },
      {
        name: 'user_open-course-records',
        path: '/user/open-course-records',
        component: 'view.user_open-course-records',
        meta: {
          title: 'user_open-course-records',
          i18nKey: 'route.user_open-course-records',
          order: 6,
          roles: [],
          keepAlive: false,
          hideInMenu: true
        }
      },
      {
        name: 'user_sms-records',
        path: '/user/sms-records',
        component: 'view.user_sms-records',
        meta: {
          title: 'user_sms-records',
          i18nKey: 'route.user_sms-records',
          order: 7,
          roles: [],
          keepAlive: true,
          icon: 'fa-solid:sms'
        }
      }
    ]
  }
];
