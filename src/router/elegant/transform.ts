/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteRecordRaw, RouteComponent } from 'vue-router';
import type { ElegantConstRoute } from '@elegant-router/vue';
import type { RouteMap, RouteKey, RoutePath } from '@elegant-router/types';

/**
 * transform elegant const routes to vue routes
 * @param routes elegant const routes
 * @param layouts layout components
 * @param views view components
 */
export function transformElegantRoutesToVueRoutes(
  routes: ElegantConstRoute[],
  layouts: Record<string, RouteComponent | (() => Promise<RouteComponent>)>,
  views: Record<string, RouteComponent | (() => Promise<RouteComponent>)>
) {
  return routes.flatMap(route => transformElegantRouteToVueRoute(route, layouts, views));
}

/**
 * transform elegant route to vue route
 * @param route elegant const route
 * @param layouts layout components
 * @param views view components
 */
function transformElegantRouteToVueRoute(
  route: ElegantConstRoute,
  layouts: Record<string, RouteComponent | (() => Promise<RouteComponent>)>,
  views: Record<string, RouteComponent | (() => Promise<RouteComponent>)>
) {
  const LAYOUT_PREFIX = 'layout.';
  const VIEW_PREFIX = 'view.';
  const ROUTE_DEGREE_SPLITTER = '_';
  const FIRST_LEVEL_ROUTE_COMPONENT_SPLIT = '$';

  function isLayout(component: string) {
    return component.startsWith(LAYOUT_PREFIX);
  }

  function getLayoutName(component: string) {
    return component.replace(LAYOUT_PREFIX, '');
  }

  function isView(component: string) {
    return component.startsWith(VIEW_PREFIX);
  }

  function getViewName(component: string) {
    return component.replace(VIEW_PREFIX, '');
  }

  function isFirstLevelRoute(item: ElegantConstRoute) {
    return !item.name.includes(ROUTE_DEGREE_SPLITTER);
  }

  function isSingleLevelRoute(item: ElegantConstRoute) {
    return isFirstLevelRoute(item) && !item.children?.length;
  }

  function getSingleLevelRouteComponent(component: string) {
    const [layout, view] = component.split(FIRST_LEVEL_ROUTE_COMPONENT_SPLIT);

    return {
      layout: getLayoutName(layout),
      view: getViewName(view)
    };
  }

  const vueRoutes: RouteRecordRaw[] = [];

  // add props: true to route
  if (route.path.includes(':') && !route.props) {
    route.props = true;
  }

  const { name, path, component, children, ...rest } = route;

  const vueRoute = { name, path, ...rest } as RouteRecordRaw;

  if (component) {
    if (isSingleLevelRoute(route)) {
      const { layout, view } = getSingleLevelRouteComponent(component);

      const singleLevelRoute: RouteRecordRaw = {
        path,
        component: layouts[layout],
        children: [
          {
            name,
            path: '',
            component: views[view],
            ...rest
          } as RouteRecordRaw
        ]
      };

      return [singleLevelRoute];
    }

    if (isLayout(component)) {
      const layoutName = getLayoutName(component);

      vueRoute.component = layouts[layoutName];
    }

    if (isView(component)) {
      const viewName = getViewName(component);

      vueRoute.component = views[viewName];
    }

  }
  
  // add redirect to child
  if (children?.length && !vueRoute.redirect) {
    vueRoute.redirect = {
      name: children[0].name
    };
  }
  
  if (children?.length) {
    const childRoutes = children.flatMap(child => transformElegantRouteToVueRoute(child, layouts, views));

    if(isFirstLevelRoute(route)) {
      vueRoute.children = childRoutes;
    } else {
      vueRoutes.push(...childRoutes);
    }
  }

  vueRoutes.unshift(vueRoute);

  return vueRoutes;
}

/**
 * map of route name and route path
 */
const routeMap: RouteMap = {
  "root": "/",
  "not-found": "/:pathMatch(.*)*",
  "exception": "/exception",
  "exception_403": "/exception/403",
  "exception_404": "/exception/404",
  "exception_500": "/exception/500",
  "403": "/403",
  "404": "/404",
  "500": "/500",
  "cms": "/cms",
  "cms_category": "/cms/category",
  "cms_content": "/cms/content",
  "cms_content-draft": "/cms/content-draft",
  "cms_content-recycle": "/cms/content-recycle",
  "cms_course": "/cms/course",
  "cms_course-pack": "/cms/course-pack",
  "cms_course-statistic": "/cms/course-statistic",
  "cms_material": "/cms/material",
  "cms_news": "/cms/news",
  "cms_special": "/cms/special",
  "ers": "/ers",
  "ers_category": "/ers/category",
  "ers_clue": "/ers/clue",
  "ers_flow": "/ers/flow",
  "ers_form-library": "/ers/form-library",
  "ers_order": "/ers/order",
  "ers_order-detail": "/ers/order-detail",
  "ers_project": "/ers/project",
  "ers_project-setting": "/ers/project-setting",
  "expert": "/expert",
  "expert_index": "/expert/index",
  "home": "/home",
  "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?",
  "manage": "/manage",
  "manage_admin": "/manage/admin",
  "manage_attachment": "/manage/attachment",
  "manage_menu": "/manage/menu",
  "manage_role": "/manage/role",
  "manage_user": "/manage/user",
  "manage_user-detail": "/manage/user-detail/:id",
  "operation": "/operation",
  "operation_banner": "/operation/banner",
  "order": "/order",
  "order_index": "/order/index",
  "order_payment": "/order/payment",
  "order_refund": "/order/refund",
  "org": "/org",
  "org_index": "/org/index",
  "org_resource": "/org/resource",
  "punish": "/punish",
  "punish_test": "/punish/test",
  "qa": "/qa",
  "qa_answer": "/qa/answer",
  "qa_chat": "/qa/chat",
  "qa_question": "/qa/question",
  "stat": "/stat",
  "stat_content-rank": "/stat/content-rank",
  "stat_index": "/stat/index",
  "stat_promoter": "/stat/promoter",
  "train": "/train",
  "train_chapter": "/train/chapter",
  "train_subject": "/train/subject",
  "train_subject-edit": "/train/subject-edit",
  "train_topic": "/train/topic",
  "user": "/user",
  "user_balance-records": "/user/balance-records",
  "user_credit-log": "/user/credit-log",
  "user_detail": "/user/detail",
  "user_index": "/user/index",
  "user_invitation": "/user/invitation",
  "user_open-course-batches": "/user/open-course-batches",
  "user_open-course-records": "/user/open-course-records",
  "user_sms-records": "/user/sms-records"
};

/**
 * get route path by route name
 * @param name route name
 */
export function getRoutePath<T extends RouteKey>(name: T) {
  return routeMap[name];
}

/**
 * get route name by route path
 * @param path route path
 */
export function getRouteName(path: RoutePath) {
  const routeEntries = Object.entries(routeMap) as [RouteKey, RoutePath][];

  const routeName: RouteKey | null = routeEntries.find(([, routePath]) => routePath === path)?.[0] || null;

  return routeName;
}
