<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { NButton } from 'naive-ui';
import type { SelectOption } from 'naive-ui';
import AddIcon from '@vicons/ionicons5/Add';
import { VueDraggable } from 'vue-draggable-plus';
import { ReorderThreeOutline } from '@vicons/ionicons5';
import { searchContent } from '@/service/api';

interface Props {
  classify?: Api.Cms.CategoryClassifyType;
  adminId?: number;
  max?: number;
  draggable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  classify: undefined,
  adminId: undefined,
  max: 10,
  draggable: false
});

interface Item {
  id: number;
  title: string;
}

const value = defineModel<number[]>('value', { required: true });
const selectedValues = ref<number[]>([]);
const selectLoading = ref<boolean>(false);
const selectedOptions = ref<SelectOption[]>([]);
const items = ref<Item[]>([]);
const showModal = ref<boolean>(false);

const loading = ref<boolean>(true);

onMounted(async () => {
  if (value.value.length > 0) {
    await init(value.value);
  } else {
    loading.value = false
  }
});

watch(items, v => {
  value.value = v.map(item => item.id);
});


async function init(ids: number[]) {
  const data = await getContents({ ids });
  loading.value = false

  const result = [];
  for (const item of data) {
    result.push({
      id: item.id,
      title: item.title
    });
  }

  // 根据 ID 的顺序排序
  items.value = result.sort((a, b) => {
    const ia = ids.indexOf(a.id);
    const ib = ids.indexOf(b.id);

    if (ia > -1 && ib > -1) {
      return ia - ib;
    }

    if (ia > -1) return -1;

    if (ib > -1) return 1;

    return 0;
  });
}

async function getContents(params: object) {
  const requestParams = props.adminId ? { admin_id: props.adminId, ...params } : params;
  const { data, error } = await searchContent(requestParams);
  if (error) {
    return [];
  }

  return data;
}

// eslint-disable-next-line @typescript-eslint/no-shadow
async function handleSearch(value: string) {
  if (!value) {
    return;
  }

  const data = await getContents({
    classify: props.classify,
    keyword: value
  });

  selectedOptions.value = [];

  for (const item of data) {
    selectedOptions.value.push({
      label: item.title,
      value: item.id
    });
  }
}

function openModal() {
  showModal.value = true;
}

function closeModal() {
  showModal.value = false;
}

async function add() {
  const ids: number[] = items.value.map(item => item.id);

  for (const id of selectedValues.value) {
    ids.push(id);
  }

  if (props.max < ids.length) {
    window.$message?.warning(`添加项目上限是${props.max}个`);
    return;
  }

  closeModal();

  await init(ids);

  // eslint-disable-next-line require-atomic-updates
  selectedValues.value = [];
  value.value = items.value.map(item => item.id);
}

function remove(id: number) {
  items.value = items.value.filter(item => item.id != id);
}
</script>

<template>
  <n-flex vertical style="width:100%;">
    <n-spin :show="loading">
    <vue-draggable v-model="items" handle=".drag" :animation="150" target="tbody" :disabled="!draggable">
      <n-table :bordered="false" :single-line="false" size="small">
        <thead>
          <tr>
            <th v-if="draggable">排序</th>
            <th>ID</th>
            <th>标题</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <template v-if="items.length > 0">
            <tr v-for="row in items" :key="row.id">
              <td class="drag" v-if="draggable"><n-icon size="20" :depth="3"><reorder-three-outline /></n-icon></td>
              <td>{{row.id}}</td>
              <td>{{row.title}}</td>
              <td>
                <n-button size="tiny" type="error" @click="remove(row.id)">删除</n-button>
              </td>
            </tr>
          </template>
          <tr v-else>
            <td colspan="4"><n-empty description="还没添加任何资料" /></td>
          </tr>
        </tbody>
      </n-table>
    </vue-draggable>
    </n-spin>

    <n-button size="small" type="primary" dashed :disabled="value.length >= max" @click="openModal()">
      <template #icon>
        <n-icon>
          <add-icon />
        </n-icon>
      </template>
      添加到列表
    </n-button>


    <n-modal v-model:show="showModal" :mask-closable="false" :close-on-esc="false">
      <n-card
        style="width: 600px; height: 200px"
        title="选择项目"
        :bordered="false"
      >
        <n-select
          v-model:value="selectedValues"
          :loading="selectLoading"
          :options="selectedOptions"
          multiple
          filterable
          clearable
          placeholder="搜索要添加的资料"
          @search="handleSearch"
        />
        <template #footer>
          <NSpace :size="16" justify="end">
            <NButton @click="closeModal">取消</NButton>
            <NButton type="primary" @click="add">确认</NButton>
          </NSpace>
        </template>
      </n-card>
    </n-modal>
  </n-flex>
</template>

<style scoped>
.drag {
  cursor: move;
}
</style>
