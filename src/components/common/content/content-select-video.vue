<script setup lang="tsx">
import { nextTick, onMounted, ref } from 'vue';
import { searchContentVideo } from '@/service/api';

defineOptions({
  name: 'ContentSelectVideo'
});

interface Props {
  width?: number;
}

withDefaults(defineProps<Props>(), {
  width: 680
});

const value = defineModel<number>('value');

const visible = ref<boolean>(false);
const loading = ref<boolean>(true);
const keyword = ref<string | undefined>(undefined);
const contentVideos = ref<Api.Cms.Content[]>([]);
const currentVideo = ref<Api.Cms.Content | null>(null);

async function openDrawer() {
  await handleSearch(3);

  visible.value = true;

  await nextTick(() => {
    document.querySelector('.video-search')?.scrollIntoView({ behavior: 'smooth' });
  });
}

function closeDrawer() {
  visible.value = false;
}

function handleSelect(row: Api.Cms.Content) {
  currentVideo.value = null;

  nextTick(() => {
    value.value = row.id;
    currentVideo.value = row;

    closeDrawer();
  });
}

async function handleSearch(limit: number = 10) {
  const { data, error } = await searchContentVideo({ keyword: keyword.value, limit });
  if (error) {
    return;
  }

  contentVideos.value = data;
}

async function init() {
  const { data, error } = await searchContentVideo({ ids: [value.value] });
  if (error) {
    return;
  }

  if (data?.length > 0) {
    currentVideo.value = data[0];
  }
}

onMounted(async () => {
  if (value.value) {
    await init();
  }

  loading.value = false;
});
</script>

<template>
  <NSpace v-if="!loading" vertical>
    <NThing v-if="currentVideo">
      <template #header>
        {{ currentVideo.title }}
      </template>
      <template #header-extra>
        <NButton type="info" size="small" @click="openDrawer">重选</NButton>
      </template>
      <VideoNode v-if="currentVideo.resource" :src="currentVideo.resource.filepath_src" width="480px" height="240px" />
    </NThing>
    <NEmpty v-else description="点击筛选按钮，筛选需要的视频">
      <template #extra>
        <NButton type="info" size="small" @click="openDrawer">筛选</NButton>
      </template>
    </NEmpty>
  </NSpace>

  <NDrawer v-model:show="visible" display-directive="show" :width="width">
    <NDrawerContent title="筛选视频" :native-scrollbar="false" closable class="pb-50px">
      <NSpace vertical class="video-search">
        <NSpace>
          <NInput v-model:value="keyword" placeholder="输入关键词搜索" clearable />
          <NButton type="info" @click="handleSearch(20)">搜索</NButton>
        </NSpace>

        <NCard title="视频列表" size="small">
          <NList v-if="contentVideos.length > 0" clickable>
            <NListItem v-for="item in contentVideos" :key="item.id">
              <NThing>
                <template #header>
                  {{ item.title }}
                </template>
                <template #header-extra>
                  <NButton type="success" @click="handleSelect(item)">选择</NButton>
                </template>
                <VideoNode
                  v-if="item.resource"
                  :src="item.resource.filepath_src"
                  :width="width - 100 + 'px'"
                  :height="width / 3 + 'px'"
                />
              </NThing>
            </NListItem>
          </NList>
          <NEmpty v-else />
        </NCard>
      </NSpace>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
