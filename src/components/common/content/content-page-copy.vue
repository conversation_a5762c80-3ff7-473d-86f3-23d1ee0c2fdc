<script setup lang="ts">
import copy from 'copy-to-clipboard';

interface Props {
  content: Api.Cms.Content;
}

const props = defineProps<Props>();

function generateWebPage(type: Api.Cms.ContentType, sid: string) {
  let path: string = '';

  // eslint-disable-next-line default-case
  switch (type) {
    case 1:
      path = `/pages/document/detail?sid=${sid}`;
      break;
    case 2:
      path = `/pages/article/detail?sid=${sid}`;
      break;
    case 3:
      path = `/pages/article/video?sid=${sid}`;
      break;
    case 4:
      path = `/pages/training/video/detail?sid=${sid}`;
      break;
  }

  return path;
}

function handleCopy() {
  copy(generateWebPage(props.content.type, props.content.sid));

  window.$message?.success('复制成功');
}
</script>

<template>
  <NButton size="small" type="info" @click="handleCopy">复制链接</NButton>
</template>

<style scoped></style>
