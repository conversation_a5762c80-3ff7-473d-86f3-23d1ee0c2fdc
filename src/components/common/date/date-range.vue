<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { dateFormat, dateSubtractToUnix, dateToUnix } from '@/utils/date';

interface Props {
  value: string[] | null;
  days?: number;
  today?: boolean;
  startPlaceholder?: string;
  endPlaceholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  value: null,
  days: undefined,
  today: true,
  startPlaceholder: '开始时间',
  endPlaceholder: '结束时间'
});

interface Emits {
  (e: 'update:value', value: string[]): void;
  (e: 'change'): void;
}

const emit = defineEmits<Emits>();

const range = ref<[number, number] | null>(null);

const format = 'YYYY-MM-DD';

function onConfirm(value: [number, number]) {
  emit('update:value', [dateFormat(value[0] / 1000, format), dateFormat(value[1] / 1000, format)]);
  emit('change');
}

function onClear() {
  emit('update:value', []);
}

onMounted(() => {
  if (props.value !== null && props.value.length === 2) {
    range.value = [dateToUnix(props.value[0]) * 1000, dateToUnix(props.value[1]) * 1000];
  } else if (props.days !== undefined && props.days >= 0) {
    range.value = [dateSubtractToUnix(7) * 1000, dateSubtractToUnix(props.today ? 0 : 1, 'end') * 1000];
  }

  if (range.value !== null) {
    emit('update:value', [dateFormat(range.value[0] / 1000, format), dateFormat(range.value[1] / 1000, format)]);
  }
});
</script>

<template>
  <NDatePicker
    v-model:value="range"
    type="daterange"
    value-format="yyyy-MM-dd"
    clearable
    :start-placeholder="props.startPlaceholder"
    :end-placeholder="props.endPlaceholder"
    :on-confirm="onConfirm"
    :on-clear="onClear"
  />
</template>

<style scoped></style>
