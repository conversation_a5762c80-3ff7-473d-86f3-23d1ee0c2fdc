<script setup lang="ts">
import dayjs from 'dayjs';
import { onMounted, ref } from 'vue';

interface Props {
  date?: any;
}

const props = withDefaults(defineProps<Props>(), {
  date: null
});

const unix = ref(0);

onMounted(() => {
  if (!props.date) {
    return;
  }

  if (typeof props.date === 'number') {
    unix.value = props.date;
  } else {
    unix.value = dayjs(props.date).unix();
  }
});
</script>

<template>
  <view v-if="unix">
    <NTime :time="unix" format="yyyy-MM-dd HH:mm:ss" time-zone="Asia/Shanghai" unix />
  </view>
  <view v-else>-</view>
</template>

<style scoped></style>
