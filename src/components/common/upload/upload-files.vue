<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { UploadFileInfo } from 'naive-ui';
import { ArchiveOutline as ArchiveIcon } from '@vicons/ionicons5';
import { getUploadConfig } from '@/service/api';

export type AttachmentType = {
  id: string;
  filename: string;
  path: string;
  path_src: string;
}

type UploadConfig = {
  file_types?: string | string[];
  storage: 'priv' | 'pub' | 'local';
  prefix?: number;
  max_size?: number;
}

interface Props {
  limit?: number;
  placeholder?: string;
  tips?: string;
  drag?: boolean;
  uploadConfig?: UploadConfig;
}

interface Emits {
  (e: 'remove', files: string[]): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {
  limit: 2,
  placeholder: '点击或者拖动文件到该区域来上传',
  tips: '请不要上传敏感数据，比如你的银行卡号和密码，信用卡号有效期和安全码',
  drag: false,
  uploadConfig: {
    file_types: '*',
    storage: 'pub',
    prefix: 0,
    max_size: 0,
  }
});

const value = defineModel<AttachmentType[]>('value', {
  default: []
});

const originalFiles = ref<AttachmentType[]>([]);
const removeFiles = ref<string[]>([]);
const uploadConfig = ref<Api.System.UploadConfig>();
const previewFileList = ref<UploadFileInfo[]>([]);

function handlePreview(files: AttachmentType[]) {
  const fileList: UploadFileInfo[] = [];

  files.forEach(item => {
    fileList.push({
      id: item.id,
      batchId: item.id,
      name: item.filename,
      status: 'finished',
      fullPath: item.path,
      url: item.path_src
    });
  })

  previewFileList.value = fileList;
}

function handleFinish({ file, event }: { file: UploadFileInfo; event?: ProgressEvent }) {
  const response = JSON.parse((event?.target as XMLHttpRequest).response) as Api.System.UploaderFile;

  value.value.push({
    id: file.id,
    filename: response.filename,
    path: response.key,
    path_src: response.url
  });

  return file;
}

function handleRemove({ file, fileList }: { file: UploadFileInfo, fileList: UploadFileInfo[] }) {
  const index = fileList.findIndex(item => file.id === item.id);

  if (index > -1) {
    removeFiles.value.push(file.fullPath);

    value.value.splice(index, 1);
  }
}

async function getConfig() {
  const { data, error } = await getUploadConfig(props.uploadConfig);
  if (error) {
    window.$message?.warning('加载上传配置失败');
    return;
  }

  uploadConfig.value = data;
}

function getAddFiles() {
  const values = value.value.map(item => item.path);

  return values.filter(item => !originalFiles.value.includes(item));
}

function getRemoveFiles() {
  return originalFiles.value.filter(item => removeFiles.value.includes(item));
}

function init(files: AttachmentType[]) {
  files.forEach(item => {
    originalFiles.value.push(item.path);
  })
}

onMounted(() => {
  if (value.value.length > 0) {
    init(value.value);
    handlePreview(value.value);
  }
});

defineExpose({
  getAddFiles,
  getRemoveFiles
});
</script>

<template>
  <NUpload
    v-model:file-list="previewFileList"
    :multiple="false"
    :max="limit"
    :action="uploadConfig?.url"
    :name="uploadConfig?.name"
    :method="uploadConfig?.method"
    :data="uploadConfig?.form_params as any"
    @finish="handleFinish"
    @remove="handleRemove"
    @before-upload="getConfig"
  >
    <NButton v-if="!drag" type="info">上传文件</NButton>
    <NUploadDragger v-else>
      <div style="margin-bottom: 12px">
        <NIcon size="48" :depth="3">
          <archive-icon />
        </NIcon>
      </div>
      <NText style="font-size: 16px">
        {{ placeholder }}
      </NText>
      <NP depth="3" style="margin: 8px 0 0 0">
        {{ tips }}
      </NP>
    </NUploadDragger>
  </NUpload>
</template>

<style scoped></style>
