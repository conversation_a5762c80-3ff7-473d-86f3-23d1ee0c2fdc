<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui';
import { ref, onMounted } from 'vue';
import { getUploadConfig } from '@/service/api';
import draggable from 'vuedraggable'

type PreviewFile = {
  name: string;
  url: string;
  imageKey: string;
}

interface Props {
  value?: string | string[];
  preview?: string | string[];
  max?: number;
  multiple?: boolean;
  storage?: Api.System.UploadStorage;
}

interface Emits {
  (e: 'update:value', updateValue: string | string[]): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {
  value: undefined,
  preview: undefined,
  max: 1,
  multiple: false,
  storage: 'pub'
});

const uploadRef = ref();
const uploadConfig = ref<Api.System.UploadConfig>();
const previewFileList = ref<PreviewFile[]>([]);

function handlePreview(previewList: string | string[]) {
  const fileList: previewList[] = [];

  if (previewList) {
    let images: string[];

    if (typeof previewList === 'string') {
      images = [previewList];
    } else {
      images = previewList;
    }

    images.forEach((item) => {
      const name = item.split('/').pop();

      fileList.push({
        name,
        url: item,
        imageKey: item
      });
    });
  }

  previewFileList.value = fileList;
}

function setPreviewFiles(files: PreviewFile[]) {
  previewFileList.value = files;
}

function handleFinish({ file, event }: { file: UploadFileInfo; event?: ProgressEvent }) {
  const response = JSON.parse((event?.target as XMLHttpRequest).response);
  const imageKey = response.key as string;

  // 插入新的文件对象
  previewFileList.value.push({
    name: response.name,
    url: response.url,
    imageKey: response.key
  });

  updateValue(imageKey, 'append');

  return file;
}

const delImg = (data:any) => {
  let index = previewFileList.value.findIndex(res => res.url == data.url);
  if (index > -1) {
    previewFileList.value.splice(index, 1);

    const valueList = previewFileList.value.map(file => {
      return file.imageKey
    });

    updateValue(valueList);
  }
}

// 排序结果
const onEnd = () => {
  const valueList = previewFileList.value.map(file => {
    return file.imageKey
  });

  updateValue(valueList);
};


function updateValue(file: string | string[], mode: 'append' | 'reset'  = 'reset') {
  if (props.max > 1) {
    let tempValue;
    if (typeof file === 'string') {
      tempValue = file ? [file] : []
    } else {
      tempValue = [...file];
    }

    if (mode == 'append') {
      emit('update:value', [...props.value, ...tempValue]);
    } else {
      emit('update:value', tempValue);
    }
  } else {
    if (typeof file == 'string') {
      emit('update:value', file);
    } else {
      emit('update:value', file[0]);
    }
  }
}


async function getConfig() {
  const { data, error } = await getUploadConfig({ file_types: ['image'], storage: props.storage });
  if (error) {
    window.$message?.success('加载上传配置失败');
    return;
  }

  uploadConfig.value = data;
}

onMounted(() => {
  if (props.preview) {
    handlePreview(props.preview);
  }
})

defineExpose({
  setPreviewFiles
});
</script>

<template>
  <n-flex vertical>
    <n-flex>
      <NUpload
        ref="uploadRef"
        :disabled="props.max <= previewFileList.length"
        :show-file-list="false"
        file-list-class="file-list"
        :multiple="props.multiple"
        :action="uploadConfig?.url"
        :name="uploadConfig?.name"
        :method="uploadConfig?.method"
        :data="uploadConfig?.form_params as any"
        @finish="handleFinish"
        @before-upload="getConfig"
      >
        <n-button>上传文件</n-button>
      </NUpload>
    </n-flex>
    <n-flex >
      <div class="draggable">
        <draggable v-model="previewFileList" @end="onEnd" itemKey="imageKey" class="grid-container">
          <template #item="{element}">
            <div class="grid-item">
              <n-image
                width="100%"
                object-fit="fill"
                :src="element?.url"
                class="grid-item-image"
                :previewed-img-props="{ style: { border: '8px solid white' } }"
              />
              <n-button tertiary class="img-del" size="tiny" @click="delImg(element)">
                删除
              </n-button>
            </div>
          </template>
        </draggable>
      </div>
    </n-flex>
  </n-flex>
</template>

<style scoped>

.draggable {
  width: 500px;
}
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 三列 */
  gap: 10px; /* 项之间的间距 */
  position: relative;
}
.grid-item {
  border: 1px solid #ccc;
  text-align: center;
  cursor: move; /* 鼠标指针变化 */
  background-color: #fff;
  transition: background-color 0.3s;
  position: relative;
}

.grid-item:hover {
  background-color: #f0f0f0; /* 悬停效果 */
}
.grid-item-image {
  max-width: 100%;
  vertical-align: top;
}
.img-del{
  position: absolute;
  right: 10px;
  top: 10px;
  background-color: rgba(0,0,0,.5);
  color: #ffffff;
}
.file-list{
  display: none;
}
</style>
