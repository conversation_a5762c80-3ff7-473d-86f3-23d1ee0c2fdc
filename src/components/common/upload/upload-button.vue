<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui';
import { nextTick, ref } from 'vue';
import { getUploadConfig } from '@/service/api';

interface Props {
  fileType?: string;
  storage?: Api.System.UploadStorage;
}

interface Emits {
  (e: 'successResponse', data?: Api.System.UploaderFile): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {
  fileType: '',
  storage: 'local'
});

const uploadConfig = ref<Api.System.UploadConfig>();

const reload = ref<boolean>(true);

function handleFinish({ file, event }: { file: UploadFileInfo; event?: ProgressEvent }) {
  const response = JSON.parse((event?.target as XMLHttpRequest).response) as Api.System.UploaderFile;

  emit('successResponse', response);

  reload.value = false;

  nextTick(() => {
    reload.value = true;
  });

  return file;
}

async function getConfig() {
  const { data, error } = await getUploadConfig({
    file_types: props.fileType ? [props.fileType] : undefined,
    storage: props.storage
  });
  if (error) {
    window.$message?.success('加载上传配置失败');
    return;
  }

  uploadConfig.value = data;
}
</script>

<template>
  <NUpload
    v-if="reload"
    :show-file-list="false"
    :max="1"
    :multiple="false"
    :action="uploadConfig?.url"
    :name="uploadConfig?.name"
    :method="uploadConfig?.method"
    :data="uploadConfig?.form_params as any"
    @finish="handleFinish"
    @before-upload="getConfig"
  >
    <slot></slot>
  </NUpload>
</template>

<style scoped></style>
