<script setup lang="ts">
import type { RawEditorOptions } from 'tinymce';
import { computed } from 'vue';
import tinymceUrl from 'tinymce/tinymce.min.js?url';
import EditorComp from '@tinymce/tinymce-vue';
import { getUploadConfig, uploadFile } from '@/service/api';
import { now } from '@/utils/date';
import { plugins, toolbar } from './constants';
import './plugins';

interface Props {
  height?: number;
  width?: number;
  toolbar?: string[];
}

interface Emits {
  (e: 'uploadSuccess', fileData: Api.System.UploaderFile): void;
}

const props = withDefaults(defineProps<Props>(), {
  height: 200,
  width: 540,
  toolbar: toolbar
});

const emit = defineEmits<Emits>();

const modelValue = defineModel<string>('value');

const initOptions = computed((): RawEditorOptions => {
  const { height, width, toolbar } = props;

  return {
    height,
    width,
    toolbar,
    menubar: false,
    plugins,
    language_url: '/langs/zh_CN.js',
    language: 'zh_CN',
    branding: false,
    default_link_target: '_blank',
    link_title: false,
    promotion: false,
    object_resizing: false,
    statusbar: true,
    resize: true,
    skin: 'oxide',
    images_upload_handler: async (blobInfo: any) => {
      const body = { file: blobInfo.blob() };
      const formData = new FormData();

      Object.keys(body).forEach(ele => {
        const item = (body as any)[ele];

        if (item !== undefined && item !== null) {
          if (typeof item === 'object' && !(item instanceof File)) {
            if (Array.isArray(item)) {
              item.forEach(f => formData.append(ele, f || ''));
            } else {
              formData.append(ele, JSON.stringify(item));
            }
          } else {
            formData.append(ele, item);
          }
        }
      });

      const { data: configResult, error: configError } = await getUploadConfig({ file_types: ['image'] });

      if (configError) {
        window.$message?.error('加载上传配置失败');
        return '';
      }

      const { data } = await uploadFile(configResult.url, formData, configResult.form_params);

      if (!data) {
        return '';
      }

      data.created_at = now();

      emit('uploadSuccess', data);

      return data.url;
    }
  };
});
</script>

<template>
  <EditorComp v-model="modelValue" :init="initOptions" :tinymce-script-src="tinymceUrl" />
</template>

<style scoped></style>
