<script setup lang="ts">
import { reactive } from 'vue';
import type { SelectMixedOption } from 'naive-ui/es/select/src/interface';
import { searchUser } from '@/service/api';
import { debounce } from '@/utils/common';

interface Props {
  placeholder?: string;
}

withDefaults(defineProps<Props>(), {
  placeholder: '搜索用户'
});

const value = defineModel<string | number | null>('value', { required: false });

const search = reactive({
  loading: false,
  options: [] as SelectMixedOption[]
});

function handleSearch(query: string) {
  if (!query) {
    return;
  }
  debounce('search-user', async () => {
    search.options = [];
    search.loading = true;

    const { data, error } = await searchUser(query);

    search.loading = false;

    if (error || data?.length === 0) {
      search.options = [];
      handleClear();
      return;
    }

    const options: SelectMixedOption[] = [];

    data.forEach(item => {
      options.push({
        label: `${item.id} ${item.nickname}`,
        value: `${item.id}`
      });
    });

    search.options = options;
  });
}

function handleClear() {
  value.value = null;
}
</script>

<template>
  <NSelect
    v-model:value="value"
    filterable
    :placeholder="placeholder"
    :options="search.options"
    :loading="search.loading"
    remote
    clearable
    @search="handleSearch"
    @clear="handleClear"
  />
</template>
