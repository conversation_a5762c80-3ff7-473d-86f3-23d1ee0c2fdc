<script setup lang="ts">
import { useRouterPush } from '@/hooks/common/router';

const { routerPush<PERSON>y<PERSON><PERSON> } = useRouterPush();

interface Props {
  user_id: number;
  user?: Api.User.User | null;
}

const props = withDefaults(defineProps<Props>(), {
  user: null
});

function goRoute() {
  routerPushByKey('user_index', { query: { user_id: props.user_id.toString() } });
}
</script>

<template>
  <NPopover v-if="props.user" trigger="hover">
    <template #trigger>
      <NButton text @click="goRoute">{{ props.user.nickname }}</NButton>
    </template>
    <span>{{ props.user_id }}</span>
  </NPopover>
  <NButton v-else text @click="goRoute">{{ props.user_id }}</NButton>
</template>
