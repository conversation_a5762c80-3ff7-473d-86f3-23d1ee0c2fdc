<script setup lang="ts">
import { reactive, ref } from 'vue';
import VideoPlay from 'vue3-video-play';
import 'vue3-video-play/dist/style.css';

const visible = ref<boolean>(false);

const options = reactive({
  width: '800px', // 播放器宽度
  height: '450px', // 播放器高度
  color: '#409eff', // 主题色
  title: '', // 视频名称
  src: '', // 视频源
  muted: false, // 静音
  webFullScreen: false,
  speedRate: ['0.75', '1.0', '1.25', '1.5', '2.0'], // 播放倍速
  autoPlay: false, // 自动播放
  loop: false, // 循环播放
  mirror: false, // 镜像画面
  ligthOff: false, // 关灯模式
  volume: 0.3, // 默认音量大小
  control: true, // 是否显示控制
  controlBtns: ['audioTrack', 'quality', 'speedRate', 'volume', 'setting', 'pip', 'pageFullScreen', 'fullScreen'] // 显示所有按钮,
});

function play(src: string, title?: string) {
  options.src = src;
  if (title) {
    options.title = title;
  }
  visible.value = true;
}

defineExpose({
  play
});
</script>

<template>
  <NModal v-model:show="visible">
    <div>
      <VideoPlay v-if="visible" v-bind="options" />
    </div>
  </NModal>
</template>

<style scoped></style>
