<script setup lang="ts">
import type { Size } from 'naive-ui/es/button/src/interface';
import { ref } from 'vue';
import { PlayCircleSharp } from '@vicons/ionicons5';

interface Props {
  src: string;
  title?: string;
  buttonName?: string;
  size?: Size;
  text?: boolean;
  icon?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  buttonName: '播放',
  icon: true,
  text: false,
  size: 'medium'
});

const videoRef = ref();

function onPlay() {
  videoRef.value.play(props.src);
}
</script>

<template>
  <div>
    <NButton type="primary" :size="size" :text="text" @click="onPlay">
      <template v-if="icon" #icon>
        <NIcon size="24">
          <PlayCircleSharp />
        </NIcon>
      </template>
      {{ buttonName }}
    </NButton>

    <VideoModal ref="videoRef" />
  </div>
</template>

<style scoped></style>
