<script setup lang="ts">
import { useBoolean } from '@sa/hooks';
import { ErrorOutlineFilled } from '@vicons/material';

interface Props {
  src: string;
  preview?: string;
  width?: number;
}

withDefaults(defineProps<Props>(), {
  width: 60,
  preview: ''
});

const { bool: loading, setTrue: showPreview } = useBoolean();

setTimeout(() => {
  showPreview();
}, 200);
</script>

<template>
  <NImage v-if="src" :width="width" :src="src" :preview-src="preview ? preview : src" fallback-src="加载失败" lazy>
    <template #placeholder>
      <NIcon v-if="loading" :size="width > 100 ? 100 : width - 5">
        <ErrorOutlineFilled />
      </NIcon>
    </template>
  </NImage>
</template>

<style scoped></style>
