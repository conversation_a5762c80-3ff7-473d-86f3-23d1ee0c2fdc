import type { ElegantConstRoute } from '@elegant-router/types';
import { localStg } from '@/utils/storage';

const permissionMenuIndex: string = 'permissionMenus';
const permissionHomeRouteIndex: string = 'permissionHomeRoute';

export function setPermissionMenus(menus: Api.SystemManage.Menu[]) {
  localStg.set(permissionMenuIndex, menus);

  setPermissionHomeRoute(menus);
}

export function getPermissionMenus(): Api.SystemManage.Menu[] {
  return localStg.get(permissionMenuIndex);
}

export function setPermissionHomeRoute(menus: Api.SystemManage.Menu[]) {
  let index = menus.findIndex(item => item.name === 'home');
  index = index >= 0 ? index : 0;
  localStg.set(permissionHomeRouteIndex, menus[index]);
}

export function getPermissionHomeRoute(): Api.SystemManage.Menu | null {
  return localStg.get(permissionHomeRouteIndex) || null;
}

export function syncPermissionMenus(roles: ElegantConstRoute[]) {
  const userInfo = localStg.get('userInfo');

  // 超管显示所有菜单
  if (userInfo?.roles.includes('R_SUPER')) {
    return roles;
  }

  let permissionRoutes = [...roles];

  const menus: Api.SystemManage.Menu[] = getPermissionMenus();

  if (menus?.length > 0) {
    permissionRoutes = handleMenus(menus, permissionRoutes);
  }

  return permissionRoutes;
}

function handleMenus(menus: Api.SystemManage.Menu[], roles: ElegantConstRoute[]) {
  const tempRoutes: ElegantConstRoute[] = [];

  roles.forEach(item => {
    const menu = getMenu(menus, item);
    if (menu) {
      const tempRoute = { ...item };

      if (item?.children && item.children.length > 0) {
        tempRoute.children = [];
        if (item.name === 'cms') {
          // 内容管理特殊处理
          item.children.forEach(cItem => {
            if (['cms_special', 'cms_category'].includes(cItem.name)) {
              if (getMenu(menus, cItem)) {
                tempRoute.children?.push(cItem);
              }
            } else {
              tempRoute.children?.push(cItem);
            }
          });
        } else {
          // 其他菜单正常处理
          tempRoute.children = handleMenus(menus, item.children);
        }
      }

      tempRoutes.push(tempRoute);
    }
  });

  return tempRoutes;
}

function getMenu(menus: Api.SystemManage.Menu[], route: ElegantConstRoute) {
  return menus.find(item => {
    return item.route_name === route.name;
  });
}
