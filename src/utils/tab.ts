import type { RouteK<PERSON> } from "@elegant-router/types";
import { useRouteStore } from "@/store/modules/route";
import { useTabStore } from "@/store/modules/tab";

export async function closeTab(routeName: string) {
  const tabStore = useTabStore();
  const routeStore = useRouteStore();

  await tabStore.removeTabByRouteName(routeName as RouteKey);
  await routeStore.reCacheRoutesByKey(routeName as RouteKey);
}

export async function pageError(routeName: string, message?: string) {
  message = message || "参数错误";

  window.$message?.warning(message);

  await closeTab(routeName);
}
