import { $t } from '@/locales';
import { getPermissions } from "@/store/modules/auth/shared";

/**
 * Transform record to option
 *
 * @example
 *   ```ts
 *   const record = {
 *     key1: 'label1',
 *     key2: 'label2'
 *   };
 *   const options = transformRecordToOption(record);
 *   // [
 *   //   { value: 'key1', label: 'label1' },
 *   //   { value: 'key2', label: 'label2' }
 *   // ]
 *   ```;
 *
 * @param record
 */
export function transformRecordToOption<T extends Record<string, string>>(record: T) {
  return Object.entries(record).map(([value, label]) => ({
    value,
    label
  })) as CommonType.Option<keyof T>[];
}

/**
 * Translate options
 *
 * @param options
 */
export function translateOptions(options: CommonType.Option<string|number>[]) {
  return options.map(option => ({
    ...option,
    label: $t(option.label as App.I18n.I18nKey)
  }));
}

/**
 * 获取文件大小
 *
 * @param bytes
 * @returns
 */
export function getFileSize(bytes: number): string {
  const fileSizeUnits = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const base = 1024;
  if (bytes) {
    let i = 0;
    if (bytes >= base) {
      let a = bytes;
      while (1) {
        a /= base;
        i++;
        if (a < base) break;
      }
    }
    return (bytes / Math.pow(base, i)).toFixed(2) + ' ' + fileSizeUnits[i];
  } else {
    return '0 KB';
  }
}


const debounceTimer: {[key: string]: any} = {};

/**
 * 接口防抖
 * @param key
 * @param callable
 * @param ms
 */
export function debounce(key: string, callable: Function, ms: number = 300) {
  if (debounceTimer[key]) {
    clearTimeout(debounceTimer[key]);
  }
  if (ms === undefined) {
    ms = 300;
  }
  debounceTimer[key] = setTimeout(() => {
    delete debounceTimer[key];
    callable();
  }, ms);
}

/**
 * 打开WEB地址
 */
export function openWebUrl(url: string) {
  window.open(url, '_blank', 'noopener=yes,noreferrer=yes');
}

/**
 * 检查工单权限
 */
export function checkServiceOrderPermission(): boolean {
  const permissions = getPermissions();

  return permissions.ers_order;
}

/**
 * 检查内容权限
 */
export function checkCmsPermission(): boolean {
  const permissions = getPermissions();

  return permissions.cms;
}
