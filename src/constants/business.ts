import { transformRecordToOption } from '@/utils/common';

export const enableStatusRecord: Record<Api.Common.EnableStatus, App.I18n.I18nKey> = {
  0: 'page.manage.common.status.disable',
  1: 'page.manage.common.status.enable'
};

export const enableStatusOptions = transformRecordToOption(enableStatusRecord);

export const displayLabel: Record<number, string> = {
  0: '隐藏',
  1: '显示'
};

export const displayOptions = transformRecordToOption(displayLabel);

export const recommendLabel: Record<number, string> = {
  0: '不推荐',
  1: '推荐'
};

export const recommendOptions = transformRecordToOption(recommendLabel);

export const categoryClassifyLabel: Record<Api.Cms.CategoryClassifyType, string> = {
  material: '资料',
  news: '资讯',
  course: '课程',
  course_pack: '课程包'
};

export const categoryClassifyOptions = transformRecordToOption(categoryClassifyLabel);

export const contentTypeLabel: Record<Api.Cms.ContentType, string> = {
  1: '文档',
  2: '文章',
  3: '视频',
  4: '课程',
  5: '课程包'
};

export const contentTypeOptions = transformRecordToOption(contentTypeLabel);

export const contentStatusLabel: Record<number, string> = {
  1: '处理中',
  2: '显示',
  3: '隐藏'
};

export const contentStatusOptions = transformRecordToOption(contentStatusLabel);

export const courseSectionStatusLabel: Record<number, string> = {
  0: '隐藏',
  1: '显示',
  2: '待处理',
  3: '处理中'
};

export const userStatusLabel: Record<number, string> = {
  0: '已禁用',
  1: '正常',
  2: '已禁言',
  3: '注销中',
  4: '已注销'
};

export const userStatusOptions = transformRecordToOption(userStatusLabel);

export const expertStatusLabel: Record<number, string> = {
  0: '待审核',
  1: '已通过',
  2: '未通过'
};

export const expertIsVisibleLabel: Record<number, string> = {
  0: '隐藏',
  1: '显示',
};

export const expertIsVisibleOptions = transformRecordToOption(expertIsVisibleLabel);

export const expertStatusOptions = transformRecordToOption(expertStatusLabel);

export const invitationStatusLabel: Record<number, string> = {
  1: '未处理',
  2: '已处理'
};

export const invitationStatusOptions = transformRecordToOption(invitationStatusLabel);

export const businessTypeLabel: Record<string, string> = {
  order: '积分充值',
  invitation: '邀请奖励',
  'cms.material': '购买资料',
  'cms.course': '购买课程',
  special: '购买专题',
  activity: '系统活动',
  question: '动态奖励'
};

export const businessTypeOptions = transformRecordToOption(businessTypeLabel);

export const orderBusinessTypeLabel: Record<string, string> = {
  credit: '积分充值',
  'cms.material': '购买资料',
  'cms.course': '购买课程',
  special: '购买专题',
  topic: '购买题库'
};

export const orderBusinessTypeOptions = transformRecordToOption(orderBusinessTypeLabel);

export const orderStatusLabel: Record<number, string> = {
  0: '待支付',
  1: '已支付'
};

export const orderStatusOptions = transformRecordToOption(orderStatusLabel);

export const questionStatusLabel: Record<number, string> = {
  0: '待审核',
  1: '已审核',
  2: '已拒绝'
};

export const questionStatusOptions = transformRecordToOption(questionStatusLabel);

export const subjectTypeLabel: Record<number, string> = {
  1: '单选',
  2: '多选',
  3: '判断',
  4: '问答'
};

export const subjectTypeOptions = transformRecordToOption(subjectTypeLabel);

export const userGenderRecord: Record<Api.SystemManage.UserGender, App.I18n.I18nKey> = {
  '1': 'page.manage.user.gender.male',
  '2': 'page.manage.user.gender.female'
};

export const userGenderOptions = transformRecordToOption(userGenderRecord);

export const menuTypeRecord: Record<Api.SystemManage.MenuType, string> = {
  0: '前端路由',
  1: '后端路由'
};

export const menuTypeOptions = transformRecordToOption(menuTypeRecord);

export const rankLabel: Record<string, string> = {
  download: '资料下载排行',
  search: '关键词搜索排行'
}

export const FormTypeLabel: Record<Api.Ers.FormType, string> = {
  group: '分组',
  text: '单行文本',
  textarea: '多行文本',
  select: '单选',
  checkbox: '多选',
  image: '图片上传',
  file: '文件上传'
};

export const FormTypeOptions = transformRecordToOption(FormTypeLabel);


export const FormTextTypeLabel: Record<string, string> = {
  text: '文本',
  number: '数字',
  tel: '电话'
};

export const FormTextTypeOptions = transformRecordToOption(FormTextTypeLabel);

export const ersProjectStatusLabel: Record<number, string> = {
  0: '关闭',
  1: '开启',
};

export const ersProjectStatusOptions = transformRecordToOption(ersProjectStatusLabel);

export const courseProgressStatusLabel: Record<number, string> = {
  1: '未学',
  2: '学习中',
  3: '已学'
};

export const courseProgressStatusOptions = transformRecordToOption(courseProgressStatusLabel);

export const examConfigTypeRecord: Record<Api.Train.TopicExamConfigType, string> = {
  single_choice: '单选题',
  multiple_choice: '多选题',
  judge: '判断题'
};

export const examConfigTypeOptions = transformRecordToOption(examConfigTypeRecord);

export const ownContentClassifyLabel: Record<string, string> = {
  course: '课程',
  course_pack: '课程包',
  material: '资料',
};

export const ownContentClassifyOptions = transformRecordToOption(ownContentClassifyLabel);

export const ownContentStatusLabel: Record<number, string> = {
  0: '已删除',
  1: '学习中',
  2: '已过期',
};

export const ownTopicStatusLabel: Record<number, string> = {
  0: '已删除',
  1: '学习中',
  2: '已过期',
};


