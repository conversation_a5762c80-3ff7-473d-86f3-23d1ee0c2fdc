<script setup lang="ts">
import { ref, h } from 'vue';
import type { Ref } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import DateFormat from '@/components/common/date/date-format.vue';

const visible = ref(false);

const tableData = ref<Api.System.AttachmentRelation[]>([]);

const columns: Ref<DataTableColumns<Api.System.AttachmentRelation>> = ref([
  {
    key: 'id',
    title: 'ID',
    align: 'center'
  },
  {
    key: 'file_id',
    title: '文件ID',
    align: 'center'
  },
  {
    key: 'target_id',
    title: '目标ID',
    align: 'center'
  },
  {
    key: 'target_type',
    title: '目标类型',
    align: 'center'
  },
  {
    key: 'created_at',
    title: '创建时间',
    align: 'center',
    render: row => {
      return h(DateFormat, { date: row.created_at });
    }
  }
]);

function open(data: Api.System.AttachmentRelation[]) {
  tableData.value = data;
  visible.value = true;
}

defineExpose({
  open
});
</script>

<template>
  <n-drawer v-model:show="visible" :width="780">
    <n-drawer-content title="附件关联">
      <n-data-table :columns="columns" :data="tableData" :row-key="row => row.id" />
    </n-drawer-content>
  </n-drawer>
</template>

<style scoped></style>
