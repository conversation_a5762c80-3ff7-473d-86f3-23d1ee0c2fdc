<script setup lang="ts">
import VideoButton from '@/components/common/video/video-button.vue';
import ImageSingle from '@/components/common/image/image-single.vue';
import { openWebUrl } from '@/utils/common';

interface Props {
  data: Api.System.AttachmentFile;
}

const props = defineProps<Props>();
</script>

<template>
  <div v-if="props.data.mime.indexOf('video') !== -1">
    <VideoButton :src="props.data.path" />
  </div>
  <div v-else-if="props.data.mime.indexOf('image') !== -1">
    <ImageSingle :src="props.data.path" />
  </div>
  <div v-else>
    <NButton type="info" @click="openWebUrl(props.data.path)">预览下载</NButton>
  </div>
</template>

<style scoped></style>
