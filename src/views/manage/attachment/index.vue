<script setup lang="tsx">
import { ref } from 'vue';
import { NButton, NButtonGroup } from 'naive-ui';
import { fetchAttachmentFileList, fetchAttachmentRelationList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { getFileSize } from '@/utils/common';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import TablePreview from './modules/table-preview.vue';
import TableRelation from './modules/table-relation.vue';

const relationRef = ref();

const {
  columns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.System.AttachmentFile, typeof fetchAttachmentFileList, 'index' | 'operate'>({
  apiFn: fetchAttachmentFileList,
  apiParams: {
    current: 1,
    size: 20,
    etag: null,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'mime',
      title: '文件类型',
      align: 'center',
      minWidth: 180
    },
    {
      key: 'type' as any,
      title: '文件预览',
      align: 'center',
      width: 120,
      render: row => {
        return <TablePreview data={row} />;
      }
    },
    {
      key: 'filename',
      title: '文件名称',
      align: 'center',
      minWidth: 120,
    },
    {
      key: 'etag',
      title: '文件哈希',
      align: 'center',
      minWidth: 120,
    },
    {
      key: 'filesize',
      title: '文件大小',
      align: 'center',
      width: 120,
      render: row => {
        return getFileSize(row.filesize);
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 160,
      render: row => {
        return (
          <NButtonGroup>
            <NButton type={'primary'} size='small' onClick={() => handleShowRelation(row)}>
              查看关联
            </NButton>
          </NButtonGroup>
        );
      }
    }
  ]
});

async function handleShowRelation(row: Api.System.AttachmentFile) {
  const { data, error } = await fetchAttachmentRelationList({ page: 1, limit: 100, file_id: row.id });

  if (error) {
    return;
  }

  relationRef.value.open(data.records);
}


function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="附件列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
    <TableRelation ref="relationRef" />
  </div>
</template>

<style scoped></style>
