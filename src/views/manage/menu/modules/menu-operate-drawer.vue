<script setup lang="tsx">
import { computed, reactive, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { menuTypeOptions } from '@/constants/business';
import { createMenu, updateMenu } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'MenuOperateDrawer'
});

const { loading, startLoading, endLoading } = useLoading();

/**
 * the type of operation
 *
 * - add: add user
 * - edit: edit user
 */
export type OperateType = 'add' | 'edit';

interface Props {
  /** the type of operation */
  operateType: OperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.Menu | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: $t('page.manage.menu.addMenu'),
    edit: $t('page.manage.menu.editMenu')
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.SystemManage.Menu,
  'parent_id' | 'type' | 'name' | 'icon' | 'route_name' | 'route_path' | 'method' | 'status' | 'sort'
>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    parent_id: props.rowData ? props.rowData.id : 0,
    type: 0,
    name: '',
    icon: '',
    route_name: '',
    route_path: '',
    method: 'GET',
    status: 1,
    sort: 0
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'route_name' | 'route_path' | 'method'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  route_name: defaultRequiredRule,
  route_path: defaultRequiredRule,
  method: defaultRequiredRule
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, {
      parent_id: props.rowData.parent_id,
      type: props.rowData.type,
      name: props.rowData.name,
      icon: props.rowData.icon,
      route_name: props.rowData.route_name,
      route_path: props.rowData.route_path,
      method: props.rowData.method,
      status: props.rowData.status,
      sort: props.rowData.sort
    });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  if (model.type === 1) {
    // 后端路由清除图标
    model.icon = '';
  } else {
    // 前端路由统一GET
    model.method = 'GET';
  }

  startLoading();

  if (props.operateType === 'add') {
    const { error } = await createMenu(model);

    endLoading();

    if(error) {
      return;
    }
    window.$message?.success($t('common.addSuccess'));
  } else {
    const { error } = await updateMenu(props.rowData?.id as number, model);

    endLoading();

    if(error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="580">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem :label="$t('page.manage.menu.menuType')" path="type">
          <NRadioGroup v-model:value="model.type" :disabled="props.operateType === 'edit'">
            <NRadio :value="0" :label="menuTypeOptions[0].label" />
            <NRadio :value="1" :label="menuTypeOptions[1].label" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="名称" path="name" class="w-320px">
          <NInput v-model:value="model.name" placeholder="请输入名称" />
        </NFormItem>
        <NFormItem v-if="model.type === 0" :label="$t('page.manage.menu.icon')" path="icon" class="w-380px">
          <NInput v-model:value="model.icon" :placeholder="$t('page.manage.menu.form.icon')" class="flex-1">
            <template #suffix>
              <SvgIcon v-if="model.icon" :icon="model.icon" class="text-icon" />
            </template>
          </NInput>
        </NFormItem>
        <NFormItem :label="$t('page.manage.menu.routeName')" path="route_name" class="w-320px">
          <NInput v-model:value="model.route_name" :placeholder="$t('page.manage.menu.form.routeName')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.menu.routePath')" path="route_path" class="w-380px">
          <NInput v-model:value="model.route_path" :placeholder="$t('page.manage.menu.form.routePath')" />
        </NFormItem>
        <NFormItem v-if="model.type === 1" label="请求方式" path="method">
          <NRadioGroup v-model:value="model.method">
            <NRadio value="GET" label="GET" />
            <NRadio value="POST" label="POST" />
            <NRadio value="PUT" label="PUT" />
            <NRadio value="DELETE" label="DELETE" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="是否显示" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio :value="1" :label="$t('common.yesOrNo.yes')" />
            <NRadio :value="0" :label="$t('common.yesOrNo.no')" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem :label="$t('page.manage.menu.order')" path="sort" class="w-200px">
          <NSpace vertical>
            <NInputNumber v-model:value="model.sort" :min="0" :max="10000" />
            <FormTips :tips="['数字越小越靠前']" />
          </NSpace>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit" :disabled="loading">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
