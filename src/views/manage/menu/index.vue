<script setup lang="tsx">
import { ref } from 'vue';
import { NButton, NPopconfirm, NSpace, NTag, NText } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchGetMenuList, updateMenu, deleteMenu } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { displayLabel } from '@/constants/business';
import SvgIcon from '@/components/custom/svg-icon.vue';
import MenuOperateDrawer, { type OperateType } from './modules/menu-operate-drawer.vue';
import OperateSort from '@/components/common/table/operate-sort.vue';
import DateFormat from '@/components/common/date/date-format.vue';

const appStore = useAppStore();
const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();

const wrapperRef = ref<HTMLElement | null>(null);

const { columns, filteredColumns, data, loading, pagination, getData } = useTable<
  Api.SystemManage.Menu,
  typeof fetchGetMenuList,
  'index' | 'operate'
>({
  apiFn: fetchGetMenuList,
  transformer: res => {
    const menus = res.data || [];

    return {
      data: menus,
      pageNum: 1,
      pageSize: 30,
      total: menus.length
    };
  },
  columns: () => [
    {
      key: 'name',
      title: $t('page.manage.menu.menuName'),
      align: 'center',
      width: 200
    },
    {
      key: 'icon',
      title: $t('page.manage.menu.icon'),
      align: 'center',
      width: 60,
      render: row => {
        if (row.icon) {
          return (
            <div class="flex-center">
              <SvgIcon icon={row.icon} class="text-icon" />
            </div>
          );
        } else {
          return '';
        }
      }
    },
    {
      key: 'route_name',
      title: $t('page.manage.menu.routeName'),
      align: 'center',
      width: 140
    },
    {
      key: 'route_path',
      title: $t('page.manage.menu.routePath'),
      align: 'left',
      width: 240,
      render: row => {
        const tagMap: Record<string, NaiveUI.ThemeColor> = {
          'GET': 'success',
          'POST': 'success',
          'PUT': 'warning',
          'DELETE': 'error',
        };

        if (row.type) {
          return <NSpace>
            <NTag type={tagMap[row.method]} size="small" bordered={false}>{row.method}</NTag>
            <NText>{row.route_path}</NText>
          </NSpace>;
        } else {
          return row.route_path;
        }
      }
    },
    {
      key: 'status',
      title: '是否显示',
      align: 'center',
      width: 80,
      render: row => {
        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        const label = displayLabel[row.status];

        return <NTag type={tagMap[row.status]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'sort',
      title: $t('page.manage.menu.order'),
      align: 'center',
      width: 100,
      render: row => {
        return <OperateSort id={row.id} sort={row.sort} updateSort={updateMenu} onReset={getData} />
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 240,
      render: row => (
        <div class="flex-center justify-end gap-8px mr-15px">
          {row.type === 0 && (
            <NButton type="primary" ghost size="small" onClick={() => handleAddChildMenu(row)}>
              {$t('page.manage.menu.addChildMenu')}
            </NButton>
          )}
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row)}>
            {$t('common.edit')}
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const operateType = ref<OperateType>('add');
const editingData = ref<Api.SystemManage.Menu | null>(null);

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;
  openDrawer();
}

function handleAddChildMenu(row: Api.SystemManage.Menu) {
  operateType.value = 'add';
  editingData.value = row;
  openDrawer();
}

function handleEdit(row: Api.SystemManage.Menu) {
  operateType.value = 'edit';
  editingData.value = row;
  openDrawer();
}


async function handleDelete(id: number) {
  const { error } = await deleteMenu(id);
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}
</script>

<template>
  <div ref="wrapperRef" class="flex-vertical-stretch gap-16px overflow-hidden <sm:overflow-auto">
    <NCard :title="$t('page.manage.menu.title')" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="1088"
        :indent="48"
        :loading="loading"
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
      <MenuOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
