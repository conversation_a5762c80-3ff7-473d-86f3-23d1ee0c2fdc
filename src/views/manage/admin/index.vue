<script setup lang="tsx">
import { ref } from 'vue';
import { NButton, NPopconfirm, NSpace, NTag } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchAdminList, updateAdmin } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { enableStatusRecord } from '@/constants/business';
import DateFormat from '@/components/common/date/date-format.vue';
import AdminOperateDrawer, { type OperateType } from './modules/admin-operate-drawer.vue';
import AdminSearch from './modules/admin-search.vue';

const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.SystemManage.Admin, typeof fetchAdminList, 'index' | 'operate'>({
  apiFn: fetchAdminList,
  apiParams: {
    current: 1,
    size: 20,
    username: null,
    status: null
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'username',
      title: '用户名',
      align: 'center',
      width: 150
    },
    {
      key: 'real_name',
      title: '姓名',
      align: 'center',
      width: 100
    },
    {
      key: 'phone',
      title: '手机号码',
      align: 'center',
      width: 150
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        const label = $t(enableStatusRecord[row.status]);

        return (
          <NTag type={tagMap[row.status]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'roles',
      title: '角色',
      align: 'center',
      render: row => (
        <NSpace justify="center">
          {row.roles?.map(item => {
            return (
              <NTag type="info" bordered={false}>
                {item.role?.name}
              </NTag>
            );
          })}
        </NSpace>
      )
    },

    {
      key: 'last_active_at',
      title: '最后登录时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.last_active_at} />;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 260,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row, 'edit')}>
            修改信息
          </NButton>
          <NButton size={'small'} ghost type={'warning'} onClick={() => handleEdit(row, 'password')}>
            修改密码
          </NButton>
          {row.status === 0 && (
            <NPopconfirm onPositiveClick={() => handleUpdateStatus(row.id, 1)}>
              {{
                default: () => '确认启用',
                trigger: () => (
                  <NButton size={'small'} ghost type={'info'}>
                    启用
                  </NButton>
                )
              }}
            </NPopconfirm>
          )}

          {row.status === 1 && (
            <NPopconfirm onPositiveClick={() => handleUpdateStatus(row.id, 0)}>
              {{
                default: () => '确认禁用',
                trigger: () => (
                  <NButton size={'small'} ghost type={'error'}>
                    禁用
                  </NButton>
                )
              }}
            </NPopconfirm>
          )}
        </div>
      )
    }
  ]
});

const operateType = ref<OperateType>('add');
/** the editing row data */
const editingData = ref<Api.SystemManage.Admin | null>(null);

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;

  openDrawer();
}

const checkedRowKeys = ref<string[]>([]);

async function handleUpdateStatus(rowId: number, status: number) {
  await updateAdmin(rowId, { status });

  await getData();
}

function handleEdit(row: Api.SystemManage.Admin, type: 'edit' | 'password') {
  operateType.value = type;
  editingData.value = row;
  openDrawer();
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <AdminSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="管理员列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
      <AdminOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
