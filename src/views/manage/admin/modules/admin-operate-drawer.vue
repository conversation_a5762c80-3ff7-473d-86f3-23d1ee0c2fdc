<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { createAdmin, updateAdmin, updateAdminPassword } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';
import AdminRoles from './admin-roles.vue';


defineOptions({
  name: 'AdminOperateDrawer'
});

/**
 * the type of operation
 *
 * - add: add role
 * - edit: edit role
 */
export type OperateType = 'add' | 'edit' | 'password';

const { loading, startLoading, endLoading } = useLoading();

interface Props {
  /** the type of operation */
  operateType: OperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.Admin | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: '添加管理员信息',
    edit: '编辑管理员信息',
    password: '修改管理员密码'
  };
  return titles[props.operateType];
});

type Model = Pick<Api.SystemManage.Admin, 'username' | 'real_name' | 'phone' | 'email' | 'status'> & {
  password: string,
  password_confirmation: string,
  role_ids: number[]
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    username: '',
    real_name: '',
    phone: '',
    email: '',
    status: 1,
    password: '',
    password_confirmation: '',
    role_ids: []
  };
}

let rules: Record<string, App.Global.FormRule> = {};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    rules = {
      username: defaultRequiredRule,
      password: defaultRequiredRule,
      password_confirmation: defaultRequiredRule,
      role_ids: defaultRequiredRule
    };

    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    rules = {
      role_ids: defaultRequiredRule
    };

    let roleIds: number[] = [];

    if(props.rowData?.roles && props.rowData.roles.length > 0) {
      props.rowData.roles.forEach(item => {
        roleIds.push(item.role_id);
      });
    }

    Object.assign(model, {
      username: props.rowData.username,
      real_name: props.rowData.real_name,
      phone: props.rowData.phone,
      email: props.rowData.email,
      status: props.rowData.status,
      password: '',
      password_confirmation: '',
      role_ids: roleIds
    });
  }

  if (props.operateType === 'password' && props.rowData) {
    rules = {
      password: defaultRequiredRule,
      password_confirmation: defaultRequiredRule
    };

    Object.assign(model, { ...createDefaultModel(), username: props.rowData.username });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  // request
  switch(props.operateType) {
    case 'add':
      const { error: e1 } = await createAdmin(model);
      endLoading();
      if (e1) {
        return;
      }
      break;
    case 'edit':
      const { error: e2 } = await updateAdmin(props.rowData?.id as number, model);
      endLoading();
      if (e2) {
        return;
      }
      break;
    case 'password':
      const { error: e3 } = await updateAdminPassword(props.rowData?.id as number, model);
      endLoading();
      if (e3) {
        return;
      }
      break;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="520">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="props.operateType === 'add'" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80">
        <NFormItem label="用户名" path="username" class="w-320px">
          <NInput v-model:value="model.username" />
        </NFormItem>
        <NFormItem label="姓名" path="real_name" class="w-220px">
          <NInput v-model:value="model.real_name" />
        </NFormItem>
        <NFormItem label="手机号码" path="phone" class="w-320px">
          <NInput v-model:value="model.phone" />
        </NFormItem>
        <NFormItem label="邮箱地址" path="email" class="w-320px">
          <NInput v-model:value="model.email" />
        </NFormItem>
        <NFormItem label="密码" path="password" class="w-320px">
          <NInput type="password"  v-model:value="model.password" />
        </NFormItem>
        <NFormItem label="确认密码" path="password_confirmation" class="w-320px">
          <NInput type="password"  v-model:value="model.password_confirmation" />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio :value="1" label="开启" />
            <NRadio :value="0" label="禁用" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="角色" path="role_ids">
          <AdminRoles v-if="visible" v-model:role_ids="model.role_ids" />
        </NFormItem>
      </NForm>
      <NForm v-else-if="props.operateType === 'edit'" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80">
        <NFormItem label="用户名" path="username" class="w-320px">
          <NInput v-model:value="model.username" :disabled="true" />
        </NFormItem>
        <NFormItem label="姓名" path="real_name" class="w-220px">
          <NInput v-model:value="model.real_name" />
        </NFormItem>
        <NFormItem label="手机号码" path="phone" class="w-320px">
          <NInput v-model:value="model.phone" />
        </NFormItem>
        <NFormItem label="邮箱地址" path="email" class="w-320px">
          <NInput v-model:value="model.email" />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio :value="1" label="开启" />
            <NRadio :value="0" label="禁用" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="角色" path="role_ids">
          <AdminRoles v-if="visible" v-model:role_ids="model.role_ids" />
        </NFormItem>
      </NForm>
      <NForm v-else ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80">
        <NFormItem label="用户名" path="username" class="w-320px">
          <NInput v-model:value="model.username" :disabled="true" />
        </NFormItem>
        <NFormItem label="密码" path="password" class="w-320px">
          <NInput type="password"  v-model:value="model.password" />
        </NFormItem>
        <NFormItem label="确认密码" path="password_confirmation" class="w-320px">
          <NInput type="password"  v-model:value="model.password_confirmation" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading"  @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
