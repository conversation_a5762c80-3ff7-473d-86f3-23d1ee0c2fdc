<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { createRole, updateRole } from '@/service/api';
import { $t } from '@/locales';
import { enableStatusOptions } from '@/constants/business';
import { useLoading } from '~/packages/hooks/src';
import RoleMenus from './role-menus.vue';

defineOptions({
  name: 'RoleOperateDrawer'
});

const { loading, startLoading, endLoading } = useLoading();

/**
 * the type of operation
 *
 * - add: add role
 * - edit: edit role
 */
export type OperateType = 'add' | 'edit';

interface Props {
  /** the type of operation */
  operateType: OperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.Role | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: $t('page.manage.role.addRole'),
    edit: $t('page.manage.role.editRole')
  };
  return titles[props.operateType];
});

type Model = Pick<Api.SystemManage.Role, 'name' | 'code' | 'desc' | 'status' | 'permissions'> & {
  menu_ids: number[];
  indeterminate_ids: number[];
  checked_ids: number[];
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    code: '',
    desc: '',
    status: 1,
    permissions: [],
    menu_ids: [],
    indeterminate_ids: [],
    checked_ids: [],
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'code' | 'menu_ids'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  code: defaultRequiredRule,
  menu_ids: defaultRequiredRule,
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    let indeterminateIds: number[] = [];
    let checkedIds: number[] = [];

    if (props.rowData?.menus && props.rowData.menus.length > 0) {
      props.rowData.menus.forEach(item => {
        if (item.checked === 1) {
          checkedIds.push(item.menu_id);
        } else {
          indeterminateIds.push(item.menu_id);
        }
      })
    }

    Object.assign(model, { ...props.rowData, menu_ids: [...indeterminateIds, ...checkedIds], indeterminate_ids: indeterminateIds, checked_ids: checkedIds });
  }
}

function closeDrawer() {
  visible.value = false;
}

function updateCheckedKeys(keys: number[]) {
  model.checked_ids = keys;
}

function updateIndeterminateKeys(keys: number[]) {
  model.indeterminate_ids = keys;

  model.menu_ids = [...model.checked_ids, ...model.indeterminate_ids];
}

async function handleSubmit() {
  await validate();

  startLoading();

  if (props.operateType === 'add') {
    const { error } = await createRole(model);

    endLoading();

    if(error) {
      return;
    }
    window.$message?.success($t('common.addSuccess'));
  } else {
    const { error } = await updateRole(props.rowData?.id as number, model);

    endLoading();

    if(error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="640">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem :label="$t('page.manage.role.roleName')" path="name" class="w-280px">
          <NInput v-model:value="model.name" :placeholder="$t('page.manage.role.form.roleName')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.role.roleCode')" path="code" class="w-420px">
          <NInput v-model:value="model.code" :placeholder="$t('page.manage.role.form.roleCode')" :disabled="!!rowData?.system" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.role.roleDesc')" path="desc" class="w-420px">
          <NInput v-model:value="model.desc" :placeholder="$t('page.manage.role.form.roleDesc')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.role.roleStatus')" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio :value="1" :label="$t(enableStatusOptions[1].label)" />
            <NRadio :value="0" :label="$t(enableStatusOptions[0].label)" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="特殊权限" path="permissions">
          <NCheckboxGroup v-model:value="model.permissions">
            <NSpace>
              <NCheckbox value="manage_editor" label="管理他人内容" />
              <NCheckbox value="manage_promoter" label="查看他人统计" />
              <NCheckbox value="manage_ers_order" label="管理服务工单" />
            </NSpace>
          </NCheckboxGroup>
        </NFormItem>
        <NFormItem label="权限" path="menu_ids">
          <RoleMenus
            v-if="visible"
            :checked-ids="model.checked_ids"
            @update-checked-keys="updateCheckedKeys"
            @update-indeterminate-keys="updateIndeterminateKeys"
            />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit" :disabled="loading">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
