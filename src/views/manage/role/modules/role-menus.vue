<script setup lang="ts">
import { ref } from 'vue';
import { fetchGetMenuList } from '@/service/api';

interface Props {
  checkedIds: number[],
}

defineProps<Props>();

interface Emits {
  (e: 'updateCheckedKeys', ids: number[]): void;
  (e: 'updateIndeterminateKeys', ids: number[]): void;
}

const emit = defineEmits<Emits>();

const menus = ref<Api.SystemManage.Menu[]>([]);

function updateCheckedKeys(keys: number[]) {
  emit('updateCheckedKeys', keys);
}

function updateIndeterminateKeys(keys: number[]) {
  emit('updateIndeterminateKeys', keys);
}

async function init() {
  const { data, error } = await fetchGetMenuList({ status: 1 });
  if (error) {
    return;
  }

  menus.value = data;
}

init();
</script>

<template>
  <NTree
    v-if="menus.length > 0"
    key-field="id"
    label-field="name"
    block-line
    cascade
    checkable
    :show-line="true"
    :selectable="false"
    :data="menus"
    :default-checked-keys="checkedIds"
    @update:checked-keys="updateCheckedKeys"
    @update:indeterminate-keys="updateIndeterminateKeys"
  />
</template>

<style scoped></style>
