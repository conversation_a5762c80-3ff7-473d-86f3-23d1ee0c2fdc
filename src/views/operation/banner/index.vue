<script setup lang="tsx">
import { ref } from 'vue';
import { N<PERSON><PERSON>on, NPopconfirm, NTag } from 'naive-ui';
import { fetchBoothList, deleteBooth, updateBooth } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useBoolean } from '@sa/hooks';
import { $t } from '@/locales';
import { displayLabel } from '@/constants/business';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import BannerOperateDrawer, { type OperateType } from './modules/banner-operate-drawer.vue';
import ImageSingle from '@/components/common/image/image-single.vue';
import OperateSort from '@/components/common/table/operate-sort.vue';

const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();

const tab = ref<Api.System.SettingBoothType>(0);

const typeLabel = ['幻灯片', '金刚区'];

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.System.SettingBooth, typeof fetchBoothList, 'index' | 'operate'>({
  apiFn: fetchBoothList,
  apiParams: {
    current: 1,
    size: 20,
    type: tab.value,
    enable: null,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'name',
      title: '名称',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'image_url',
      title: '图片',
      align: 'center',
      minWidth: 80,
      render: row => {
        return <ImageSingle src={row.image_url} width={100} />
      }
    },
    {
      key: 'url',
      title: '链接',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'sort',
      title: '排序',
      align: 'center',
      minWidth: 100,
      render: row => {
        return <OperateSort id={row.id} sort={row.sort} updateSort={updateBooth} onReset={getData} />
      }
    },
    {
      key: 'enable',
      title: '是否显示',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        const label = displayLabel[row.enable];

        return <NTag type={tagMap[row.enable]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 120,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      minWidth: 120,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      minWidth: 160,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const operateType = ref<OperateType>('add');

/** the editing row data */
const editingData = ref<Api.System.SettingBooth | null>(null);

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;
  openDrawer();
}

const checkedRowKeys = ref<string[]>([]);

function handleEdit(row: Api.System.SettingBooth) {
  operateType.value = 'edit';
  editingData.value = row;
  openDrawer();
}

async function handleDelete(row: Api.System.SettingBooth) {
  const { error } = await deleteBooth(row.id);
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}

function handleTabValue(value: Api.System.SettingBoothType) {
  tab.value = value;

  updateSearchParams({
    type: tab.value
  });

  getData();
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="幻灯片列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NTabs type="line" animated @update:value="handleTabValue">
        <NTabPane :name="0" :tab="typeLabel[0]" />
        <NTabPane :name="1" :tab="typeLabel[1]" />
      </NTabs>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
      <BannerOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :booth-type="tab"
        :booth-type-label="typeLabel"
        :row-data="editingData"
        @submitted="getData"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
