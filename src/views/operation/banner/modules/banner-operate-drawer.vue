<script setup lang="tsx">
import { computed, reactive, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { createBooth, updateBooth } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

defineOptions({
  name: 'BannerOperateDrawer'
});

/**
 * the type of operation
 *
 * - add: add user
 * - edit: edit user
 */
export type OperateType = 'add' | 'edit';

const { loading, startLoading, endLoading } = useLoading();

interface Props {
  operateType: OperateType;
  boothType: Api.System.SettingBoothType;
  boothTypeLabel: string[];
  rowData?: Api.System.SettingBooth | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: `新增${props.boothTypeLabel[props.boothType]}`,
    edit: `编辑${props.boothTypeLabel[props.boothType]}`
  };
  return titles[props.operateType];
});

type Model = Pick<Api.System.SettingBooth, 'name' | 'type' | 'image' | 'url' | 'enable' | 'sort'>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    type: props.boothType,
    name: '',
    url: '',
    image: '',
    enable: 1,
    sort: 0
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'image'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  image: defaultRequiredRule
};

const formConfig = [
  {
    title: '幻灯标题',
    image_tips: ['建议尺寸：900*383'],
    url_tips: ['1、内容链接，可在对应内容列表中复制粘贴链接', '2、其他链接咨询开发人员'],
  },
  {
    title: '文字',
    image_tips: ['建议宽高比1:1，分辨率不低于120*120'],
    url_tips: ['1、内容链接，可在对应内容列表中复制粘贴链接', '2、其他链接咨询开发人员'],
  }
];


async function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, {
      type: props.rowData.type,
      name: props.rowData.name,
      url: props.rowData.url,
      image: props.rowData.image,
      enable: props.rowData.enable,
      sort: props.rowData.sort,
    });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  if (props.operateType === 'add') {
    const { error } = await createBooth(model);
    endLoading();
    if(error) {
      return;
    }
    window.$message?.success($t('common.addSuccess'));
  } else {
    const { error } = await updateBooth(props.rowData?.id as number, model);
    endLoading();
    if(error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="480">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem :label="formConfig[boothType].title" path="name" class="w-300px">
          <NInput v-model:value="model.name" />
        </NFormItem>
        <NFormItem label="图片" path="image">
          <NSpace vertical>
            <UploadImage v-model:value="model.image" :preview="rowData?.image_url" />
            <FormTips :tips="formConfig[boothType].image_tips" />
          </NSpace>
        </NFormItem>
        <NFormItem label="链接" path="url" class="w-400px">
          <NSpace vertical>
            <NInput v-model:value="model.url" />
            <FormTips :tips="formConfig[boothType].url_tips" />
          </NSpace>
        </NFormItem>
        <NFormItem label="排序" path="sort" class="w-200px">
          <NSpace vertical>
            <NInputNumber v-model:value="model.sort" :min="0" :max="10000" />
            <FormTips :tips="['数字越大越靠前']" />
          </NSpace>
        </NFormItem>
        <NFormItem label="是否显示" path="enable">
          <NRadioGroup v-model:value="model.enable">
            <NRadio :value="1" label="显示" />
            <NRadio :value="0" label="隐藏" />
          </NRadioGroup>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
