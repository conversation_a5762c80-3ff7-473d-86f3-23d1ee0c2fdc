<script setup lang="tsx">
import { NTag, NText } from 'naive-ui';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { fetchSmsRecords } from '@/service/api';
import { businessTypeLabel } from '@/constants/business';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnUser from '@/components/common/table/column-user.vue';

const { route } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.User.CreditLog, typeof fetchSmsRecords, 'index' | 'operate'>({
  apiFn: fetchSmsRecords,
  apiParams: {
    current: 1,
    size: 20,
    phone: route.value.query.phone ? route.value.query.phone : null
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'created_at',
      title: '发送时间',
      render: row => {
        return <DateFormat date={row.created_at} />;
      },
      align: 'center',
      width: 100
    },
    {
      key: 'phone',
      title: '手机号码',
      align: 'center',
      width: 150
    },
    {
      key: 'content',
      title: '内容',
      align: 'center',
      width: 120
    },
    {
      key: 'type',
      title: ' 类型',
      align: 'center',
      width: 120
    }
  ]
});

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    user_id: null
  });
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="短信发送记录（只显示最近24小时）" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :show-add="false"
          :loading="loading"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
