<script setup lang="ts">
import { useLoading } from '@sa/hooks';
import { getServiceBaseURL } from "@/utils/service";
import { localStg } from '@/utils/storage';
import { ref } from 'vue';
import { useDialog } from 'naive-ui';

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';
const { baseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

const token = localStg.get('token');
const Authorization = token ? `Bearer ${token}` : null;
const uploadRef = ref(null);

defineOptions({
  name: 'CourseImport'
});

interface Emits {
  (e: 'reset'): void;
}

const emit = defineEmits<Emits>();

const { loading, startLoading, endLoading } = useLoading();

const acceptTypes = '.xlsx,.xls';

const uploadUrl = `${baseURL}/user/openCourseRecords/import`;

const dialog = useDialog();

function beforeUpload() {
  return new Promise((resolve) => {
    dialog.warning({
      content: `导入后无法撤销，请再次检查您的表格字段是否准确无误，确认要导入吗？`,
      positiveText: '确认导入',
      negativeText: '取消',
      onPositiveClick: () => {
        startLoading();
        resolve(true)
      },
      onNegativeClick: () => {
        resolve(false);
      }
    });
  });
}

function handleFinish() {
  uploadRef.value.clear();
  endLoading();
  window.$message?.success('导入成功');

  emit('reset');
}

function handelError(e) {
  let response = JSON.parse(e.event.currentTarget.response);
  uploadRef.value.clear();
  endLoading();
  window.$message?.error('导入失败，原因：' + response.message);
}

</script>

<template>

  <NUpload
    ref="uploadRef"
    :action="uploadUrl"
    :headers="{
      'Authorization': Authorization
    }"
    :accept="acceptTypes"
    :max="1"
    :show-file-list="false"
    @finish="handleFinish"
    @error="handelError"
    @before-upload="beforeUpload"
  >
    <NButton type="primary" size="large" :loading="loading">
      {{ loading ? '导入中' : '导入' }}
    </NButton>
  </NUpload>
</template>

<style scoped></style>
