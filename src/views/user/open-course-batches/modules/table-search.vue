<script setup lang="ts">
import { $t } from '@/locales';
import TableImport from "../modules/table-import.vue";
import { fetchOpenCourseRecordTemplate } from "@/service/api";


defineOptions({
  name: 'AdminSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type AdminSearchParams = CommonType.RecordNullable<
  Pick<Api.User.OpenCourseRecord, 'user_id'> &
    Api.SystemManage.CommonSearchParams & {
      id: string;
    }
>;

const emit = defineEmits<Emits>();

const model = defineModel<AdminSearchParams>('model', { required: true });

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}

async function downloadTemplate() {
  const {data} = await fetchOpenCourseRecordTemplate();

  window.location.href = data;
}
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:7" label="创建时间" path="created_at" class="pr-24px">
          <DatetimeRange v-model:value="model.created_at"  />
        </NFormItemGi>
        <NFormItemGi span="24 s:4">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
        <NFormItemGi span="24 s:4">
          <NSpace class="w-full" justify="end">
            <TableImport @reset="search" />
            <NButton type="primary" size="large" @click="downloadTemplate">下载模板</NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
