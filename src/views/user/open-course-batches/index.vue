<script setup lang="tsx">
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { fetchOpenCourseBatchList } from '@/service/api';
import DateFormat from '@/components/common/date/date-format.vue';
import TableSearch from './modules/table-search.vue';
import {$t} from "@/locales";
import {NButton} from "naive-ui";

const { routerPushByKey } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.User.OpenCourseBatch, typeof fetchOpenCourseBatchList, 'index' | 'operate'>({
  apiFn: fetchOpenCourseBatchList,
  apiParams: {
    current: 1,
    size: 20,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'count',
      title: '开通人数',
      align: 'center',
      width: 150,
    },
    {
      key: 'admin_id',
      title: '操作人',
      align: 'center',
      width: 150,
      render: row => {
        return row.admin ? row.admin.username : '-';
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 100,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleShow(row.id)}>
            查看
          </NButton>
        </div>
      )
    }
  ]
});

function handleShow(id: number) {
  routerPushByKey('user_open-course-records', { query: { batch_id: id } })
}

function handleReset() {
  resetSearchParams();
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="开课记录" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :show-add="false"
          :loading="loading"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
