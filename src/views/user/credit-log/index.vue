<script setup lang="tsx">
import { NTag, NText } from 'naive-ui';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { fetchUserCreditLogList } from '@/service/api';
import { businessTypeLabel } from '@/constants/business';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnUser from '@/components/common/table/column-user.vue';

const { route } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.User.CreditLog, typeof fetchUserCreditLogList, 'index' | 'operate'>({
  apiFn: fetchUserCreditLogList,
  apiParams: {
    current: 1,
    size: 20,
    user_id: route.value.query.user_id ? route.value.query.user_id : null,
    type: null,
    business_type: null,
    business_id: null,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 100
    },
    {
      key: 'user',
      title: '用户',
      align: 'center',
      width: 150,
      render: row => {
        return <ColumnUser user_id={row.user_id} user={row.user} />
      }
    },
    {
      key: 'origin_credit',
      title: '原有积分',
      align: 'center',
      width: 120
    },
    {
      key: 'change_credit',
      title: '预扣积分',
      align: 'center',
      width: 120,
      render: row => {
        return <NText type={row.type === 'recharge' ? 'error' : 'success'}>{row.change_credit}</NText>
      }
    },
    {
      key: 'real_change_credit',
      title: '实扣积分',
      align: 'center',
      width: 120,
      render: row => {
        return <NText type={row.type === 'recharge' ? 'error' : 'success'}>{row.real_change_credit}</NText>
      }
    },
    {
      key: 'business_type',
      title: '业务类型',
      align: 'center',
      width: 120,
      render: row => {
        if (businessTypeLabel.hasOwnProperty(row.business_type)) {
          return <NTag type="info" bordered={false}>{businessTypeLabel[row.business_type]}</NTag>
        } else {
          return row.business_type;
        }
      }
    },
    {
      key: 'business_id',
      title: '业务ID',
      align: 'center',
      width: 100
    },
    {
      key: 'remark',
      title: '备注',
      align: 'left',
      width: 200
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    }
  ]
});

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    user_id: null
  });
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="流水记录" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :show-add="false"
          :loading="loading"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
