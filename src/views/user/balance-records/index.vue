<script setup lang="tsx">
import { NText } from 'naive-ui';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { fetchUserBalanceRecordList } from '@/service/api';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnUser from '@/components/common/table/column-user.vue';

const { route } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.User.BalanceRecord, typeof fetchUserBalanceRecordList, 'index' | 'operate'>({
  apiFn: fetchUserBalanceRecordList,
  apiParams: {
    current: 1,
    size: 20,
    user_id: route.value.query.user_id ? route.value.query.user_id : null,
    type: null,
    business_type: null,
    business_id: null,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center'
    },
    {
      key: 'user',
      title: '用户',
      align: 'center',
      render: row => {
        return <ColumnUser user_id={row.user_id} user={row.user} />
      }
    },
    {
      key: 'origin_balance',
      title: '原有金额',
      align: 'center',
    },
    {
      key: 'amount',
      title: '变更金额',
      align: 'center',
      render: row => {
        return <NText type={row.type === 'income' ? 'error' : 'success'}>{row.amount}</NText>
      }
    },
    {
      key: 'remark',
      title: '备注',
      align: 'center',
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    }
  ]
});

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    user_id: null
  });
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="流水记录" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :show-add="false"
          :loading="loading"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
