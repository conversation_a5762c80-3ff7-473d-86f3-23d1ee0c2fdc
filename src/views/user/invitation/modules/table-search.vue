<script setup lang="ts">
import { $t } from '@/locales';
import { invitationStatusOptions } from '@/constants/business';

defineOptions({
  name: 'AdminSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type AdminSearchParams = CommonType.RecordNullable<Pick<Api.User.Invitation, 'status'> & Api.SystemManage.CommonSearchParams & {
  id: string;
  referral_id: string;
  invitee_id: string;
}>;

const emit = defineEmits<Emits>();

const model = defineModel<AdminSearchParams>('model', { required: true });

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:3" label="ID" path="id" class="pr-24px">
          <NInput v-model:value="model.id" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="推荐人" path="referral_id" class="pr-24px">
          <SearchUser v-model:value="model.referral_id" placeholder="搜索推荐人" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="受邀人" path="invitee_id" class="pr-24px">
          <SearchUser v-model:value="model.invitee_id" placeholder="搜索受邀人" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="状态" path="status" class="pr-24px">
          <NSelect v-model:value="model.status" :options="invitationStatusOptions" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:7" label="创建时间" path="created_at" class="pr-24px">
          <DatetimeRange v-model:value="model.created_at"  />
        </NFormItemGi>
        <NFormItemGi span="24 s:5">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
