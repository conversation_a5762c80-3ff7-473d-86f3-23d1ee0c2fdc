<script setup lang="tsx">
import { NTag } from 'naive-ui';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { invitationStatusLabel } from '@/constants/business'
import { fetchInvitationList } from '@/service/api';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnUser from '@/components/common/table/column-user.vue';
import TableSearch from './modules/table-search.vue';

const { route } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.User.Invitation, typeof fetchInvitationList, 'index' | 'operate'>({
  apiFn: fetchInvitationList,
  apiParams: {
    current: 1,
    size: 20,
    id: null,
    status: null,
    referral_id: route.value.query.user_id ? route.value.query.user_id : null,
    invitee_id: null,
    created_at: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 100
    },
    {
      key: 'referral_id',
      title: '推荐人ID',
      align: 'center',
      width: 100
    },
    {
      key: 'referral',
      title: '推荐人',
      align: 'center',
      width: 120,
      render: row => {
        return <ColumnUser user_id={row.referral_id} user={row.referral} />
      }
    },
    {
      key: 'invitee_id',
      title: '受邀人ID',
      align: 'center',
      width: 100
    },
    {
      key: 'invitee',
      title: '受邀人',
      align: 'center',
      width: 120,
      render: row => {
        return <ColumnUser user_id={row.invitee_id} user={row.invitee} />
      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          2: 'success',
          1: 'warning'
        };

        const label = invitationStatusLabel[row.status];

        return <NTag type={tagMap[row.status]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'credit',
      title: '积分数量',
      align: 'center',
      width: 120
    },
    {
      key: 'send_at',
      title: '奖励时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.send_at} />;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    }
  ]
});

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    referral_id: null
  });
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="邀请记录" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :show-add="false"
          :loading="loading"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
