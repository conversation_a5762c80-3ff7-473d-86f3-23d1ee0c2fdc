<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { NTag, NPopconfirm, NButton } from 'naive-ui';
import { fetchUserOwnContentList, removeUserOwnContent } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { ownContentClassifyLabel, ownContentStatusLabel } from "@/constants/business";
import DateFormat from '@/components/common/date/date-format.vue';
import TableSearch from './table-search-course.vue';

interface Props {
  detail: Api.User.User;
}

const props = defineProps<Props>();

const {
  columns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams
} = useTable<Api.User.OwnContent, typeof fetchUserOwnContentList, 'index' | 'operate'>({
  apiFn: fetchUserOwnContentList,
  apiParams: {
    current: 1,
    size: 10,
    user_id: null,
    id: null,
    keyword: null,
    classify: null
  },
  immediate: false,
  transformer: res => {
    const { records = [], current = 1, size = 10, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row) => row.id,
      align: 'center'
    },
    {
      key: 'content_id',
      title: '关联ID',
      align: 'center'
    },
    {
      key: 'content',
      title: '名称',
      align: 'center',
      render: (row) => row.content?.title || '-'
    },
    {
      key: 'classify',
      title: '类型',
      align: 'center',
      render: (row) => {
        const tagMap: Record<string, NaiveUI.ThemeColor> = {
          course: 'info',
          course_pack: 'success',
          material: 'warning',
        };

        const label = ownContentClassifyLabel[row.classify];

        return <NTag type={tagMap[row.classify]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: (row) => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'error',
          1: 'success',
          2: 'default',
        };

        const label = ownContentStatusLabel[row.status];

        return <NTag type={tagMap[row.status]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'created_at',
      title: '开通时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'expired_at',
      title: '有效期至',
      align: 'center',
      render: row => {
        return <DateFormat date={row.expired_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      render: (row) => {
        if (row.status == 1) {
          return (
            <NPopconfirm onPositiveClick={() => handleRemove(row.id)}>
              {{
                default: () => '确定要删除吗？',
                trigger: () => (
                  <NButton size={'small'} type={'error'}>
                    删除
                  </NButton>
                )
              }}
            </NPopconfirm>
          );
        }
      }
    }
  ]
});

const removeLoading = ref<boolean>(false);

async function handleRemove(id: number) {
  if (removeLoading.value) {
    return;
  }

  removeLoading.value = true;

  await removeUserOwnContent(id);

  removeLoading.value = false;

  window.$message?.success('操作成功')

  await getData();
}

function handleReset() {
  updateSearchParams({
    user_id: props.detail.id,
    id: null,
    keyword: null,
    classify: null,
  });

  getData();
}

onMounted( () => {
  updateSearchParams({
    user_id: props.detail.id,
  });

  getData();
})
</script>

<template>
  <div class="min-h-500px">
    <NCard size="small" class="sm:flex-1-hidden card-wrapper">
      <NSpace vertical>
        <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />


        <NDataTable
          :columns="columns"
          :data="data"
          size="small"
          :scroll-x="702"
          :loading="loading"
          remote
          :pagination="pagination"
          :row-key="item => item.id"
          class="sm:h-full"
        />
      </NSpace>
    </NCard>
  </div>
</template>

<style scoped></style>
