<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { getUser } from '@/service/api';
import { closeTab, pageError } from '@/utils/tab';
import { useRouterPush } from "@/hooks/common/router";
import { userStatusLabel } from "@/constants/business";
import TableListCourse from "./modules/table-list-course.vue";
import TableListTopic from "./modules/table-list-topic.vue";

const { route } = useRouterPush();

const detail = ref<Api.User.User | null>(null);

const tagMap: Record<number, NaiveUI.ThemeColor> = {
  1: 'success',
  2: 'warning',
  3: 'error',
  4: 'error',
};

async function init(id: number) {
  const { error, data } = await getUser(id);

  if (error) {
    await closeTab('user_detail');
    return;
  }

  detail.value = data;
}

onMounted(async () => {
  if (route.value.query?.user_id) {
    await init(parseInt(route.value.query.user_id as string));
    return;
  }


  await pageError('user_detail');
})
</script>

<template>
  <div class="gap-16px">
    <NCard v-if="detail" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <div class="flex flex-row detail-box">
          <div class="text-20px font-bold">用户信息</div>
          <div class="pl-1">
            <NTag :type="tagMap[detail.status]" :bordered="false">{{ userStatusLabel[detail.status] }}</NTag>
          </div>
        </div>
      </template>

      <NSpace vertical>
        <NCard size="small">
          <n-descriptions label-placement="left" label-align="right">
            <n-descriptions-item label="id">
              {{ detail.id }}
            </n-descriptions-item>
            <n-descriptions-item label="所属机构">
              {{ detail.org_id > 0 ? detail.org?.name : '-' }}
            </n-descriptions-item>
            <n-descriptions-item label="最近登录时间">
              <DateFormat :date="detail.last_logged_at" />
            </n-descriptions-item>
            <n-descriptions-item label="昵称">
              {{ detail.nickname }}
            </n-descriptions-item>
            <n-descriptions-item label="余额">
              {{ detail.balance }}
            </n-descriptions-item>
            <n-descriptions-item label="最近活跃时间">
              <DateFormat :date="detail.last_active_at" />
            </n-descriptions-item>
            <n-descriptions-item label="手机号码">
              {{ detail.phone }}
            </n-descriptions-item>
            <n-descriptions-item label="积分">
              {{ detail.credit }}
            </n-descriptions-item>
            <n-descriptions-item label="用户注册时间">
              <DateFormat :date="detail.created_at" />
            </n-descriptions-item>
          </n-descriptions>
        </NCard>

        <NCard size="small">
          <NTabs type="line" animated>
            <NTabPane name="content" tab="我的课程">
              <TableListCourse :detail="detail" />
            </NTabPane>
            <NTabPane name="topic" tab="我的题库">
              <TableListTopic :detail="detail" />
            </NTabPane>
          </NTabs>
        </NCard>
      </NSpace>
    </NCard>
  </div>
</template>

<style scoped>
.detail-box {
  display: flex;
  flex-direction: row;
  line-height: 48px;
}

.class-list {
  display: flex;
  flex-direction: row;
}

.class-list-item {
  display: flex;
  flex-direction: column;
  padding-right: 52px;
  padding-top: 6px;
  min-width: 150px;
  line-height: 28px;
}

.class-info-title {
  font-size: 14px;
  font-weight: bold;
}

.class-info-desc {
  font-size: 18px;
  font-weight: bold;
}

.class-info-course {
  max-width: 320px;
}

.class-stats-count {
  font-size: 20px;
  font-weight: bold;
}

.class-stats-desc {
  font-size: 14px;
  font-weight: bold;
}
</style>
