<script setup lang="tsx">
import { NSpace, NTag } from 'naive-ui';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { fetchOpenCourseRecordList } from '@/service/api';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnUser from '@/components/common/table/column-user.vue';
import TableSearch from './modules/table-search.vue';

const { route } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.User.OpenCourseRecord, typeof fetchOpenCourseRecordList, 'index' | 'operate'>({
  apiFn: fetchOpenCourseRecordList,
  apiParams: {
    current: 1,
    user_id: route.value.query.user_id ? route.value.query.user_id : null,
    batch_id: route.value.query.batch_id ? route.value.query.batch_id : null,
    size: 20,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'batch_id',
      title: '批次ID',
      align: 'center',
      minWidth: 150
    },
    {
      key: 'user_id',
      title: '用户',
      align: 'center',
      width: 150,
      render: row => {
        return <ColumnUser user_id={row.user_id} user={row.user} />
      }
    },
    {
      key: 'phone',
      title: '电话',
      align: 'center',
      width: 150,
      render: (row) => row.user?.phone
    },
    {
      key: 'org_id',
      title: '所属机构',
      align: 'center',
      width: 150,
      render: (row) => row.org?.name
    },
    {
      key: 'courses',
      title: '开通课程',
      align: 'center',
      minWidth: 120,
      maxWidth: 240,
      render: row => (
        <NSpace justify="center">
          {row.courses?.map(item => {
            return (
              <NTag type="info" bordered={false}>
                {item.title}
              </NTag>
            );
          })}
        </NSpace>
      )
    },
    {
      key: 'topic_ids',
      title: '开通题库',
      align: 'center',
      minWidth: 120,
      maxWidth: 240,
      render: row => (
        <NSpace justify="center">
          {row.topics?.map(item => {
            return (
              <NTag type="info" bordered={false}>
                {item.name}
              </NTag>
            );
          })}
        </NSpace>
      )
    },
    {
      key: 'packs',
      title: '课程包',
      align: 'center',
      minWidth: 120,
      maxWidth: 240,
      render: row => (
        <NSpace justify="center">
          {row.packs?.map(item => {
            return (
              <NTag type="info" bordered={false}>
                {item.title}
              </NTag>
            );
          })}
        </NSpace>
      )
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    }
  ]
});

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    user_id: null
  });
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="开课记录" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :show-add="false"
          :loading="loading"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
