<script setup lang="tsx">
import { NAvatar, NTag } from 'naive-ui';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { userStatusLabel } from '@/constants/business';
import { fetchUserList } from '@/service/api';
import DateFormat from '@/components/common/date/date-format.vue';
import TableSearch from './modules/table-search.vue';
import TableActions from './modules/table-actions.vue';

const { route } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.User.User, typeof fetchUserList, 'index' | 'operate'>({
  apiFn: fetchUserList,
  apiParams: {
    current: 1,
    size: 20,
    id: route.value.query.user_id ? route.value.query.user_id : null,
    org_id: null,
    status: null,
    uuid: null,
    nickname: null,
    phone: null,
    created_at: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'avatar',
      title: '头像',
      align: 'center',
      width: 60,
      render: row => {
        return <NAvatar round src={row.avatar} />;
      }
    },
    {
      key: 'nickname',
      title: '昵称',
      align: 'center',
      minWidth: 150
    },
    {
      key: 'phone',
      title: '手机号码',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'uuid',
      title: 'UUID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'org_id',
      title: '所属机构',
      align: 'center',
      minWidth: 120,
      render: row => {
        if (!row.students || row.students.length === 0) return '-';
        return row.students.map(student => student.org?.name).join('、');
      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          1: 'success',
          2: 'warning',
          3: 'error',
          4: 'error'
        };

        const label = userStatusLabel[row.status];

        return (
          <NTag type={tagMap[row.status]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'credit',
      title: '积分数量',
      align: 'center',
      width: 100
    },
    {
      key: 'balance',
      title: '余额',
      align: 'center',
      width: 100
    },
    {
      key: 'last_logged_at',
      title: '最近登录时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.last_logged_at} />;
      }
    },
    {
      key: 'last_active_at',
      title: '最近活跃时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.last_active_at} />;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 150,
      render: row => {
        return <TableActions user={row} onReload={getData} />;
      }
    }
  ]
});

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    id: null
  });
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="用户列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :show-add="false"
          :loading="loading"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
