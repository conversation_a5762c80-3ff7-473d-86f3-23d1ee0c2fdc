<template>
  <NForm :model="form" label-placement="left" :label-width="100">
    <n-form-item label="用户ID" path="inputValue">
      <n-input
        :value="props?.user?.id.toString()"
        placeholder="用户ID"
        disabled
      />
    </n-form-item>
    <n-form-item label="手机号码" path="inputValue">
      <n-input :value="props?.user?.phone" placeholder="手机号码" disabled />
    </n-form-item>
    <n-form-item label="类型" path="inputSelect">
      <n-select :options="positions" v-model:value="amountType" />
    </n-form-item>
    <n-form-item label="金额(1：10)" path="amount">
      <n-flex vertical>
        <n-input-number v-model:value="amount" placeholder="请输入金额" />
        <n-flex v-if="amount > 0">合计：{{ amount * 10 }}积分</n-flex>
      </n-flex>
    </n-form-item>
    <n-form-item label="计入统计" path="inputCheckbox">
      <n-checkbox v-model:checked="isStatistics"> </n-checkbox>
    </n-form-item>
  </NForm>
  <NSpace :size="16" justify="end">
    <NButton @click="submitClose">取消</NButton>
    <NButton type="primary" @click="submitForm">确认</NButton>
  </NSpace>
</template>
<script setup lang="tsx">
import { ref } from "vue";
import { updateUserCredit } from '@/service/api';

interface Props {
  user: Api.User.User;
  showModal: boolean;
}
const form = ref({
  amount: null,
});

interface Emits {
  (e: 'reload'): void;
  (e: 'update:modalState', visible: boolean): void;
}
const emit = defineEmits<Emits>();

const props = defineProps<Props>();
const amountType = ref(1);
const amount = ref(0);
const isStatistics = ref(true);
const positions = [
  { label: "增加积分", value: 1 },
  { label: "扣除积分", value: 2 },
];
// 提交表单
const submitForm = async () => {
  let data = {
    amount: amount.value,
    amountType: amountType.value,
    isStatistics: isStatistics.value,
  }
  const error = await updateUserCredit(props.user.id, data);
  console.log(error)
  if (error?.error) {
    return;
  }
  window.$message?.success('修改成功');
  emit("update:modalState", false);
  emit('reload');
};
const submitClose = async () => {
  emit("update:modalState", false); // 触发事件并传递新状态
};
</script>

<style scoped></style>
