<script setup lang="tsx">
import type { DropdownMixedOption } from 'naive-ui/es/dropdown/src/interface';
import type { Component, VNodeChild } from 'vue';
import { h, ref } from 'vue';
import { NIcon } from 'naive-ui';
import {
  CheckmarkCircleOutline as EnableIcon,
  HandRightOutline as ForbidIcon,
  MicOffOutline as MuteIcon,
  MicOutline as UnmuteIcon
} from '@vicons/ionicons5';
import { Product as ProductIcon, UserProfileAlt } from '@vicons/carbon';
import { CreditCardFilled as CreditCardIcon, EditOutlined } from '@vicons/antd'
import { AccountBalanceWalletOutlined, InsertInvitationFilled } from '@vicons/material'
import { useRouterPush } from '@/hooks/common/router';
import { updateUser } from '@/service/api';
import { useBoolean } from "@sa/hooks";
import { closeTab } from "@/utils/tab";
import { UserStatus } from '@/enum/index.js';
import UserEditCredit from "./user-edit-credit.vue";
import UserEditBalance from "./user-edit-balance.vue";

interface Props {
  user: Api.User.User;
}

interface Emits {
  (e: 'reload'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { bool: balanceVisible, setTrue: openBalanceModal } = useBoolean();

const { routerPushByKey } = useRouterPush();

const options = ref<DropdownMixedOption[]>(init(props.user.status));

const showModal = defineModel<boolean>('showModal', {
  default: false
});

function renderIcon(icon: Component): () => VNodeChild {
  return () => {
    return h(NIcon, null, {
      default: () => h(icon)
    });
  };
}

function init(status: number): DropdownMixedOption[] {
  const optionList: DropdownMixedOption[] = [
    // {
    //   label: '绑定记录',
    //   key: 'binds'
    // },
    {
      label: '查看详情',
      key: 'detail',
      icon: renderIcon(UserProfileAlt),
    },
    {
      label: '订单列表',
      key: 'order',
      icon: renderIcon(ProductIcon),
    },
    {
      label: '积分明细',
      key: 'credit-logs',
      icon: renderIcon(CreditCardIcon)
    },
    {
      label: '修改积分',
      key: 'credit-edit',
      icon: renderIcon(EditOutlined)
    },
    {
      label: '余额明细',
      key: 'balance-records',
      icon: renderIcon(AccountBalanceWalletOutlined)
    },
    {
      label: '修改余额',
      key: 'credit-balance',
      icon: renderIcon(EditOutlined)
    },
    {
      label: '邀请记录',
      key: 'invitations',
      icon: renderIcon(InsertInvitationFilled)
    }
  ];

  const optionalOptions = [
    {
      label: '封禁',
      key: 'status_disabled',
      icon: renderIcon(ForbidIcon),
      availableStatuses: [UserStatus.Normal, UserStatus.Mute]
    },
    {
      label: '禁止发言',
      key: 'status_mute',
      icon: renderIcon(MuteIcon),
      availableStatuses: [UserStatus.Normal]
    },
    {
      label: '启用',
      key: 'status_enabled',
      icon: renderIcon(EnableIcon),
      availableStatuses: [UserStatus.Forbid]
    },
    {
      label: '允许发言',
      key: 'status_enabled',
      icon: renderIcon(UnmuteIcon),
      availableStatuses: [UserStatus.Mute]
    }
  ];

  optionalOptions.forEach(row => {
    if (row.availableStatuses.includes(status)) {
      optionList.push({
        label: row.label,
        key: row.key,
        icon: row.icon
      });
    }
  });

  return optionList;
}

function handleSelect(key: string) {
  // eslint-disable-next-line default-case
  switch (key) {
    case 'detail':
      closeTab('user_detail')
      routerPushByKey('user_detail', { query: { user_id: props.user.id.toString() }})
      break;
    case 'order':
      closeTab('order_index')
      routerPushByKey('order_index', { query: { user_id: props.user.id.toString() }})
      break;
    case 'balance-records':
      closeTab('user_balance-records')
      routerPushByKey('user_balance-records', { query: { user_id: props.user.id.toString() }})
      break;
    case 'credit-logs':
      closeTab('user_credit-log')
      routerPushByKey('user_credit-log', { query: { user_id: props.user.id.toString() }})
      break;
    case 'invitations':
      closeTab('user_invitation')
      routerPushByKey('user_invitation', { query: { user_id: props.user.id.toString() }})
      break;
    case 'status_disabled':
      updateStatus(0);
      break;
    case 'status_mute':
      updateStatus(2);
      break;
    case 'status_enabled':
      updateStatus(1);
      break;
    case 'credit-edit':
      showModal.value = true;
      break;
    case 'credit-balance':
      openBalanceModal();
      break;
  }
}
const updateModalState = (visible: boolean) => {
  showModal.value = visible;
};
const editReload = () => {
  emit('reload');
}
function updateStatus(status: number) {
  window.$dialog?.warning({
    title: '提示',
    content: '您确定要执行该操作吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
        const { error } = await updateUser(props.user.id, { status: status });
        if (error) {
          return;
        }

      options.value = init(status);

      window.$message?.success('修改成功');

      editReload();
    }
  });
}
</script>

<template>
  <NDropdown trigger="click" :options="options" @select="handleSelect">
    <NButton type="primary">
      更多操作
      <svg-icon icon="mingcute:down-fill" class="ml-5px" />
    </NButton>
  </NDropdown>
  <n-modal v-model:show="showModal">
    <n-card
      style="width: 600px"
      title="修改积分"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <UserEditCredit :user="user" v-model:showModal="showModal" @update:modalState="updateModalState" @reload="editReload" />
    </n-card>
  </n-modal>
  <UserEditBalance v-model:visible="balanceVisible" :user="user" @reload="editReload" />
</template>

<style scoped></style>
