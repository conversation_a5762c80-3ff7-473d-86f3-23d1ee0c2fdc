<script setup lang="tsx">
import { ref, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { updateUserBalance } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = {
  type: 'add' | 'sub';
  amount: number;
  remark: string;
};

interface Props {
  user: Api.User.User;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const model = ref<Model>({
  type: 'add',
  amount: 1,
  remark: ''
});

const typeOptions = [
  {
    value: 'add',
    label: '增加'
  },
  {
    value: 'sub',
    label: '减少'
  },
];

const rules: Record<string, App.Global.FormRule> = {
  type: defaultRequiredRule,
  amount: defaultRequiredRule,
  remark: defaultRequiredRule,
};

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  const { error } = await updateUserBalance(props.user.id, model.value);

  endLoading();

  if(error) {
    return;
  }

  window.$message?.success('操作成功');

  closeDrawer();
  emit('reload');
}

watch(visible, () => {
  if (visible.value) {
    restoreValidation();
  }
});
</script>

<template>
  <n-modal v-model:show="visible">
    <n-card
      style="width: 600px"
      title="修改余额"
      :bordered="false"
    >
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem label="用户" class="w-260px">
          <NText>{{ user.id }}</NText>
        </NFormItem>
        <NFormItem label="手机号码" class="w-260px">
          <NText>{{ user.phone }}</NText>
        </NFormItem>
        <NFormItem label="选择类型" path="type" class="w-260px">
          <NSelect v-model:value="model.type" :options="typeOptions" clearable />
        </NFormItem>
        <NFormItem label="金额" path="amount" class="w-260px">
          <NInputNumber v-model:value="model.amount" placeholder="请输入金额">
            <template #suffix>元</template>
          </NInputNumber>
        </NFormItem>
        <NFormItem label="备注" path="remark" class="w-420px">
          <NInput v-model:value="model.remark" placeholder="请输入备注信息" />
        </NFormItem>

      </NForm>
      <template #footer>
        <NSpace :size="16" justify="end">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit" :disabled="loading">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
