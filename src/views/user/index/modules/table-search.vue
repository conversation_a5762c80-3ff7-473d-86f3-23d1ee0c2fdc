<script setup lang="ts">
import { $t } from '@/locales';
import { userStatusOptions } from '@/constants/business';

defineOptions({
  name: 'AdminSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type AdminSearchParams = CommonType.RecordNullable<
  Pick<Api.User.User, 'status' | 'nickname' | 'uuid' | 'phone' | 'org_id'> &
    Api.SystemManage.CommonSearchParams & {
      id: string;
    }
>;

const emit = defineEmits<Emits>();

const model = defineModel<AdminSearchParams>('model', { required: true });

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:3" label="ID" path="id" class="pr-24px">
          <NInput v-model:value="model.id" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="UUID" path="uuid" class="pr-24px">
          <NInput v-model:value="model.uuid" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="手机号码" path="phone" class="pr-24px">
          <NInput v-model:value="model.phone" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="昵称" path="nickname" class="pr-24px">
          <NInput v-model:value="model.nickname" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="所属机构" path="org_id" class="pr-24px">
          <SearchOrg v-model:value="model.org_id" clearable></SearchOrg>
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="状态" path="status" class="pr-24px">
          <NSelect v-model:value="model.status" :options="userStatusOptions" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:7" label="创建时间" path="created_at" class="pr-24px">
          <DatetimeRange v-model:value="model.created_at"  />
        </NFormItemGi>
        <NFormItemGi span="24 s:4">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
