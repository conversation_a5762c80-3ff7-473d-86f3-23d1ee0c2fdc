<script setup lang="tsx">
import { NTag } from 'naive-ui';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { fetchOrderList } from '@/service/api';
import { orderBusinessTypeLabel, orderStatusLabel } from '@/constants/business';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnUser from '@/components/common/table/column-user.vue';

const { route } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Order.Order, typeof fetchOrderList, 'index' | 'operate'>({
  apiFn: fetchOrderList,
  apiParams: {
    current: 1,
    size: 20,
    id: null,
    user_id: route.value.query.user_id ? route.value.query.user_id : null,
    order_no: null,
    business_type: null,
    business_id: null,
    transaction_no: null,
    status: '1',
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'user_id',
      title: '用户',
      align: 'center',
      width: 150,
      render: row => {
        return <ColumnUser user_id={row.user_id} user={row.user} />
      }
    },

    {
      key: 'order_no',
      title: '订单号',
      align: 'center',
      minWidth: 120,
    },
    {
      key: 'payment.transaction_no' as any,
      title: '交易流水号',
      align: 'center',
      minWidth: 120,
      render: row => {
        return row.payment ? row.payment.transaction_no : '-';
      }
    },
    {
      key: 'payment_amount',
      title: '支付金额',
      align: 'center',
      width: 100,
    },
    {
      key: 'payment.platform' as any,
      title: '支付渠道',
      align: 'center',
      width: 100,
      render: row => {
        if (row.payment) {
          return row.payment.platform === 'wechat' ? '微信' : '支付宝' ;
        } else {
          return '-';
        }
      }
    },
    {
      key: 'business_type',
      title: '业务类型',
      align: 'center',
      width: 100,
      render: row => {
        if (orderBusinessTypeLabel.hasOwnProperty(row.business_type)) {
          return orderBusinessTypeLabel[row.business_type];
        } else {
          return row.business_type;
        }
      }
    },
    {
      key: 'business_id',
      title: '业务ID',
      align: 'center',
      width: 100
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'info',
          1: 'success',
          2: 'error',
          3: 'error',
          4: 'error',
        };

        const label = orderStatusLabel[row.status];

        return <NTag type={tagMap[row.status]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'payment_at',
      title: '支付时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.payment_at} />;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    }
  ]
});

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    user_id: null,
    status: null
  });
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="订单列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :show-add="false"
          :loading="loading"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
