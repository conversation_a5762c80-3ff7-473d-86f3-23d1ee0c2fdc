<script setup lang="ts">
import { $t } from '@/locales';
import { orderBusinessTypeOptions, orderStatusOptions } from '@/constants/business';

defineOptions({
  name: 'AdminSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type AdminSearchParams = CommonType.RecordNullable<Pick<Api.Order.Order, 'order_no' | 'business_type' | 'status'> & Api.SystemManage.CommonSearchParams & {
  id: string;
  user_id: string;
  business_id: string;
  transaction_no: string;
}>;

const emit = defineEmits<Emits>();

const model = defineModel<AdminSearchParams>('model', { required: true });

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:3" label="用户" path="user_id" class="pr-24px">
          <SearchUser v-model:value="model.user_id" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="订单ID" path="id" class="pr-24px">
          <NInput v-model:value="model.id" clearable placeholder="请输入订单ID" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:5" label="订单号" path="order_no" class="pr-24px">
          <NInput v-model:value="model.order_no" clearable placeholder="请输入订单号" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:5" label="交易流水号" path="transaction_no" class="pr-24px">
          <NInput v-model:value="model.transaction_no" clearable placeholder="请输入交易流水号" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:7" label="创建时间" path="created_at" class="pr-24px">
          <DatetimeRange v-model:value="model.created_at" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="状态" path="status" class="pr-24px">
          <NSelect v-model:value="model.status" :options="orderStatusOptions" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="业务类型" path="business_type" class="pr-24px">
          <NSelect v-model:value="model.business_type" :options="orderBusinessTypeOptions" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="业务ID" path="business_id" class="pr-24px">
          <NInput v-model:value="model.business_id" :disabled="!model.business_type" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:4">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
