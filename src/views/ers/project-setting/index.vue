<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { $t } from "@/locales";
import {NButton, NPopconfirm} from 'naive-ui';
import { useLoading, useBoolean } from "@sa/hooks";
import { FormTypeLabel } from "@/constants/business";
import { useRouterPush } from "@/hooks/common/router";
import { closeTab } from '@/utils/tab';
import {
  searchErsIndustry,
  searchErsEnterprise,
  getErsProject,
  getErsProjectForm,
  updateErsProjectInput,
  deleteErsProjectInput
} from '@/service/api';
import OperateSort from '@/components/common/table/operate-sort.vue';
import ProjectBase from './modules/project-base.vue';
import FormInputOperate, { type OperateType } from "./modules/form-input-operate.vue";

const { loading: tableLoading, startLoading: startTableLoading, endLoading: endTableLoading } = useLoading();
const { route, routerPushByKey } = useRouterPush();
const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();

const loading = ref<boolean>(true);
const saveLoading = ref<boolean>(false);
const stepCurrent = ref<number>(1);
const project = ref<Api.Ers.Project | null>(null);
const projectForm = ref<Api.Ers.ProjectForm | null>(null);
const projectInputs = ref<Api.Ers.ProjectInput[]>([]);
const showCategory = ref<boolean>(false);
const stepId = ref<number>(0);
const industryId = ref<number>(0);
const industryOptions = ref<Api.Ers.Industry[]>([]);
const enterpriseId = ref<number>(0);
const enterpriseOptions = ref<Api.Ers.Enterprise[]>([]);

const projectRef = ref(null);

const tableColumns = [
  {
    key: 'key',
    title: '序号',
    align: 'center',
    render: (_, index) => {
      return `${index + 1}`
    }
  },
  {
    key: 'title',
    title: '组件名称',
    align: 'center',
  },
  {
    key: 'type',
    title: '类型',
    align: 'center',
    minWidth: 120,
    render: row => {
      return FormTypeLabel[row.type];
    }
  },
  {
    key: 'sort',
    title: '排序',
    align: 'center',
    minWidth: 120,
    render: row => {
      return <OperateSort id={row.id} sort={row.sort} updateSort={updateErsProjectInput} onReset={getProjectForm} />
    }
  },
  {
    key: 'operate',
    title: $t('common.operate'),
    align: 'center',
    render: row => (
      <NSpace justify="end">
        { !row.form_library_id && (
          <NButton type="primary" ghost size="small" onClick={() => handleProjectInputEdit(row)}>
            编辑
          </NButton>
        )}

        <NPopconfirm onPositiveClick={() => handleProjectInputDelete(row.id)}>
          {{
            default: () => $t('common.confirmDelete'),
            trigger: () => (
              <NButton type="error" ghost size="small">
                {$t('common.delete')}
              </NButton>
            )
          }}
        </NPopconfirm>
      </NSpace>
    )
  }
];

const inputOperateType = ref<OperateType>('add');
const projectInputData = ref<Api.Ers.ProjectInput | null>(null);

function handleProjectInputAdd(mod: string) {
  inputOperateType.value = mod;
  projectInputData.value = null;
  openDrawer();
}

function handleProjectInputEdit(row: Api.Ers.ProjectInput) {
  inputOperateType.value = 'edit';
  projectInputData.value = row;
  openDrawer();
}

async function handleProjectInputDelete(id: number) {
  const { error } = await deleteErsProjectInput(id)
  if (error) {
    return;
  }

  window.$message?.success('删除成功');

  await getProjectForm();
}

async function submitProject() {
  if (saveLoading.value) {
    return;
  }

  saveLoading.value = true;

  await projectRef.value.submit();

  saveLoading.value = false;
}

async function submitProjectCallback(object: Api.Ers.Project) {
  if (project.value) {
    await getProject(object.id, true);

  } else {
    await getProject(object.id, true);

    if (project.value.is_bind_category) {
      await getCategories();
    }

    if (stepId.value) {
      await getProjectForm();
    }
  }

  changeStep(2);
}

function changeStep(current: number) {
  if (current > 1 && !project.value) {
    return;
  }

  stepCurrent.value = current;
}

function continueCreate() {
  project.value = undefined;

  changeStep(1);
}

async function handleSelectIndustry(id) {
  industryId.value = id;

  await getErsEnterprises();
  await getProjectForm();
}

async function handleSelectEnterpriseId(id) {
  enterpriseId.value = id;

  await getProjectForm();
}

async function close() {
  await closeTab(route.value.name as string);

  await routerPushByKey('ers_project', { query: { page: 1 } });
}

async function handleError() {
  await closeTab(route.value.name as string);
}

async function getErsIndustries() {
  const industryData = await searchErsIndustry();

  industryId.value = industryData.data[0].id;
  industryOptions.value = industryData.data;
}

async function getErsEnterprises() {
  const enterpriseData = await searchErsEnterprise({ industry_id: industryId.value });

  enterpriseId.value = enterpriseData.data[0].id;
  enterpriseOptions.value = enterpriseData.data;
}

async function getCategories() {
  await getErsIndustries();
  await getErsEnterprises();

  showCategory.value = true;
}

async function getProject(id: number, reload: boolean = false) {
  let data;

  if (reload) {
    data = await getErsProject(id);
  } else {
    data = await getErsProject(id);

    loading.value = false;
  }

  project.value = data.data;

  if (!project.value) {
    await handleError();
  }


  if (project.value.flow?.steps[0]) {
    stepId.value = project.value.flow.steps[0].id;
  }
}

async function getProjectForm() {
  projectInputs.value = [];

  startTableLoading()

  let data = await getErsProjectForm({ project_id: project.value?.id, step_id: stepId.value, industry_id: industryId.value, enterprise_id: enterpriseId.value });

  endTableLoading()

  projectForm.value = data.data;

  projectInputs.value = projectForm.value.inputs;
}

onMounted(async () => {
  if (route.value.query.id) {
    await getProject(route.value.query.id);

    if (project.value.is_bind_category) {
      await getCategories();
    }

    if (stepId.value) {
      await getProjectForm();
    }
  } else {
    loading.value = false;
  }
});
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <NSpace v-if="!loading" vertical :size="36" class="pl-10% pt-20px">
        <NSteps :current="stepCurrent" >
          <NStep title="基本资料"></NStep>
          <NStep title="所需资料管理"></NStep>
          <NStep title="完成"></NStep>
        </NSteps>

        <div v-if="stepCurrent === 1">
          <ProjectBase ref="projectRef" :row-data="project" @submitted="submitProjectCallback" />
        </div>
        <div v-else-if="stepCurrent === 2 && projectForm">
          <NGrid :cols="24" x-gap="12">
            <NGridItem :span="3">
              <NCard title="行业类别" size="small">
                <NMenu v-if="showCategory" :options="industryOptions" :value="industryId" :root-indent="15" key-field="id" label-field="name" @update:value="handleSelectIndustry" />
                <NText v-else>-</NText>
              </NCard>
            </NGridItem>
            <NGridItem :span="3">
              <NCard title="企业类别" size="small">
                <NMenu v-if="showCategory" :options="enterpriseOptions" :value="enterpriseId" :root-indent="15" key-field="id" label-field="name" @update:value="handleSelectEnterpriseId" />
                <NText v-else>-</NText>
              </NCard>
            </NGridItem>
            <NGridItem :span="11">
              <NCard title="所需资料" size="small">
                <NDataTable
                  :columns="tableColumns"
                  :data="projectInputs"
                  :loading="tableLoading"
                  size="small"
                  remote
                />
                <template #action>
                  <NSpace reverse>
                    <NButton type="primary" @click="handleProjectInputAdd('add')">新增资料</NButton>
                    <NButton type="success" @click="handleProjectInputAdd('select')">从共用资料库中选择</NButton>
                  </NSpace>
                </template>
              </NCard>
            </NGridItem>
          </NGrid>
        </div>
        <div v-else-if="stepCurrent === 3 && project" class="mb-150px mr-24% mt-100px">
          <NResult
            status="success"
            title="保存成功"
            :description="project.title"
          >
            <template #footer>
              <NFlex justify="center">
                <NButton type="success" @click="close">返回列表页</NButton>
                <NButton type="info" @click="continueCreate()">继续新建</NButton>
              </NFlex>
            </template>
          </NResult>
        </div>
      </NSpace>

      <template #footer>
        <div v-if="stepCurrent === 1">
          <NSpace v-if="project?.id" class="pl-10%">
            <NButton type="primary" @click="changeStep(2)">下一步</NButton>
            <NButton type="success" :disabled="saveLoading" @click="submitProject">保存</NButton>
            <NButton type="default" @click="close">关闭</NButton>
          </NSpace>
          <NSpace v-else class="pl-10%">
            <NButton type="success" :disabled="saveLoading" @click="submitProject">下一步</NButton>
            <NButton type="default" @click="close">关闭</NButton>
          </NSpace>
        </div>
        <div v-else-if="stepCurrent === 2">
          <NSpace class="pl-10%">
            <NButton type="primary" @click="changeStep(1)">上一步</NButton>
            <NButton type="primary" @click="changeStep(3)">提交</NButton>
          </NSpace>
        </div>
      </template>

      <FormInputOperate
        v-if="projectForm"
        v-model:visible="drawerVisible"
        :form-id="projectForm.id"
        :operate-type="inputOperateType"
        :row-data="projectInputData"
        @submitted="getProjectForm"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
