<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue';
import { createErsProject, updateErsProject, searchErsFlow } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'ProjectBase'
});

interface Props {
  rowData?: Api.Ers.Project;
}

interface Emits {
  (e: 'submitted', object: Api.Ers.Project): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const saveLoading = ref<boolean>(false);
const flows = ref<Api.Ers.Flow[]>([]);
const attachmentFiles = ref<AttachmentFile[]>([]);

type Model = Pick<Api.Ers.Project, 'title' | 'sort' | 'icon' | 'intro' | 'status' | 'is_bind_category' | 'flow_id' & {
  upload_files?: AttachmentFile[];
}>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    title: '',
    icon: '',
    intro: '',
    sort: 0,
    status: 0,
    is_bind_category: false,
    flow_id: undefined,
  };
}

type RuleKey = Extract<keyof Model, 'title' | 'flow_id'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  title: defaultRequiredRule,
  flow_id: defaultRequiredRule
};


function handleUpdateModelWhenEdit(object?: Api.Ers.Project) {

  if (object) {
    Object.assign(model, {
      title: object.title,
      icon: object.icon,
      intro: object.intro,
      sort: object.sort,
      status: object.status,
      is_bind_category: object.is_bind_category,
      flow_id: object.flow_id,
    });
  } else {
    Object.assign(model, createDefaultModel());
  }
}

async function submit() {
  await validate();

  if (attachmentFiles.value.length > 0) {
    model.upload_files = attachmentFiles.value;
  }

  saveLoading.value = true;

  let data;

  if (props.rowData?.id) {
    data = await updateErsProject(props.rowData.id, model);
  } else {
    data = await createErsProject(model);
  }

  saveLoading.value = false;

  if (!data.data) {
    return;
  }

  emit('submitted', data.data);

  await restoreValidation();
}

function handleUploadSuccess(file: Api.System.UploaderFile) {
  attachmentFiles.value.push({
    type: 'ers',
    type_id: 0,
    filename: file.filename,
    filepath: file.key,
    url: file.url,
    created_at: file.created_at as string
  });
}

onMounted(async () => {
  handleUpdateModelWhenEdit(props.rowData);

  const data = await searchErsFlow();

  flows.value = data.data;
});

defineExpose({
  submit
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
    <NFormItem label="项目名称" path="title" class="w-420px">
      <NInput v-model:value="model.title" placeholder="请输入项目名称" />
    </NFormItem>
    <NFormItem label="选择流程" path="flow_id" class="w-320px">
      <NSelect v-model:value="model.flow_id" :options="flows" :disabled="!!rowData?.id" value-field="id" label-field="name" />
    </NFormItem>
    <NFormItem label="项目图标" path="icon">
      <UploadImage v-model:value="model.icon" :preview="rowData?.icon_src" />
    </NFormItem>
    <NFormItem label="是否绑定类别" path="is_bind_category">
      <NSwitch v-model:value="model.is_bind_category" :disabled="!!rowData?.id" />
    </NFormItem>
    <NFormItem label="项目状态" path="status">
      <NRadioGroup v-model:value="model.status">
        <NRadio :value="1" label="开启" />
        <NRadio :value="0" label="关闭" />
      </NRadioGroup>
    </NFormItem>
    <NFormItem label="项目排序" path="sort" class="w-210px">
      <NSpace vertical>
        <NInputNumber v-model:value="model.sort" :min="0" :max="10000" />
        <FormTips :tips="['数字越小越靠前']" />
      </NSpace>
    </NFormItem>
    <NFormItem label="项目介绍" path="intro" >
      <EditorSimple
        v-model:value="model.intro"
        :width="680"
        :height="320"
        @upload-success="handleUploadSuccess"
      />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
