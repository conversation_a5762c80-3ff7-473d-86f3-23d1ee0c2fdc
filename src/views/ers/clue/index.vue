<script setup lang="tsx">
import { ref } from 'vue';
import { N<PERSON>utton, NPopconfirm } from 'naive-ui';
import { fetchErsClueList } from '@/service/api/ers';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';

defineOptions({
  name: 'ErsClue',
});

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<any, typeof fetchErsClueList, 'index'>({
  apiFn: fetchErsClueList,
  apiParams: {
    current: 1,
    size: 20,
    keyword: '',
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};
    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;
    updateSearchParams({
      current: page,
      size: pageSize
    });
    getData();
  },
  columns: () => [
    { key: 'id', title: 'ID', align: 'center', width: 80 },
    { key: 'name', title: '姓名', align: 'center', width: 120 },
    { key: 'phone', title: '手机', align: 'center', width: 120 },
    { key: 'company', title: '单位', align: 'center', width: 200 },
    { key: 'created_at', title: '创建时间', align: 'center', width: 160, render: row => <DateFormat date={row.created_at} /> }
  ]
});

function handleReset() {
  resetSearchParams();
  updateSearchParams({});
  getData();
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="客户线索列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :loading="loading"
          :show-add="false"
          :show-batch-delete="false"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style> 