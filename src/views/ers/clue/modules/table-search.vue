<script setup lang="ts">
import { ref } from 'vue';
import { $t } from '@/locales';
import DatetimeRange from '@/components/common/date/datetime-range.vue';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

interface SearchParams {
  keyword?: string;
  created_at?: [string, string];
}

const emit = defineEmits<Emits>();
const model = defineModel<SearchParams>('model', { required: true });

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:8" label="关键词" path="keyword" class="pr-24px">
          <NInput v-model:value="model.keyword" placeholder="输入姓名、手机、单位关键词" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:8" label="创建时间" path="created_at" class="pr-24px">
          <DatetimeRange v-model:value="model.created_at" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:8" class="pr-24px">
          <NSpace class="w-full" justify="start">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style> 