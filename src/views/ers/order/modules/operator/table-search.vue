<script setup lang="ts">
import { onMounted, ref } from "vue";
import { $t } from '@/locales';
import { searchErsProject, searchErsIndustry, searchErsEnterprise } from '@/service/api';
import DatetimeRange from '@/components/common/date/datetime-range.vue';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type SearchParams = CommonType.RecordNullable<Pick<Api.Ers.ServiceOrder, 'sid' | 'user_id' | 'project_id' | 'industry_id' | 'enterprise_id' | 'status'>
  & Api.SystemManage.CommonSearchParams>;

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

const projects = ref<Api.Ers.Project[]>([]);
const industries = ref<Api.Ers.Industry[]>([]);
const enterprises = ref<Api.Ers.Enterprise[]>([]);

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}

async function changeIndustry(id) {
  enterprises.value = [];
  model.value.enterprise_id = null;

  const data = await searchErsEnterprise({ industry_id: id });

  enterprises.value = data.data;
}

onMounted(async () => {
  let data = await searchErsProject();

  projects.value = data.data;

  data = await searchErsIndustry();

  industries.value = data.data;
});
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:4" label="工单编号" path="sid" class="pr-24px">
          <NInput v-model:value="model.sid" placeholder="输入工单编号" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="提交用户" path="user_id" class="pr-24px">
          <SearchUser v-model:value="model.user_id" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="服务项目" path="project_id" class="pr-24px">
          <NSelect v-model:value="model.project_id" :options="projects" value-field="id" label-field="title" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="行业类别" path="industry_id" class="pr-24px">
          <NSelect v-model:value="model.industry_id" :options="industries" value-field="id" label-field="name" clearable @update:value="changeIndustry"  />
        </NFormItemGi>
        <NFormItemGi v-if="model.industry_id" span="24 s:12 m:4" label="企业类别" path="enterprise_id" class="pr-24px">
          <NSelect v-model:value="model.enterprise_id" :options="enterprises" value-field="id" label-field="name" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:7" label="创建时间" path="created_at" class="pr-24px">
          <DatetimeRange v-model:value="model.created_at"  />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:5" class="pr-24px">
          <NSpace class="w-full" justify="start">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
