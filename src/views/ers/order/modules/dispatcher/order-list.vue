<script setup lang="tsx">
import { ref } from 'vue';
import { N<PERSON>utton, NTag } from 'naive-ui';
import { fetchErsOrderList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from "@/hooks/common/router";
import { $t } from '@/locales';
import TableSearch from './table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnUser from '@/components/common/table/column-user.vue';

interface Props {
  statusOptions: object[];
}

const props = defineProps<Props>();

const { routerPushByKey } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Ers.ServiceOrder, typeof fetchErsOrderList, 'index' | 'operate'>({
  apiFn: fetchErsOrderList,
  apiParams: {
    current: 1,
    size: 10,
    sid: null,
    user_id: null,
    project_id: null,
    industry_id: null,
    enterprise_id: null,
    admin_id: null,
    status: props.statusOptions[0].status,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 100
    },
    {
      key: 'sid',
      title: '工单编号',
      align: 'center',
      width: 120,
      render: row => {
        if (row.deleted_at) {
          return <NTag type="error" bordered={false}>{row.sid}</NTag>;
        } else {
          return row.sid;
        }
      }
    },
    {
      key: 'user',
      title: '提交用户昵称',
      align: 'center',
      width: 150,
      render: row => {
        return <ColumnUser user_id={row.user_id} user={row.user} />
      }
    },
    {
      key: 'user_id',
      title: '提交用户手机',
      align: 'center',
      width: 120,
      render: row => {
        return row.user?.phone;
      }
    },
    {
      key: 'project_id',
      title: '服务项目',
      align: 'center',
      width: 150,
      render: row => {
        return row.project?.title;
      }
    },
    {
      key: 'industry_id',
      title: '行业类别',
      align: 'center',
      width: 100,
      render: row => {
        return row.industry ? row.industry.name : '-';

      }
    },
    {
      key: 'enterprise_id',
      title: '企业类别',
      align: 'center',
      width: 100,
      render: row => {
        return row.enterprise ? row.enterprise.name : '-';
      }
    },
    {
      key: 'admin_id',
      title: '代办人',
      align: 'center',
      width: 150,
      render: row => {
        return row.operator ? row.operator.real_name : '-';
      }
    },
    {
      key: 'finished_at',
      title: '完成时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 100,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleShow(row.id)}>
            查看
          </NButton>
        </div>
      )
    }
  ]
});

const checkedRowKeys = ref<number[]>([]);

function handleShow(id: number) {
  routerPushByKey('ers_order-detail', { query: { id } })
}

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    question_id: null
  });
}

function handleTabValue(value: number) {
  updateSearchParams({
    status: props.statusOptions[value].status
  });

  getData();
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
  <NCard title="工单列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
    <template #header-extra>
      <TableHeaderOperation
        v-model:columns="filteredColumns"
        :disabled-delete="checkedRowKeys.length === 0"
        :loading="loading"
        :show-batch-delete="false"
        :show-add="false"
        @refresh="getData"
      />
    </template>
    <NTabs type="line" animated @update:value="handleTabValue">
      <NTabPane v-for="item in statusOptions" :name="item.value" :tab="item.label">
        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="data"
          size="small"
          :scroll-x="702"
          :loading="loading"
          remote
          :pagination="pagination"
          :row-key="item => item.id"
          class="sm:h-full"
        />
      </NTabPane>
    </NTabs>

  </NCard>
</template>

<style scoped></style>
