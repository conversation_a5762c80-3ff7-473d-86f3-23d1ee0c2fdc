<script setup lang="tsx">
import { ref } from 'vue';
import { checkServiceOrderPermission } from "@/utils/common";
import DispatcherOrderList from './modules/dispatcher/order-list.vue';
import OperatorOrderList from './modules/operator/order-list.vue';

defineOptions({
  name: 'ErsOrder',
});

const orderPermission = ref<boolean>(checkServiceOrderPermission());

const statusOptions = [
  {
    label: '待办事项',
    value: 0,
    status: [2]
  },
  {
    label: '已办未结',
    value: 1,
    status: [1]
  },
  {
    label: '已办结束',
    value: 2,
    status: [3]
  },
];
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <DispatcherOrderList v-if="orderPermission" :status-options="statusOptions" />
    <OperatorOrderList v-else :status-options="statusOptions" />
  </div>
</template>

<style scoped></style>
