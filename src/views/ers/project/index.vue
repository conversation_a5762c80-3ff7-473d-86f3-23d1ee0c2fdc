<script setup lang="tsx">
import { ref } from 'vue';
import { N<PERSON>utton, NPopconfirm, NTag } from 'naive-ui';
import { fetchErsProjectList, deleteErsProject, updateErsProject } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from "@/hooks/common/router";
import { ersProjectStatusLabel } from "@/constants/business";
import { $t } from '@/locales';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import ImageSingle from '@/components/common/image/image-single.vue';
import OperateSort from '@/components/common/table/operate-sort.vue';

defineOptions({
  name: 'ErsProject',
});

const { routerPushByKey } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Ers.Project, typeof fetchErsProjectList, 'index' | 'operate'>({
  apiFn: fetchErsProjectList,
  apiParams: {
    current: 1,
    size: 20,
    id: null,
    title: null,
    status: null,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'icon',
      title: '图标',
      align: 'center',
      width: 80,
      render: row => {
        return <ImageSingle src={row.icon_src} width={64} />
      }
    },
    {
      key: 'title',
      title: '名称',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'flow_id',
      title: '流程',
      align: 'center',
      minWidth: 120,
      render: row => {
        return row.flow?.name;
      }
    },
    {
      key: 'is_bind_category',
      title: '是否绑定类别',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<boolean, NaiveUI.ThemeColor> = {
          true: 'primary',
          false: 'default'
        };

        const label = row.is_bind_category ? '是' : '否';

        return (
          <NTag type={tagMap[row.is_bind_category]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        const label = ersProjectStatusLabel[row.status];

        return <NTag type={tagMap[row.status]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'sort',
      title: '排序',
      align: 'center',
      width: 100,
      render: row => {
        return <OperateSort id={row.id} sort={row.sort} updateSort={updateErsProject} onReset={getData} />
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '修改时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 100,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row.id)}>
            编辑
          </NButton>

          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const checkedRowKeys = ref<number[]>([]);

function handleAdd() {
  routerPushByKey('ers_project-setting', { query: { } }, true)
}

function handleEdit(id: number) {
  routerPushByKey('ers_project-setting', { query: { id } }, true)
}

async function handleDelete(id: number) {
  const { error } = await deleteErsProject(id)

  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    question_id: null
  });
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="项目列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-batch-delete="false"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
