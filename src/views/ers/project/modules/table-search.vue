<script setup lang="ts">
import { $t } from '@/locales';
import { ersProjectStatusOptions } from '@/constants/business';
import DatetimeRange from '@/components/common/date/datetime-range.vue';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type SearchParams = CommonType.RecordNullable<Pick<Api.Ers.FormLibrary, 'id' | 'title' | 'status'> & Api.SystemManage.CommonSearchParams>;

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:3" label="ID" path="id" class="pr-24px">
          <NInput v-model:value="model.id" placeholder="ID" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="名称" path="title" class="pr-24px">
          <NInput v-model:value="model.title" placeholder="输入项目名称" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="状态" path="status" class="pr-24px">
          <NSelect v-model:value="model.status" :options="ersProjectStatusOptions" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:7" label="创建时间" path="created_at" class="pr-24px">
          <DatetimeRange v-model:value="model.created_at"  />
        </NFormItemGi>
        <NFormItemGi span="24 s:4">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
