<script setup lang="ts">
import { ref } from "vue";
import Industry from './modules/industry.vue';
import Enterprise from './modules/enterprise.vue';

const enterpriseRef = ref();

function changeEnterprise(id: number) {
  enterpriseRef.value.open(id);
}

</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <NCard title="类别列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <NGrid :cols="24" x-gap="12">
        <NGridItem :span="5">
          <Industry @change="changeEnterprise" />
        </NGridItem>
        <NGridItem :span="5">
          <Enterprise ref="enterpriseRef" />
        </NGridItem>
      </NGrid>
    </NCard>
  </div>
</template>
