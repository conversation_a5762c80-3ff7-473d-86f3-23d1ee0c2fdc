<script setup lang="ts">
import { ref, onMounted } from "vue";
import { searchErsIndustry, deleteErsIndustry, moveErsIndustry } from '@/service/api';
import { useBoolean } from "@sa/hooks";
import { EditOutlined } from "@vicons/antd";
import { Add, Remove, ArrowUp, ArrowDown } from "@vicons/ionicons5";
import IndustryOperate, { type OperateType } from "./industry-operate.vue";

interface Emits {
  (e: 'change', id: number): void;
}

const emit = defineEmits<Emits>();

const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();
const { bool: loading, setBool: setLoading } = useBoolean();

const operateType = ref<OperateType>('add');
const selectIndex = ref<number>(0);
const currentId = ref<number>(0);
const options = ref<Api.Ers.Industry[]>([]);

const editingData = ref<Api.Ers.Industry | null>(null);

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;
  openDrawer();
}

function handleEdit() {
  if (selectIndex.value < 0) {
    return;
  }

  operateType.value = 'edit';
  editingData.value = options.value[selectIndex.value];
  openDrawer();
}

function handleChange(selectId) {
  selectIndex.value = options.value.findIndex(item => item.id === selectId);
  currentId.value = options.value[selectIndex.value].id;

  emit('change', currentId.value);
}

async function handleRemove() {
  if (loading.value) {
    return;
  }

  setLoading(true);

  const { error } = await deleteErsIndustry(options.value[selectIndex.value].id);

  setLoading(false);

  if (error) {
    return;
  }

  await getData();

  window.$message?.success('删除成功');
}

async function handleMove(type: string) {
  const count = options.value.length;
  const index = selectIndex.value;
  const fromId = options.value[index].id;

  if (type === 'up') {
    if (index <= 0) {
      return;
    }

    selectIndex.value = index - 1;
  } else {
    selectIndex.value = index + 1;

    if (index < 0 || selectIndex.value >= count) {
      return;
    }
  }

  if (loading.value) {
    return;
  }

  setLoading(true);

  await moveErsIndustry(fromId, options.value[selectIndex.value].id);

  setLoading(false);

  await getData();
}

async function getData(reload: boolean = true) {
  const data = await searchErsIndustry();

  options.value = data.data;

  if (!reload) {
    handleChange(data.data[0].id);
  }
}

onMounted(async () => {
  await getData(false);
});
</script>

<template>
  <NCard title="行业类别" size="small">
    <NMenu :options="options" :value="currentId" :root-indent="15" key-field="id" label-field="name" @update:value="handleChange" />
    <template #action>
      <NSpace>
        <NButton quaternary circle type="primary" @click="handleAdd">
          <NIcon><Add /></NIcon>
        </NButton>
        <NButton quaternary circle type="info" @click="handleEdit">
          <NIcon><EditOutlined /></NIcon>
        </NButton>
        <NButton quaternary circle type="error" @click="handleRemove">
          <NIcon><Remove /></NIcon>
        </NButton>
        <NButton quaternary circle type="primary" :disabled="selectIndex === 0" @click="handleMove('up')">
          <NIcon><ArrowUp /></NIcon>
        </NButton>
        <NButton quaternary circle type="primary" :disabled="selectIndex === options.length - 1" @click="handleMove('down')">
          <NIcon><ArrowDown /></NIcon>
        </NButton>
      </NSpace>
    </template>
    <IndustryOperate
      v-model:visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="getData"
    />
  </NCard>
</template>
