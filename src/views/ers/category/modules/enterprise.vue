<script setup lang="ts">
import { ref, onMounted } from "vue";
import { searchErsEnterprise, deleteErsEnterprise, moveErsEnterprise } from '@/service/api';
import { useBoolean } from "@sa/hooks";
import { EditOutlined } from "@vicons/antd";
import { Add, Remove, ArrowUp, ArrowDown } from "@vicons/ionicons5";
import EnterpriseOperate, { type OperateType } from "./enterprise-operate.vue";

const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();
const { bool: loading, setBool: setLoading } = useBoolean();

const industryId = ref<number>(0);
const operateType = ref<OperateType>('add');
const selectIndex = ref<number>(0);
const currentId = ref<number>(0);
const options = ref<Api.Ers.Enterprise[]>([]);

const editingData = ref<Api.Ers.Enterprise | null>(null);

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;
  openDrawer();
}

function handleEdit() {
  if (selectIndex.value < 0) {
    return;
  }

  operateType.value = 'edit';
  editingData.value = options.value[selectIndex.value];
  openDrawer();
}

function handleChange(selectId) {
  selectIndex.value = options.value.findIndex(item => item.id === selectId);
  currentId.value = options.value[selectIndex.value].id;
}

async function handleRemove() {
  if (loading.value) {
    return;
  }

  setLoading(true);

  const { error } = await deleteErsEnterprise(options.value[selectIndex.value].id);

  setLoading(false);

  if (error) {
    return;
  }

  await getData();

  window.$message?.success('删除成功');
}

async function handleMove(type: string) {
  const count = options.value.length;
  const index = selectIndex.value;
  const fromId = options.value[index].id;

  if (type === 'up') {
    if (index <= 0) {
      return;
    }

    selectIndex.value = index - 1;
  } else {
    selectIndex.value = index + 1;

    if (index < 0 || selectIndex.value >= count) {
      return;
    }
  }

  if (loading.value) {
    return;
  }

  setLoading(true);

  await moveErsEnterprise(fromId, options.value[selectIndex.value].id);

  setLoading(false);

  await getData();
}

async function getData() {
  const data = await searchErsEnterprise({ industry_id: industryId.value });

  options.value = data.data;
}

async function open(id: number) {
  industryId.value = id;

  await getData();

  if (options.value.length > 0) {
    handleChange(options.value[0].id);
  }
}

defineExpose({
  open
});
</script>

<template>
  <NCard title="企业类别" size="small">
    <NMenu :options="options" :value="currentId" :root-indent="15" key-field="id" label-field="name" @update:value="handleChange" />
    <template #action>
      <NSpace>
        <NButton quaternary circle type="primary" @click="handleAdd">
          <NIcon><Add /></NIcon>
        </NButton>
        <NButton quaternary circle type="info" @click="handleEdit">
          <NIcon><EditOutlined /></NIcon>
        </NButton>
        <NButton quaternary circle type="error" @click="handleRemove">
          <NIcon><Remove /></NIcon>
        </NButton>
        <NButton quaternary circle type="primary" :disabled="selectIndex === 0" @click="handleMove('up')">
          <NIcon><ArrowUp /></NIcon>
        </NButton>
        <NButton quaternary circle type="primary" :disabled="selectIndex === options.length - 1" @click="handleMove('down')">
          <NIcon><ArrowDown /></NIcon>
        </NButton>
      </NSpace>
    </template>
    <EnterpriseOperate
      v-if="industryId"
      v-model:visible="drawerVisible"
      :industry-id="industryId"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="getData"
    />
  </NCard>
</template>
