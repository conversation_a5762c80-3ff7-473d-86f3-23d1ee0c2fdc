<script setup lang="tsx">
import { onMounted, ref } from "vue";
import { getErsOrder, finishErsOrder } from '@/service/api';
import { useRouterPush } from "@/hooks/common/router";
import { useLoading } from "~/packages/hooks";
import { closeTab } from '@/utils/tab';
import { checkServiceOrderPermission } from "@/utils/common";
import OrderForm from "./modules/order-form.vue";
import OrderPayment from "./modules/order-payment.vue";
import SolutionDownload from "./modules/solution-download.vue";
import SolutionPreview from "./modules/solution-preview.vue";
import RoleDispatcher from "./modules/role-dispatcher.vue";
import RoleOperator from "./modules/role-operator.vue";

const { route } = useRouterPush();
const { loading, startLoading, endLoading } = useLoading();
const { loading: finishLoading, startLoading: startFinishLoading, endLoading: endFinishLoading } = useLoading();

const orderId = ref<number>(0);
const order = ref<Api.Ers.ServiceOrder | null>(null);
const orderPermission = ref<boolean>(checkServiceOrderPermission());

async function handleError() {
  await closeTab(route.value.name as string);
}

async function finish() {
  if (finishLoading.value) {
    return;
  }

  startFinishLoading();

  await finishErsOrder(orderId.value, { force: true});

  endFinishLoading();

  await init();
}

async function init() {
  startLoading();

  const data = await getErsOrder(orderId.value);

  endLoading();

  if (!data.data) {
    await handleError();
  }

  order.value = data.data;
}

onMounted(async () => {
  if (!route.value.query.id) {
    await handleError();
  }

  orderId.value = parseInt(route.value.query.id);

  await init();
});
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <NGrid v-if="order">
        <NGridItem :span="22" class="order-grid">
          <NCard title="基本信息" size="small">
            <NSpace vertical>
              <div>编号：{{ order.sid }}</div>
              <div>
                <NSpace>
                  <NText class="font-size-6 font-800">{{ order.project?.title }}</NText>
                  <NTag v-if="order.status === 2" class="mt-1.5">待办事项</NTag>
                  <NTag v-else-if="order.status === 1" type="primary" class="mt-1.5">已办未结</NTag>
                  <NTag v-else-if="order.status === 3" type="success" class="mt-1.5">已办结束</NTag>
                  <NTag v-else class="mt-1.5">{{ order.status }}</NTag>
                </NSpace>
              </div>
              <div v-if="order.industry_id" class="font-size-3 font-thin">行业类别：{{ order.industry?.name }}</div>
              <div v-if="order.enterprise_id" class="font-size-3 font-thin">企业类别：{{ order.enterprise?.name }}</div>
              <div class="font-size-3 font-thin">提交人：{{ order.user?.nickname }} / {{ order.user?.phone }}</div>
              <div class="font-size-3 font-thin">提交时间：<DateFormat :date="order.created_at" /></div>
              <div v-if="order.status === 3" class="font-size-3 font-thin">完成时间：<DateFormat :date="order.finished_at" /></div>
            </NSpace>
          </NCard>
        </NGridItem>
        <NGridItem :span="7" class="order-grid">
          <NCard v-if="!loading" title="服务记录" size="small" :segmented="{
            content: true,
            footer: 'soft',
          }">
            <RoleDispatcher v-if="orderPermission" :order="order" @reload="init" />
            <RoleOperator v-else :order="order" @reload="init" />
            <template v-if="order.status !== 3" #footer>
              <NSpace justify="center">
                <NPopconfirm @positive-click="finish">
                  <template #trigger>
                    <NButton type="warning">终止任务</NButton>
                  </template>
                  您确定要终止任务吗？
                </NPopconfirm>
              </NSpace>
            </template>
          </NCard>
        </NGridItem>
        <NGridItem :span="15" class="order-grid">
          <template v-if="!loading" v-for="(item, index) in order.flows" :key="index">
            <NCard v-if="item.data" :title="item.module === 'form' ? '资料列表' : item.name" size="small" class="mb-2">
              <template v-if="item.module === 'form'">
                <OrderForm :data="item.data" />
              </template>
              <template v-else-if="item.module === 'payment_stage'">
                <OrderPayment :data="item.data" />
              </template>
              <template v-else-if="item.module === 'solution_preview'">
                <SolutionPreview :data="item.data" />
              </template>
              <template v-else-if="item.module === 'payment_final'">
                <OrderPayment :data="item.data" :total-amount="item.data.total_amount" />
              </template>
              <template v-else-if="item.module === 'solution_download'">
                <SolutionDownload :data="item.data" />
              </template>
              <template v-else>-</template>
            </NCard>
          </template>
        </NGridItem>
      </NGrid>
    </NCard>
  </div>
</template>

<style scoped>
  .order-grid {
    margin: 10px;
  }
</style>
