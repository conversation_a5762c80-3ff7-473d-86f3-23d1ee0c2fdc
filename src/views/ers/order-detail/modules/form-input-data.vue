<script setup lang="tsx">
interface Props {
  data: object;
}

const props = defineProps<Props>();

</script>

<template>
  <div v-if="data.type === 'image'">
    <NImageGroup>
      <NSpace justify="center">
        <NImage v-for="(item, index) in data.value" :key="index" width="32" :src="item.path_src" />
      </NSpace>
    </NImageGroup>
  </div>
  <div v-else-if="data.type === 'file'">
    <NImageGroup>
      <NSpace justify="center">
        <NButton
          v-for="(item, index) in data.value" :key="index"
          text
          tag="a"
          :href="item.path_src"
          target="_blank"
          type="primary"
        >
          附件{{ index + 1 }}
        </NButton>
      </NSpace>
    </NImageGroup>
  </div>
  <div v-else-if="data.type === 'checkbox'">
    {{ data.value.join(',') }}
  </div>
  <div v-else>
    {{ data.value }}
  </div>
</template>

<style scoped></style>
