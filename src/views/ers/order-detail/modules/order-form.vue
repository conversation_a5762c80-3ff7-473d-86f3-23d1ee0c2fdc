<script setup lang="tsx">
import FormInputData from './form-input-data.vue'

interface Props {
  data: object[];
}

const props = defineProps<Props>();

const statusLabel = {
  1: '待审核',
  2: '已通过',
  3: '未通过',
}

const tableColumns = [
  {
    key: 'id',
    title: '序号',
    align: 'center',
    minWidth: 80,
    render: (_, index) => {
      return `${index + 1}`
    }
  },
  {
    key: 'title',
    title: '名称',
    align: 'center',
    minWidth: 200,
  },
  {
    key: 'value',
    title: '信息',
    align: 'center',
    minWidth: 200,
    render: row => {
      return <FormInputData data={row} />
    }
  },
  {
    key: 'status',
    title: '状态',
    align: 'center',
    minWidth: 100,
    render: row => {
      const tagMap: Record<number, NaiveUI.ThemeColor> = {
        1: 'primary',
        2: 'success',
        3: 'error'
      };

      const label = statusLabel[row.status];

      if (row.status > 0 ) {
        return <NTag type={tagMap[row.status]} bordered={false}>{label}</NTag>;
      } else {
        return '-';
      }
    }
  },
  {
    key: 'reject_reason',
    title: '拒绝原因',
    align: 'center',
    minWidth: 200,
  }
];
</script>

<template>
  <NDataTable :columns="tableColumns" :data="data" size="small" />
</template>

<style scoped></style>
