<script setup lang="tsx">
import { onMounted, ref } from 'vue'
import { CheckmarkCircle } from '@vicons/ionicons5';
import { ClockCircleOutlined } from '@vicons/antd';
import UpdateOperator from "./role/update-operator.vue";
import DispatcherAvatar from '@/assets/avatar/admin_default.jpg'
import OperatorAvatar from '@/assets/avatar/teacher_default.jpg'

interface Props {
  order: Api.Ers.ServiceOrder;
}

interface Emits {
  (e: 'reload'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const updateOperatorRef = ref();

const actions = ref<object[]>([]);

function reload() {
  emit('reload');
}

function init() {
  const list = [];

  props.order?.flows.forEach(item => {
    item.sub_flows.forEach(subItem => {
      if (subItem.people && subItem.people.who === 1) {
        subItem.people.avatar = OperatorAvatar;
      }

      list.push({
        ...subItem,
        show: false
      });
    });
  });

  if (list[0].status === 2) {
    if (props.order.admin_id) {
      list.splice(1, 0, {
        desc: '更换老师',
        status: 2,
        at: null,
        show: true,
        people: {
          who: 2,
          name: '系统管理员',
          avatar: DispatcherAvatar
        },
        action: () => {
          updateOperatorRef.value.open({
            order_id: props.order.id,
          });
        }
      });
    } else {
      list.splice(1, 0, {
        desc: '分配老师',
        status: 1,
        at: null,
        show: true,
        people: {
          who: 2,
          name: '系统管理员',
          avatar: DispatcherAvatar
        },
        action: () => {
          updateOperatorRef.value.open({
            order_id: props.order.id,
          });
        }
      });
    }
  }

  actions.value = list;
}

onMounted(() => {
  init();
});
</script>

<template>
  <NList>
    <NListItem v-for="(item, index) in actions" :key="index">
      <NThing>
        <template v-if="item.people" #avatar>
          <NAvatar circle :src="item.people.avatar" />
        </template>
        <template v-else #avatar>
          <NAvatar circle :src="OperatorAvatar" />
        </template>
        <template v-if="item.people" #header>
          <div class="font-size-3.8 font-600">{{ item.people.name }}</div>
        </template>
        <template v-else #header>
          <div class="font-size-3.8 font-600">暂无</div>
        </template>
        <template #header-extra>
          <NTag v-if="item.status === 1" type="primary" size="small" :bordered="false">
            待处理
            <template #icon>
              <NIcon><ClockCircleOutlined /></NIcon>
            </template>
          </NTag>
          <NTag v-else-if="item.status === 2" type="success" size="small" :bordered="false">
            已完成
            <template #icon>
              <NIcon><CheckmarkCircle /></NIcon>
            </template>
          </NTag>
        </template>
        <template  #description>
          <NSpace vertical :size="0">
            <div class="font-size-3 font-200">{{ item.desc }}</div>
            <div v-if="item.at" class="font-size-3 font-200"><DateFormat :date="item.at" /></div>
          </NSpace>
        </template>
        <template v-if="item.show" #footer>
          <NSpace justify="center">
            <NButton type="primary" ghost @click="item.action">{{ item.desc }}</NButton>
          </NSpace>
        </template>
      </NThing>
    </NListItem>
  </NList>
  <UpdateOperator ref="updateOperatorRef" @reload="reload" />
</template>

<style scoped></style>
