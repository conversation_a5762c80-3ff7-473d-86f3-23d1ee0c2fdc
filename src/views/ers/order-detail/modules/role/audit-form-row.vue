<script setup lang="tsx">
import { ref, onMounted } from 'vue';

type Model = {
  pass: boolean;
  reject_reason: string;
};

interface Props {
  rowData: object;
  rowIndex: number;
}

interface Emits {
  (e: 'submit', index: number, data: object): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const rejectReason = ref<String>('');
const model = ref<Model>(null);

function submit(pass: boolean) {
  if (!pass && !rejectReason.value) {
    window.$message?.warning('请填写原因');
    return;
  }

  if (pass) {
    rejectReason.value = '';
  }

  model.value = {
    pass,
    reject_reason: rejectReason.value,
  };

  emit('submit', props.rowIndex, {
    id: props.rowData.id,
    ...model.value
  });
}

function cancel() {
  model.value = null;
}

onMounted(() => {
  if (props.rowData.status === 2) {
    model.value = {
      pass: true,
      reject_reason: ''
    }
  }
})
</script>

<template>
  <NSpace v-if="model" vertical>
    <NButton type="primary" @click="cancel">修改</NButton>
    <NGradientText v-if="model.pass" type="success" :size="18">通过</NGradientText>
    <NGradientText v-else type="error" :size="18">拒绝：{{ model.reject_reason }}</NGradientText>
  </NSpace>
  <NSpace v-else vertical>
    <NSpace>
      <NInput v-model:value="rejectReason" placeholder="请输入原因" />
      <NButton type="error" @click="submit(false)">拒绝</NButton>
    </NSpace>
    <NSpace vertical>
      <NButton type="success" @click="submit(true)">通过</NButton>
    </NSpace>
  </NSpace>
</template>

<style scoped></style>
