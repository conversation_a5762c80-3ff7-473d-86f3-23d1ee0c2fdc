<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { updateErsOrderOperators, getErsOrderOperators } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = {
  order_id: number;
  admin_id: number;
};

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const visible = ref<boolean>(false);
const operators = ref<Api.SystemManage.Admin[]>([]);

const model = ref<Model>({
  order_id: undefined,
  admin_id: undefined
});

const rules: Record<string, App.Global.FormRule> = {
  admin_id: defaultRequiredRule,
};

function open(data: object) {
  model.value.order_id = data.order_id;

  visible.value = true;
}

function closeDrawer() {
  restoreValidation();

  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  const { error } = await updateErsOrderOperators(model.value);

  endLoading();

  if(error) {
    return;
  }

  window.$message?.success('操作成功');

  closeDrawer();
  emit('reload');
}

onMounted(async () => {
  let data = await getErsOrderOperators();

  operators.value = data.data;
});

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="620">
    <NDrawerContent title="分配师资" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="120">
        <NFormItem label="选择师资" path="admin_id" class="w-320px">
          <NSelect v-model:value="model.admin_id" :options="operators" value-field="id" label-field="real_name" clearable />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
