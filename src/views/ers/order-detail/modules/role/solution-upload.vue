<script setup lang="tsx">
import { ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { uploadErsOrderSolution } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';
import UploadFiles, { type AttachmentType } from "@/components/common/upload/upload-files.vue";

const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = {
  solution_files: AttachmentType[];
};

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const visible = ref<boolean>(false);
const params = ref<object>(null);
const model = ref<Model>({
  solution_files: []
});

const uploadFileRef = ref();
const oldFiles = ref<string[]>([]);
const removeFiles = ref<string[]>([]);

const rules: Record<string, App.Global.FormRule> = {
  solution_files: defaultRequiredRule,
};

function open(data: object) {
  params.value = data;
  oldFiles.value = data.files ? data.files : [];

  model.value.solution_files = oldFiles.value;


  visible.value = true;
}

function closeDrawer() {
  restoreValidation();

  visible.value = false;
}

function handleRemove(files: string[]) {
  removeFiles.value = files;
}

async function handleSubmit() {
  await validate();

  const formData = {
    files_add: uploadFileRef.value.getAddFiles(),
    files_remove: uploadFileRef.value.getAddFiles(),
  }

  startLoading();

  const { error } = await uploadErsOrderSolution(params.value.order_id, params.value.step_id, formData);

  endLoading();

  if(error) {
    return;
  }

  window.$message?.success('操作成功');

  closeDrawer();
  emit('reload');
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="620">
    <NDrawerContent title="上传方案" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="120">
        <NFormItem label="方案附件" path="solution_files" class="w-500px">
          <UploadFiles
            ref="uploadFileRef"
            v-model:value="model.solution_files"
            drag
            :limit="9"
            :upload-config="{
              file_types: ['image', 'doc'],
              storage: 'priv',
              prefix: params.step_id,
              max_size: 102400
            }"
            tips="请上传方案附件文件，大小在10M以内"
            @remove="handleRemove"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
