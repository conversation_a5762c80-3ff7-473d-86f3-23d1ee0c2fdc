<script setup lang="tsx">
import { ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { setErsOrderPaymentAmount } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = {
  total_amount: number;
  pay_amount: number;
};

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const visible = ref<boolean>(false);
const params = ref<object>(null);
const model = ref<Model>({
  total_amount: undefined,
  pay_amount: undefined
});

let rules: Record<string, App.Global.FormRule>;

function open(data: object) {
  params.value = data;

  if (data.type === 'stage') {
    rules = {
      total_amount: defaultRequiredRule,
      pay_amount: defaultRequiredRule,
    }

    if (data.total_amount) {
      model.value.total_amount = parseFloat(data.total_amount);
      model.value.pay_amount = parseFloat(data.pay_amount);
    }
  } else {
    rules = {
      total_amount: defaultRequiredRule,
    }

    if (data.total_amount) {
      model.value.total_amount = parseFloat(data.total_amount);
    }
  }

  visible.value = true;
}

function closeDrawer() {
  restoreValidation();

  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  const { error } = await setErsOrderPaymentAmount(params.value.order_id, params.value.step_id, model.value);

  endLoading();

  if(error) {
    return;
  }

  window.$message?.success('操作成功');

  closeDrawer();
  emit('reload');
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="620">
    <NDrawerContent title="设置服务价格" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="120">
        <template v-if="params.type === 'stage'">
          <NFormItem label="服务总价" path="total_amount" class="w-260px">
            <NInputNumber v-model:value="model.total_amount" placeholder="请输入金额">
              <template #suffix>元</template>
            </NInputNumber>
          </NFormItem>
          <NFormItem label="预付款" path="pay_amount" class="w-260px">
            <NInputNumber v-model:value="model.pay_amount" placeholder="请输入金额">
              <template #suffix>元</template>
            </NInputNumber>
          </NFormItem>
        </template>
        <template v-else>
          <NFormItem label="服务总价" path="total_amount" class="w-260px">
            <NInputNumber v-model:value="model.total_amount" placeholder="请输入金额">
              <template #suffix>元</template>
            </NInputNumber>
          </NFormItem>
        </template>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
