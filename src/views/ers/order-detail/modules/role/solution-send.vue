<script setup lang="tsx">
import { ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { sendErsOrderSolution } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const visible = ref<boolean>(false);
const params = ref<object>(null);

const rules: Record<string, App.Global.FormRule> = {
  total_amount: defaultRequiredRule,
  pay_amount: defaultRequiredRule,
};

function open(data: object) {
  params.value = data;

  visible.value = true;
}

function closeDrawer() {
  restoreValidation();

  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  const { error } = await sendErsOrderSolution(params.value.order_id, params.value.step_id);

  endLoading();

  if(error) {
    return;
  }

  window.$message?.success('操作成功');

  closeDrawer();
  emit('reload');
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="620">
    <NDrawerContent title="发送方案" :native-scrollbar="false" closable>
      <NResult
        status="info"
        size="small"
        title="确认发送方案至邮箱并完结"
        :description="`确认已将方案发送至邮箱：${params.email}`"
      />
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">确认发送并完结</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
