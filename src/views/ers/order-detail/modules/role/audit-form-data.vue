<script setup lang="tsx">
import { ref } from 'vue';
import { $t } from '@/locales';
import { auditErsOrderFormData } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';
import FormInputData from "../form-input-data.vue";
import AuditFormRow from "./audit-form-row.vue";

const { loading, startLoading, endLoading } = useLoading();

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const visible = ref<boolean>(false);
const params = ref<object>(null);
const audits = ref<object[]>([]);

const tableColumns = [
  {
    key: 'id',
    title: '序号',
    align: 'center',
    minWidth: 80,
    render: (_, index) => {
      return `${index + 1}`
    }
  },
  {
    key: 'title',
    title: '名称',
    align: 'center',
    minWidth: 200
  },
  {
    key: 'value',
    title: '信息',
    align: 'center',
    minWidth: 200,
    render: row => {
      return <FormInputData data={row} />
    }
  },
  {
    key: 'operate',
    title: '操作',
    align: 'left',
    minWidth: 240,
    render: (row, index) => (
      <AuditFormRow rowData={row} rowIndex={index} onSubmit={updateRow} />
    )
  }
];

function updateRow(index: number, data: object) {
  audits.value[index].audit = data;
}

function open(data: object) {
  params.value = data;
  audits.value = data.form_data;

  visible.value = true;
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  const postData = {};

  for (const index in audits.value) {
    if (!audits.value[index].audit) {
      window.$message?.warning('请全部审核后提交');
      return;
    }

    postData[audits.value[index].audit.id] = {
      pass: audits.value[index].audit.pass,
      reject_reason: audits.value[index].audit.reject_reason,
    };
  }

  startLoading();

  const { error } = await auditErsOrderFormData(params.value.order_id, params.value.step_id, { audits: postData });

  endLoading();

  if(error) {
    return;
  }

  window.$message?.success('操作成功');

  closeDrawer();
  emit('reload');
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="860">
    <NDrawerContent title="审核资料" :native-scrollbar="false" closable>
      <NDataTable :columns="tableColumns" :data="audits" size="small" />
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
