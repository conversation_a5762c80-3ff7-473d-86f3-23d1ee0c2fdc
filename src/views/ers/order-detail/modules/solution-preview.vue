<script setup lang="tsx">
import { getFileSize } from '@/utils/common';

interface Props {
  data: object;
}

const props = defineProps<Props>();

</script>

<template>
  <NSpace vertical :size="0">
    <div v-for="(item, index) in data.files" :key="index">
      <NSpace>
        <NText>{{ item.filename }}</NText>
        <NText>{{ getFileSize(item.filesize) }}</NText>
        <NButton text tag="a" target="_blank" type="primary" :href="item.path_src">查看</NButton>
      </NSpace>
    </div>
  </NSpace>
</template>

<style scoped></style>
