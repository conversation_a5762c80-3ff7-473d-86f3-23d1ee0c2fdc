<script setup lang="tsx">
import { onMounted, ref } from 'vue'
import { CheckmarkCircle } from '@vicons/ionicons5';
import { ClockCircleOutlined } from '@vicons/antd';
import AuditFormData from "./role/audit-form-data.vue";
import PaymentAmountSetting from "./role/payment-amount-setting.vue";
import SolutionSend from "./role/solution-send.vue";
import SolutionUpload from "./role/solution-upload.vue";
import OperatorAvatar from '@/assets/avatar/teacher_default.jpg'

interface Props {
  order: Api.Ers.ServiceOrder;
}

interface Emits {
  (e: 'reload'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const auditFormDataRef = ref();
const paymentAmountSettingRef = ref();
const solutionUploadRef = ref();
const solutionSendRef = ref();

const actions = ref<object[]>([]);

function reload() {
  emit('reload');
}

function init() {
  const list = [];

  props.order?.flows.forEach(item => {
    item.sub_flows.forEach(subItem => {
      if (subItem.people && subItem.people.who === 1) {
        subItem.people.avatar = OperatorAvatar;
      }

      switch (subItem.action) {
        case 'form:audit':
          list.push({
            ...subItem,
            show: subItem.status === 1,
            action: () => {
              auditFormDataRef.value.open({
                order_id: props.order.id,
                step_id: item.id,
                form_data: item.data
              });
            }
          });
          break;
        case 'payment:set':
          if (item.module === 'payment_stage') {
            list.push({
              ...subItem,
              show: [0, 1, 2].includes(item.status),
              action: () => {
                paymentAmountSettingRef.value.open({
                  type: 'stage',
                  order_id: props.order.id,
                  step_id: item.id,
                  total_amount: item.data?.total_amount,
                  pay_amount: item.data?.pay_amount,
                });
              }
            });
          } else {
            list.push({
              ...subItem,
              show: [0, 1, 2].includes(item.status),
              action: () => {
                paymentAmountSettingRef.value.open({
                  type: 'default',
                  order_id: props.order.id,
                  step_id: item.id,
                  total_amount: item.data?.total_amount,
                  pay_amount: item.data?.pay_amount,
                });
              }
            });
          }
          break;
        case 'solution_preview:upload':
          list.push({
            ...subItem,
            show: [1, 2].includes(item.status),
            action: () => {
              solutionUploadRef.value.open({
                order_id: props.order.id,
                step_id: item.id,
                files: item.data?.files
              })
            }
          });
          break;
        case 'solution_download:done':
          list.push({
            ...subItem,
            show: subItem.status === 1,
            action: () => {
              solutionSendRef.value.open({
                order_id: props.order.id,
                step_id: item.id,
                email: item.data.email
              })
            }
          });
          break;
        default:
          list.push({
            ...subItem,
            show: false
          });
      }
    });
  });

  actions.value = list;
}

onMounted(() => {
  init();
});
</script>

<template>
  <NList>
    <NListItem v-for="(item, index) in actions" :key="index">
      <NThing>
        <template v-if="item.people" #avatar>
          <NAvatar circle :src="item.people.avatar" />
        </template>
        <template v-else #avatar>
          <NAvatar circle :src="OperatorAvatar" />
        </template>
        <template v-if="item.people" #header>
          <div class="font-size-3.8 font-600">{{ item.people.name }}</div>
        </template>
        <template v-else #header>
          <div class="font-size-3.8 font-600">暂无</div>
        </template>
        <template #header-extra>
          <NTag v-if="item.status === 1" type="primary" size="small" :bordered="false">
            待处理
            <template #icon>
              <NIcon><ClockCircleOutlined /></NIcon>
            </template>
          </NTag>
          <NTag v-else-if="item.status === 2" type="success" size="small" :bordered="false">
            已完成
            <template #icon>
              <NIcon><CheckmarkCircle /></NIcon>
            </template>
          </NTag>
        </template>
        <template  #description>
          <NSpace vertical :size="0">
            <div class="font-size-3 font-200">{{ item.desc }}</div>
            <div v-if="item.at" class="font-size-3 font-200"><DateFormat :date="item.at" /></div>
          </NSpace>
        </template>
        <template v-if="order.status !== 3 && item.show" #footer>
          <NSpace justify="center">
            <NButton type="primary" ghost @click="item.action">{{ item.desc }}</NButton>
          </NSpace>
        </template>
      </NThing>
    </NListItem>
  </NList>
  <AuditFormData ref="auditFormDataRef" @reload="reload" />
  <PaymentAmountSetting ref="paymentAmountSettingRef" @reload="reload" />
  <SolutionUpload ref="solutionUploadRef" @reload="reload" />
  <SolutionSend ref="solutionSendRef" @reload="reload" />
</template>

<style scoped></style>
