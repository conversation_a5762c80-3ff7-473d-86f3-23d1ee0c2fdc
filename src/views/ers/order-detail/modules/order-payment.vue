<script setup lang="tsx">
interface Props {
  data: object;
  totalAmount?: string;
}

const props = withDefaults(defineProps<Props>(), {
  totalAmount: ''
});

const paymentMethodLabel = {
  payment: '在线支付',
  balance: '余额支付',
}
</script>

<template>
  <NSpace vertical :size="0">
    <div v-if="totalAmount">总金额：{{ totalAmount }}</div>
    <div>付款金额：{{ data.pay_amount }}</div>
    <div>付款方式：{{ paymentMethodLabel[data.pay_order?.payment_method] }}</div>
    <div>付款单号：{{ data.pay_order?.order_no }}</div>
    <div>付款时间：<DateFormat :date="data.pay_order?.payment_at" /></div>
  </NSpace>
</template>

<style scoped></style>
