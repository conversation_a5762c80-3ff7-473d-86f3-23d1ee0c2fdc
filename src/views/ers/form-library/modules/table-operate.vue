<script setup lang="tsx">
import { computed, reactive, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { FormTypeOptions, FormTextTypeOptions } from '@/constants/business';
import { $t } from '@/locales';
import { createErsFormLibrary, updateErsFormLibrary } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

/**
 * the type of operation
 *
 * - add: add user
 * - edit: edit user
 */
export type OperateType = 'add' | 'edit';

const { loading, startLoading, endLoading } = useLoading();

interface Props {
  operateType: OperateType;
  rowData?: Api.Ers.FormLibrary | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: `新增组件`,
    edit: `编辑组件`
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Ers.FormLibrary, 'title' | 'type' | 'is_required' | 'desc' | 'options'>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    title: '',
    type: 'text',
    is_required: true,
    desc: '',
    options: {
      type: 'text',
      min: 0,
      max: 0
    }
  };
}

type RuleKey = Extract<keyof Model, 'title' | 'type' | 'options'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  title: defaultRequiredRule,
  type: defaultRequiredRule,
  options: defaultRequiredRule
};

async function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, {
      title: props.rowData.title,
      type: props.rowData.type,
      is_required: props.rowData.is_required,
      desc: props.rowData.desc,
      options: props.rowData.options
    });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  if (props.operateType === 'add') {
    const { error } = await createErsFormLibrary(model);
    endLoading();
    if(error) {
      return;
    }
    window.$message?.success($t('common.addSuccess'));
  } else {
    const { error } = await updateErsFormLibrary(props.rowData?.id as number, model);
    endLoading();
    if(error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  closeDrawer();
  emit('submitted');
}

function changeFormType(type) {
  switch (type) {
    case 'text':
      model.options = {
        type: 'text',
        min: 0,
        max: 0
      };
      break;
    case 'textarea':
      model.options = {
        type: 'text',
        min: 0,
        max: 0
      };
      break;
    case 'select':
    case 'checkbox':
      model.options = {
        options: []
      };
      break;
    case 'image':
      model.options = {
        size: 10240,
        limit: 1
      };
      break;
    case 'file':
      model.options = {
        size: 10240,
        ext: ['*'],
        limit: 1
      };
      break;
    default:
      model.options = null;
  }
}

function changeFormTextType(type) {
  switch (type) {
    case 'number':
      model.options = {
        type: 'number',
        min: 1,
        max: 100
      };
      break;
    case 'tel':
      model.options = {
        type: 'tel',
      };
      break;
    default:
      model.options = {
        type: 'text',
        min: 2,
        max: 30
      };
  }
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="720">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="120">
        <NFormItem label="组件名称" path="title" class="w-300px">
          <NInput v-model:value="model.title" />
        </NFormItem>
        <NFormItem label="组件类型" path="type" class="w-300px">
          <NSelect v-model:value="model.type" :disabled="!!props.rowData?.id" :options="FormTypeOptions" clearable @update:value="changeFormType" />
        </NFormItem>
        <NFormItem v-if="model.type === 'text'" label="类型" path="options.type" class="w-300px">
          <NRadioGroup v-model:value="model.options.type" :disabled="!!props.rowData?.id" clearable @update:value="changeFormTextType">
            <NRadio v-for="item in FormTextTypeOptions" :value="item.value" :label="item.label" :key="item.value" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem v-if="['text', 'textarea'].includes(model.type)" :label="model.options.type === 'text' ? '最小长度' : '最小值'" path="options.min" class="w-240px">
          <NInputNumber v-model:value="model.options.min" :min="0" />
        </NFormItem>
        <NFormItem v-if="['text', 'textarea'].includes(model.type)" :label="model.options.type === 'text' ? '最大长度' : '最大值'"  path="options.max" class="w-240px">
          <NInputNumber v-model:value="model.options.max" :min="0"  />
        </NFormItem>
        <NFormItem v-if="['select', 'checkbox'].includes(model.type)" label="选项"  path="options.limit" class="w-520px">
          <NDynamicTags v-model:value="model.options.options" />
        </NFormItem>
        <NFormItem v-if="model.type === 'file'"  label="文件类型" path="options.ext" class="w-520px">
          <NCheckboxGroup v-model:value="model.options.ext">
            <NCheckbox value="*" label="不限制"></NCheckbox>
            <NCheckbox value="doc" label="文档"></NCheckbox>
            <NCheckbox value="video" label="视频"></NCheckbox>
            <NCheckbox value="image" label="图片"></NCheckbox>
            <NCheckbox value="archive" label="压缩包"></NCheckbox>
          </NCheckboxGroup>
        </NFormItem>
        <NFormItem v-if="['image', 'file'].includes(model.type)" label="上传数量"  path="options.limit" class="w-260px">
          <NInputNumber v-model:value="model.options.limit">
            <template #suffix>个</template>
          </NInputNumber>
        </NFormItem>
        <NFormItem v-if="['image', 'file'].includes(model.type)" label="单个文件大小"  path="options.size" class="w-260px">
          <NInputNumber v-model:value="model.options.size">
            <template #suffix>KB</template>
          </NInputNumber>
        </NFormItem>
        <NFormItem v-if="model.type !== 'group'" label="是否必填" path="is_required">
          <NRadioGroup v-model:value="model.is_required">
            <NRadio :value="true" label="是" />
            <NRadio :value="false" label="否" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="组件描述" path="desc" class="w-420px">
          <NInput type="textarea" v-model:value="model.desc" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
