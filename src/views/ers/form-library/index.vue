<script setup lang="tsx">
import { ref } from 'vue';
import { N<PERSON><PERSON>on, NPopconfirm, NTag } from 'naive-ui';
import { fetchErsFormLibraryList, deleteErsFormLibrary } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useBoolean } from '@sa/hooks';
import { $t } from '@/locales';
import { FormTypeLabel } from '@/constants/business';
import TableSearch from './modules/table-search.vue';
import TableOperate, { type OperateType } from './modules/table-operate.vue';
import DateFormat from '@/components/common/date/date-format.vue';

defineOptions({
  name: 'ErsFormLibrary',
});

const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Ers.FormLibrary, typeof fetchErsFormLibraryList, 'index' | 'operate'>({
  apiFn: fetchErsFormLibraryList,
  apiParams: {
    current: 1,
    size: 20,
    title: null,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'title',
      title: '组件名称',
      align: 'center',
      width: 300
    },
    {
      key: 'type',
      title: '类型',
      align: 'center',
      width: 100,
      render: row => {
        return FormTypeLabel[row.type];
      }
    },
    {
      key: 'is_required',
      title: '是否必填',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          true: 'error',
          false: 'default'
        };

        const label = row.is_required ? '是' : '否';

        return (
          <NTag type={tagMap[row.is_required]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '修改时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 100,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row)}>
            编辑
          </NButton>

          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const checkedRowKeys = ref<number[]>([]);

const operateType = ref<OperateType>('add');

/** the editing row data */
const editingData = ref<Api.Ers.FormLibrary | null>(null);

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;
  openDrawer();
}

function handleEdit(row: Api.Ers.FormLibrary) {
  operateType.value = 'edit';
  editingData.value = row;
  openDrawer();
}

async function handleDelete(id: number) {
  const { error } = await deleteErsFormLibrary(id)
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    question_id: null
  });
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="组件列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-batch-delete="false"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
      <TableOperate
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
