<script setup lang="ts">
import { $t } from '@/locales';
import { enableStatusOptions } from '@/constants/business';
import { translateOptions } from '@/utils/common';
import { fetchGetAllRoles } from '@/service/api';
import { ref } from "vue";

defineOptions({
  name: 'TableSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type AdminSearchParams = CommonType.RecordNullable<
  Pick<Api.SystemManage.Admin, 'username' | 'status'> & Api.SystemManage.CommonSearchParams & {
  role_id: number;
  date: number;
}
>;

const emit = defineEmits<Emits>();

const model = defineModel<AdminSearchParams>('model', { required: true });

const roles = ref<Api.SystemManage.Role[]>([]);

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}


async function init() {
  const { data, error } = await fetchGetAllRoles({ status: 1 });
  if (error) {
    return;
  }

  roles.value = data;
}

init();
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:4" label="用户名" path="username" class="pr-24px">
          <NInput v-model:value="model.username" placeholder="输入用户名" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="状态" path="enable" class="pr-24px">
          <NSelect v-model:value="model.status" :options="translateOptions(enableStatusOptions)" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="角色" path="enable" class="pr-24px">
          <NSelect v-model:value="model.role_id" :options="roles" label-field="name" value-field="id" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:5" label="日期" path="enable" class="pr-24px">
          <NDatePicker v-model:value="model.date" type="daterange" value-format="yyyy-MM-dd"  />
        </NFormItemGi>
        <NFormItemGi span="24 s:3">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
