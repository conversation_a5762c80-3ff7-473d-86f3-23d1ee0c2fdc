<script setup lang="tsx">
import { N<PERSON><PERSON>on, NTag } from 'naive-ui';
import { fetchPromoterList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { enableStatusRecord } from '@/constants/business';
import { useRouterPush } from '@/hooks/common/router';
import DateFormat from '@/components/common/date/date-format.vue';
import TableSearch from './modules/table-search.vue';

const { routerPushByKey } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.SystemManage.Admin, typeof fetchPromoterList, 'index' | 'operate'>({
  apiFn: fetchPromoterList,
  apiParams: {
    current: 1,
    size: 20,
    username: null,
    status: null,
    role_id: null,
    date: null,
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'username',
      title: '用户名',
      align: 'center',
      width: 100,
    },
    {
      key: 'real_name',
      title: '姓名',
      align: 'center',
      width: 80
    },
    {
      key: 'phone',
      title: '手机号码',
      align: 'center',
      width: 120
    },
    {
      key: 'stats.content_material' as any,
      title: '上传资料',
      align: 'center',
      width: 100,
      render: row => {
        return row.stats ? row.stats.content_material : 0;
      }
    },
    {
      key: 'stats.content_course' as any,
      title: '上传课程',
      align: 'center',
      width: 100,
      render: row => {
        return row.stats ? row.stats.content_course : 0;
      }
    },
    {
      key: 'stats.content_special',
      title: '内容专题',
      align: 'center',
      width: 100,
      render: row => {
        return row.stats ? row.stats.content_special : 0;
      }
    },
    {
      key: 'stats.consume_credit' as any,
      title: '消耗积分',
      align: 'center',
      width: 100,
      render: row => {
        return row.stats ? row.stats.consume_credit : 0;
      }
    },
    {
      key: 'stats.payment_amount' as any,
      title: '成交金额',
      align: 'center',
      width: 100,
      render: row => {
        return row.stats ? parseFloat(row.stats.payment_amount) : 0;
      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 80,
      render: row => {
        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        const label = $t(enableStatusRecord[row.status]);

        return (
          <NTag type={tagMap[row.status]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 100,
      render: row => (
        <NButton type="primary" ghost size="small" onClick={() => goStat(row.id)}>
          数据概况
        </NButton>
      )
    }
  ]
});

function goStat(id: number) {
  routerPushByKey('stat_index', { query: { id: id.toString() } });
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="用户列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :show-add="false"
          :loading="loading"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
