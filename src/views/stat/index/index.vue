<script setup lang="ts">
import { onBeforeMount } from "vue";
import { useTabStore } from "@/store/modules/tab";
import { useRouterPush } from "@/hooks/common/router";
import { getAdmin } from '@/service/api';
import GuestStatus from '../../home/<USER>/stats.vue';

const tabStore = useTabStore();
const { route, routerBack } = useRouterPush();

const adminId = route.value.query.id ? parseInt(route.value.query.id as string) : 0;

async function init(id: number) {
  const { data } = await getAdmin(id);

  if (data) {
    const name = data.real_name ? data.real_name : data.username;
    tabStore.setTabLabel(`${name} 数据概况`);
  }
}

onBeforeMount(() => {
  if (!adminId) {
    window.$message?.warning('参数错误');
    routerBack();

    return;
  }

  init(adminId);
});
</script>

<template>
  <GuestStatus :admin-id="adminId" />
</template>

<style scoped></style>
