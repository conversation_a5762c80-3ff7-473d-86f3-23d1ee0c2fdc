<script setup lang="ts">
import {ref} from "vue";
import {useTable} from "@/hooks/common/table";
import {getRankList} from "@/service/api";

defineOptions({
  name: 'DownloadRank'
});

const checkedRowKeys = ref<string[]>([]);

const {
  columns,
  data,
  loading,
} = useTable<Api.Stat.Search, typeof getRankList, 'index' | 'operate'>({
  apiFn: getRankList,
  apiParams: {
    type: 'download'
  },
  transformer: res => {
    res.data.forEach((item, index) => {
      item.rank = index + 1
    });

    return {
      data: res.data
    };
  },
  columns: () => [
    {
      key: 'rank',
      title: '排名',
      align: 'center',
      width: 30
    },
    {
      key: 'content.title' as any,
      title: '资料名称',
      align: 'center',
      width: 30
    },
    {
      key: 'download_count' as any,
      title: '下载数量',
      align: 'center',
      width: 30
    }
  ]
});
</script>

<template>
  <NCard title="资料下载总排行榜" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
    <NDataTable
      v-model:checked-row-keys="checkedRowKeys"
      :columns="columns"
      :data="data"
      size="small"
      :loading="loading"
      remote
      :row-key="item => item.id"
      class="sm:h-full"
    />
  </NCard>
</template>

<style scoped>

</style>
