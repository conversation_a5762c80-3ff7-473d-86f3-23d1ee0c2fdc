<script setup lang="ts">
import {useTable} from "@/hooks/common/table";
import {getRankList} from "@/service/api";
import {ref} from "vue";

defineOptions({
  name: 'SearchRank'
});

const checkedRowKeys = ref<string[]>([]);

const {
  columns,
  data,
  loading,
} = useTable<Api.Stat.Search, typeof getRankList, 'index' | 'operate'>({
  apiFn: getRankList,
  apiParams: {
    type: 'search'
  },
  transformer: res => {
    res.data.forEach((item, index) => {
      item.rank = index + 1
    });

    return {
      data: res.data
    };
  },
  columns: () => [
    {
      key: 'rank',
      title: '排名',
      align: 'center',
      width: 30
    },
    {
      key: 'keyword',
      title: '关键词',
      align: 'center',
      width: 30
    },
    {
      key: 'search_count',
      title: '搜索数量',
      align: 'center',
      width: 30
    }
  ]
});
</script>

<template>
  <div ref="wrapperRef">
    <NCard title="搜索关键词总排行榜" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :loading="loading"
        remote
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped>

</style>
