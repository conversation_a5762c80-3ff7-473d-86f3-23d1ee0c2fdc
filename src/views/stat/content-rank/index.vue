<script setup lang="ts">

import DownloadRank from './modules/download.vue';
import SearchRank from './modules/search.vue';
import Rank from './modules/rank.vue';
interface Props {
  adminId?: number;
}

defineProps<Props>();
</script>

<template>
  <div ref="wrapperRef">
    <NGrid :cols="3"  x-gap="24">
      <NGridItem>
        <SearchRank />
      </NGridItem>
      <NGridItem>
        <DownloadRank />
      </NGridItem>
      <NGridItem>
        <Rank :admin-id="adminId" />
      </NGridItem>
    </NGrid>
  </div>
</template>

<style scoped></style>
