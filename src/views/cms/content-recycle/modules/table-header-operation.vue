<script setup lang="ts">
import type { FilteredColumn } from '@/hooks/common/table';

defineOptions({
  name: 'MaterialTableHeaderOperation'
});


interface Props {
  itemAlign?: NaiveUI.Align;
  showBatchDelete?: boolean,
  disabledDelete?: boolean;
  loading?: boolean;
}

withDefaults(defineProps<Props>(), {
  showBatchDelete: true
});

interface Emits {
  (e: 'delete'): void;
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<FilteredColumn[]>('columns', {
  default: () => []
});

function batchDelete() {
  emit('delete');
}

function refresh() {
  emit('refresh');
}

</script>

<template>
  <NSpace :align="itemAlign" wrap justify="end" class="<sm:w-200px">
    <slot name="prefix"></slot>
    <slot name="default">
      <NPopconfirm v-if="showBatchDelete" @positive-click="batchDelete">
        <template #trigger>
          <NButton size="small" ghost type="error" :disabled="disabledDelete">
            <template #icon>
              <icon-ic-round-delete class="text-icon" />
            </template>
            {{ $t('common.batchDelete') }}
          </NButton>
        </template>
        {{ $t('common.confirmDelete') }}
      </NPopconfirm>
    </slot>
    <NButton size="small" @click="refresh">
      <template #icon>
        <icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loading }" />
      </template>
      {{ $t('common.refresh') }}
    </NButton>
    <TableColumnSetting v-model:columns="columns" />
    <slot name="suffix"></slot>
  </NSpace>
</template>

<style scoped></style>
