<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { $t } from '@/locales';
import DatetimeRange from '@/components/common/date/datetime-range.vue';
import { fetchCategoryList } from '@/service/api';
import { CascaderOption } from 'naive-ui';

defineOptions({
  name: 'MaterialSearch'
});

interface Props {
  classify: Api.Cms.CategoryClassifyType;
}

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const props = defineProps<Props>();

type SearchParams = CommonType.RecordNullable<Api.SystemManage.CommonSearchParams & {
  id: string;
  category_id: number;
  keyword: string;
}>;

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

const categoryList = ref<Api.Cms.Category[]>([]);


function reset() {
  emit('reset');
}

function search() {
  emit('search');
}

onMounted(async () => {
  const { data, error } = await fetchCategoryList({ classify: props.classify });

  if (!error) {
    categoryList.value = data;
  }
})
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:4" label="ID" path="id" class="pr-24px">
          <NInput v-model:value="model.id" placeholder="输入ID" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="关键词" path="keyword" class="pr-24px">
          <NInput v-model:value="model.keyword" placeholder="输入搜索关键词" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="选择分类" path="category_id" class="pr-24px">
          <NCascader
            v-model:value="model.category_id"
            :options="(categoryList as CascaderOption[])"
            value-field="id"
            label-field="name"
            check-strategy="child"
            clearable
          />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:7" label="创建时间" path="created_at" class="pr-24px">
          <DatetimeRange v-model:value="model.created_at"  />
        </NFormItemGi>
        <NFormItemGi span="24 s:5">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
