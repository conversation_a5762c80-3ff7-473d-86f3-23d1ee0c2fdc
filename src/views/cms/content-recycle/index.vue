<script setup lang="tsx">
import { ref } from 'vue';
import { <PERSON><PERSON><PERSON><PERSON>, NPopconfirm, NTag } from 'naive-ui';
import { fetchContentList, batchDeleteContent } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { contentTypeLabel } from '@/constants/business';
import { useRouterPush } from '@/hooks/common/router';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnEllipsis from '@/components/common/table/column-ellipsis.vue';
import TableHeaderOperation from './modules/table-header-operation.vue';

const { route } = useRouterPush();

const classify = ref<Api.Cms.CategoryClassifyType>(route.value.query.classify ? route.value.query.classify as Api.Cms.CategoryClassifyType : 'material');

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Cms.Content, typeof fetchContentList, 'index' | 'operate'>({
  apiFn: fetchContentList,
  apiParams: {
    current: 1,
    size: 20,
    id: null,
    keyword: null,
    classify: classify.value,
    category_id: null,
    is_destroy: 1,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'category_id',
      title: '所属分类',
      align: 'center',
      minWidth: 150,
      render: row => {
        return row.category?.name;
      }
    },
    {
      key: 'title',
      title: '标题',
      align: 'center',
      minWidth: 260,
      render: row => {
        return <ColumnEllipsis text={row.title} />;
      }
    },
    {
      key: 'type',
      title: '类型',
      align: 'center',
      minWidth: 150,
      render: row => {
        return <NTag type='info' bordered={false}>{contentTypeLabel[row.type]}</NTag>;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 150,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'deleted_at',
      title: '删除时间',
      align: 'center',
      minWidth: 150,
      render: row => {
        return <DateFormat date={row.deleted_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      minWidth: 80,
      render: row => (
        <div class="flex-center gap-8px">
          <NPopconfirm onPositiveClick={() => handleDelete(row)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const checkedRowKeys = ref<string[]>([]);

async function handleBatchDelete() {
  const { error } = await batchDeleteContent({ ids: checkedRowKeys.value, force: 1 })
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  checkedRowKeys.value = [];

  await getData();
}

async function handleDelete(row: Api.Cms.Content) {
  const { error } = await batchDeleteContent({ ids: [row.id], force: 1})
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" :classify="classify" @reset="resetSearchParams" @search="getData" />
    <NCard title="回收站" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
