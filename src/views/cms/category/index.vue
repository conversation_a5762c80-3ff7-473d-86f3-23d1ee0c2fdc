<script setup lang="tsx">
import { ref } from 'vue';
import { <PERSON><PERSON><PERSON>on, NPopconfirm, NTag } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchCategoryList, deleteCategory, updateCategory } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { displayLabel, categoryClassifyLabel } from '@/constants/business';
import CategoryOperateDrawer, { type OperateType } from './modules/category-operate-drawer.vue';
import ImageSingle from '@/components/common/image/image-single.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import OperateSort from '@/components/common/table/operate-sort.vue';
import HotPosition from './modules/hot-position.vue';

const appStore = useAppStore();
const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();

const wrapperRef = ref<HTMLElement | null>(null);
const tab = ref<Api.Cms.CategoryClassifyType>('material');
const expandedRowKeys = ref<number[]>([]);

const selectedPositions = ref<number[]>([]);

const { columns, filteredColumns, data, loading, getData, updateSearchParams } = useTable<
  Api.Cms.Category,
  typeof fetchCategoryList,
  'index' | 'operate'
>({
  apiFn: fetchCategoryList,
  apiParams: {
    classify: null
  },
  immediate: false,
  transformer: res => {
    const categories = res.data || [];

    categories.forEach(item => {
      expandedRowKeys.value.push(item.id);
    })

    return {
      data: categories,
      pageNum: 1,
      pageSize: 10,
      total: categories.length
    };
  },
  columns: () => [
    {
      key: 'name',
      title: '名称',
      align: 'left',
      width: 240,
    },
    {
      key: 'logo_src',
      title: '图标',
      align: 'center',
      width: 80,
      render: row => {
        return <ImageSingle src={row.logo_src} width={48} />
      }
    },
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      width: 120,
    },
    {
      key: 'visible',
      title: '是否显示',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        const label = displayLabel[row.visible];

        return <NTag type={tagMap[row.visible]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'sort',
      title: '排序',
      align: 'center',
      width: 100,
      render: row => {
        return <OperateSort id={row.id} sort={row.sort} updateSort={updateCategory} onReset={getData} />
      }
    },
    {
      key: 'hot_position',
      title: '热门分类位置',
      align: 'center',
      width: 100,
      render: row => {
        return <HotPosition id={row.id} value={row.hot_position} selectedPositions={selectedPositions.value} onUpdateValue={updatePosition} />
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 260,
      render: row => {
        return <div class="flex-center justify-end gap-8px mr-15px">
          {!(row.cascades_value && row.cascades_value.length >= 3) &&
            <NButton type="primary" ghost size="small" onClick={() => handleAddChild(row)}>
              新增子分类
            </NButton>
          }
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      }
    }
  ]
});

const operateType = ref<OperateType>('add');

/** the editing row data */
const editingData = ref<Api.Cms.Category | null>(null);

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;
  openDrawer();
}

const checkedRowKeys = ref<string[]>([]);

function handleAddChild(row: Api.Cms.Category) {
  operateType.value = 'add';
  editingData.value = row;
  openDrawer();
}

function handleEdit(row: Api.Cms.Category) {
  operateType.value = 'edit';
  editingData.value = row;
  openDrawer();
}

async function handleDelete(id: number) {
  const { error } = await deleteCategory(id);
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  refresh();
}

function handleTabValue(value: any) {
  tab.value = value;

  updateSearchParams({
    classify: tab.value
  });

  refresh();
}

function fetchAllHotPositions(rows: Api.Cms.Category[]): number[] {
  const positions: number[] = [];

  rows.forEach(row => {
    if (row.hot_position > 0) {
      positions.push(row.hot_position);
    }
    if (row.children && row.children.length > 0) {
      positions.push(...fetchAllHotPositions(row.children));
    }
  });

  return positions;
}

async function updatePosition(value: number, id: number) {
  await updateCategory(id, {hot_position: value});
  await refresh();
}

/**
 * 刷新数据并执行一些当前页面依赖数据更新后的逻辑
 */
async function refresh() {
  await getData();
  selectedPositions.value = fetchAllHotPositions(data.value);
}

refresh();
</script>

<template>
  <div ref="wrapperRef">
    <NCard title="分类列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :loading="loading"
          @add="handleAdd"
          @refresh="refresh"
        />
      </template>
      <NSpace justify="center">
        <NTabs type="segment" animated @update:value="handleTabValue" class="w-320px">
          <NTabPane name="material" :tab="categoryClassifyLabel.material">
          </NTabPane>
          <NTabPane name="news" :tab="categoryClassifyLabel.news">
          </NTabPane>
          <NTabPane name="course" :tab="categoryClassifyLabel.course">
          </NTabPane>
        </NTabs>
      </NSpace>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="1088"
        :min-height="560"
        :loading="loading"
        :row-key="item => item.id"
        :indent="48"
        :default-expanded-row-keys="expandedRowKeys"
        class="sm:h-full"
      />
      <CategoryOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        :classify="tab"
        @submitted="getData"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
