<script setup lang="tsx">
import { computed, reactive, ref, watch } from 'vue';
import { CascaderOption } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { categoryClassifyLabel, contentTypeOptions } from '@/constants/business';
import { fetchCategoryList, createCategory, updateCategory } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

defineOptions({
  name: 'CategoryOperateDrawer'
});

/**
 * the type of operation
 *
 * - add: add user
 * - edit: edit user
 */
export type OperateType = 'add' | 'edit';

const { loading, startLoading, endLoading } = useLoading();

interface Props {
  operateType: OperateType;
  classify: Api.Cms.CategoryClassifyType;
  rowData?: Api.Cms.Category | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const parentList = ref<Api.Cms.Category[]>([]);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: `新增${categoryClassifyLabel[props.classify]}分类`,
    edit: `编辑${categoryClassifyLabel[props.classify]}分类`
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Cms.Category, 'name' | 'intro' | 'logo' | 'pid' | 'classify' | 'visible' | 'allow_types' | 'sort'>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    intro: '',
    logo: '',
    pid: 0,
    classify: props.classify,
    visible: 1,
    allow_types: [],
    sort: 0
  };
}

type RuleKey = Extract<keyof Model, 'name'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule
};


async function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    if (props.rowData) {
      Object.assign(model, { ...createDefaultModel(), pid: props.rowData.id });
    } else {
      Object.assign(model, createDefaultModel());
    }
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, {
      name: props.rowData.name,
      intro: props.rowData.intro,
      logo: props.rowData.logo,
      pid: props.rowData.pid,
      classify: props.rowData.classify,
      visible: props.rowData.visible,
      allow_types: props.rowData.allow_types,
      sort: props.rowData.sort
    });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  if (props.operateType === 'add') {
    const { error } = await createCategory(model);

    endLoading();

    if(error) {
      return;
    }
    window.$message?.success($t('common.addSuccess'));
  } else {
    const { error } = await updateCategory(props.rowData?.id as number, model);

    endLoading();

    if(error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  closeDrawer();
  emit('submitted');
}

async function getParentList() {
  const { data: list, error } = await fetchCategoryList({ classify: props.classify });
  if(error) {
    return;
  }

  parentList.value = list;
  parentList.value.unshift({
    id: 0,
    name: '顶级分类'
  } as Api.Cms.Category);
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
    getParentList();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="680">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem label="选择父类" path="pid" class="w-420px">
          <NCascader
            v-model:value="model.pid"
            :options="parentList"
            value-field="id"
            label-field="name"
            check-strategy="child"
          />
        </NFormItem>
        <NFormItem label="名称" path="name" class="w-360px">
          <NInput v-model:value="model.name" />
        </NFormItem>
        <NFormItem label="简介" path="intro" class="w-420px">
          <NInput type="textarea" v-model:value="model.intro" :rows="5" />
        </NFormItem>
        <NFormItem label="图标" path="logo">
          <UploadImage v-model:value="model.logo" :preview="rowData?.logo_src" />
        </NFormItem>
        <NFormItem label="是否显示" path="visible">
          <NRadioGroup v-model:value="model.visible">
            <NRadio :value="1" label="显示" />
            <NRadio :value="0" label="隐藏" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="排序" path="sort" class="w-210px">
          <NSpace vertical>
            <NInputNumber v-model:value="model.sort" :min="0" :max="10000" />
            <FormTips :tips="['数字越大越靠前']" />
          </NSpace>
        </NFormItem>
        <NFormItem v-if="0" label="内容类型" path="allow_types">
          <NCheckboxGroup v-model:value="model.allow_types">
            <NSpace item-style="display: flex;">
              <NCheckbox v-for="item in contentTypeOptions" :key="item.value" :value="item.value" :label="item.label" />
            </NSpace>
          </NCheckboxGroup>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
