<script setup lang="tsx">
import { computed } from 'vue';
import { SelectMixedOption } from "naive-ui/es/select/src/interface";

interface Props {
  id: number,
  value: number;
  selectedPositions: number[];
}

const props = defineProps<Props>();

interface Emits {
  (e: 'updateValue', value: number, id: number): void;
}

const emit = defineEmits<Emits>();

const positions = [
  { label: '-', value: 0 },
  { label: '2', value: 1 },
  { label: '3', value: 2 },
  { label: '4', value: 3 },
  { label: '5', value: 4 },
  { label: '6', value: 5 },
  { label: '7', value: 6 },
  { label: '8', value: 7 },
];

const hotPositionOptions = computed<SelectMixedOption[]>(() => {
  return positions.map((item) => {
    return {
      label: item.label,
      value: item.value,
      disabled: item.value !=0 && props.selectedPositions.includes(item.value) && props.value != item.value,
    };
  });
});
</script>

<template>
  <NSelect :options="hotPositionOptions" :value="value" @update-value="$emit('updateValue', $event, id)" />
</template>
