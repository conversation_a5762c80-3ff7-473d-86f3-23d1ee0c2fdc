<script setup lang="tsx">
import { ref } from 'vue';
import { N<PERSON><PERSON>on, NPopconfirm, NTag } from 'naive-ui';
import { fetchSpecialList, deleteSpecial, getSpecial } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useBoolean } from '@sa/hooks';
import { $t } from '@/locales';
import { displayLabel } from '@/constants/business';
import TableSearch from './table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import OperateDrawer, { type OperateType } from './operate-drawer.vue';
import ImageSingle from '@/components/common/image/image-single.vue';
import ColumnEllipsis from '@/components/common/table/column-ellipsis.vue';

const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Cms.Special, typeof fetchSpecialList, 'index' | 'operate'>({
  apiFn: fetchSpecialList,
  apiParams: {
    current: 1,
    size: 20,
    id: null,
    name: null,
    status: null,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 100
    },
    {
      key: 'cover',
      title: '封面',
      align: 'center',
      width: 120,
      render: row => {
        return <ImageSingle src={row.cover_src} width={100} />
      }
    },
    {
      key: 'name',
      title: '标题',
      align: 'center',
      minWidth: 200
    },
    {
      key: 'intro',
      title: '简介',
      align: 'center',
      minWidth: 200,
      render: row => {
        return <ColumnEllipsis text={row.intro} line={2} />;
      }
    },
    {
      key: 'charge_credit',
      title: '积分价格',
      align: 'center',
      width: 120
    },
    {
      key: 'status',
      title: '是否显示',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        const label = displayLabel[row.status];

        return <NTag type={tagMap[row.status]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      minWidth: 160,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const operateType = ref<OperateType>('add');

/** the editing row data */
const editingData = ref<Api.Cms.Special | null>(null);

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;
  openDrawer();
}

const checkedRowKeys = ref<string[]>([]);

async function handleEdit(row: Api.Cms.Special) {
  const { data, error } = await getSpecial(row.id);
  if (error) {
    return;
  }

  operateType.value = 'edit';
  editingData.value = data;
  openDrawer();
}

async function handleDelete(id: number) {
  const { error } = await deleteSpecial(id);
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="专题列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        :is-manage="false"
        @submitted="getData"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
