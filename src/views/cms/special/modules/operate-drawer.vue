<script setup lang="tsx">
import { computed, reactive, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { createSpecial, updateSpecial } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

defineOptions({
  name: 'SpecialOperateDrawer'
});

/**
 * the type of operation
 *
 * - add: add user
 * - edit: edit user
 */
export type OperateType = 'add' | 'edit';

const { loading, startLoading, endLoading } = useLoading();

interface Props {
  operateType: OperateType;
  rowData: Api.Cms.Special | null;
  isManage: boolean;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: `新增专题`,
    edit: `编辑专题`
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Cms.Special, 'name' | 'intro' | 'cover' | 'status' | 'charge_credit'> & {
  recommend: number;
  content_ids: number[];
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    intro: '',
    cover: '',
    status: 1,
    charge_credit: 30,
    recommend: 0,
    content_ids: []
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'cover' | 'charge_credit' | 'content_ids'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  cover: defaultRequiredRule,
  charge_credit: defaultRequiredRule,
  content_ids: defaultRequiredRule,
};


async function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    let contentIds: number[] = [];
    if (props.rowData?.contents && props.rowData.contents.length > 0) {
      props.rowData.contents.forEach(item => {
        contentIds.push(item.content_id);
      });
    }

    Object.assign(model, { ...props.rowData, recommend: props.rowData.recommend_at ? 1 : 0, content_ids: contentIds });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  if (props.operateType === 'add') {
    const { error } = await createSpecial(model);

    endLoading();

    if(error) {
      return;
    }
    window.$message?.success($t('common.addSuccess'));
  } else {
    const { error } = await updateSpecial(props.rowData?.id as number, model);

    endLoading();

    if(error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="680">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem label="标题" path="name" class="w-360px">
          <NInput v-model:value="model.name" />
        </NFormItem>
        <NFormItem label="封面" path="cover">
          <UploadImage v-model:value="model.cover" :preview="rowData?.cover_src" />
        </NFormItem>
        <NFormItem label="简介" path="intro" class="w-480px">
          <NInput type="textarea" v-model:value="model.intro" :rows="6" />
        </NFormItem>
        <NFormItem label="积分价格" path="charge_credit" class="w-220px">
          <NInputNumber v-model:value="model.charge_credit" :min="0" />
        </NFormItem>
        <NFormItem v-if="isManage" label="推荐" path="recommend">
          <NSwitch v-model:value="model.recommend" :checked-value="1" :unchecked-value="0"></NSwitch>
        </NFormItem>
        <NFormItem label="是否显示" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio :value="1" label="显示" />
            <NRadio :value="0" label="隐藏" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="关联资料" path="content_ids">
          <ContentRelation v-model:value="model.content_ids" :admin-id="rowData?.admin_id" classify="material" draggable />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
