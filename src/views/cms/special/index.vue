<script setup lang="tsx">
import { ref } from 'vue';
import { checkCmsPermission } from '@/utils/common';
import TableManage from './modules/table-manage.vue';
import TableGeneral from './modules/table-general.vue';

const permission = ref<boolean>(checkCmsPermission());
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableManage v-if="permission" />
    <TableGeneral v-else />
  </div>
</template>

<style scoped></style>
