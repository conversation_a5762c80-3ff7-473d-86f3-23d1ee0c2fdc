<script setup lang="ts">
import type { FilteredColumn } from '@/hooks/common/table';
import { ref } from 'vue';
import { LicenseDraft } from '@vicons/carbon';
import { LoopOutlined } from '@vicons/material';
import { useRouterPush } from '@/hooks/common/router';

defineOptions({
  name: 'MaterialTableHeaderOperation'
});

const { routerPushByKey } = useRouterPush();

interface Props {
  classify: Api.Cms.CategoryClassifyType;
  itemAlign?: NaiveUI.Align;
  showBatchDelete?: boolean,
  disabledDelete?: boolean;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showBatchDelete: true
});

interface Emits {
  (e: 'delete'): void;
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<FilteredColumn[]>('columns', {
  default: () => []
});

const addOptions = ref([
  {
    label: '文档',
    key: '1'
  },
  {
    label: '视频',
    key: '3'
  },
]);

function add(key: string) {
  routerPushByKey('cms_content', { query: { classify: props.classify, type: key } }, true)
}

function batchDelete() {
  emit('delete');
}

function refresh() {
  emit('refresh');
}

function draft() {
  routerPushByKey('cms_content-draft', { query: { classify: props.classify } });
}

function recycle() {
  routerPushByKey('cms_content-recycle', { query: { classify: props.classify } });
}
</script>

<template>
  <NSpace :align="itemAlign" wrap justify="end" class="<sm:w-200px">
    <slot name="prefix"></slot>
    <slot name="default">
      <NButton size="small" ghost type="default" @click="draft">
        <template #icon>
          <LicenseDraft class="text-icon" />
        </template>
        草稿
      </NButton>
      <NButton size="small" ghost type="warning" @click="recycle">
        <template #icon>
          <LoopOutlined class="text-icon" />
        </template>
        回收站
      </NButton>
      <NDropdown :options="addOptions" @select="add">
        <NButton size="small" ghost type="primary">
          <template #icon>
            <icon-ic-round-plus class="text-icon" />
          </template>
          {{ $t('common.add') }}
        </NButton>
      </NDropdown>
      <NPopconfirm v-if="showBatchDelete" @positive-click="batchDelete">
        <template #trigger>
          <NButton size="small" ghost type="error" :disabled="disabledDelete">
            <template #icon>
              <icon-ic-round-delete class="text-icon" />
            </template>
            {{ $t('common.batchDelete') }}
          </NButton>
        </template>
        {{ $t('common.confirmDelete') }}
      </NPopconfirm>
    </slot>
    <NButton size="small" @click="refresh">
      <template #icon>
        <icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loading }" />
      </template>
      {{ $t('common.refresh') }}
    </NButton>
    <TableColumnSetting v-model:columns="columns" />
    <slot name="suffix"></slot>
  </NSpace>
</template>

<style scoped></style>
