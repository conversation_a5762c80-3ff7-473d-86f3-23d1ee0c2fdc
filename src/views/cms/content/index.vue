<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useBoolean } from '@sa/hooks';
import { useRouterPush } from '@/hooks/common/router';
import { fetchCategoryList, getContent } from '@/service/api';
import { categoryClassifyLabel } from '@/constants/business';
import { useTabStore } from '@/store/modules/tab';
import { closeTab } from '@/utils/tab';
import OperateContent from './modules/operate-content.vue';

const tabStore = useTabStore();
const { route } = useRouterPush();
const { bool: formVisible, setTrue: showForm } = useBoolean();

const classify = ref<Api.Cms.CategoryClassifyType>();
const contentType = ref<Api.Cms.ContentType>();
const categoryList = ref<Api.Cms.Category[]>([]);
const contentDetail = ref<Api.Cms.Content>();

async function initFetchCategory(type: Api.Cms.CategoryClassifyType) {
  const { data, error } = await fetchCategoryList({ classify: type, visible: 1 });

  if (error) {
    window.$message?.success('请先创建分类');
    await handleError();
    return;
  }

  categoryList.value = data;
}

async function initFetchContent(id: string) {
  const { data, error } = await getContent(Number.parseInt(id, 10));

  if (error) {
    window.$message?.success('内容不存在');
    await handleError();
    return;
  }

  contentDetail.value = data;

  classify.value = data.category?.classify;
  contentType.value = data.type;
}

async function handleError() {
  await closeTab(route.value.name as string);
}

onMounted(async () => {
  if (route.value.query.classify) {
    classify.value = route.value.query.classify as Api.Cms.CategoryClassifyType;
  }

  if (route.value.query.type) {
    contentType.value = parseInt(route.value.query.type as string) as Api.Cms.ContentType;
  }

  if (route.value.query.id) {
    await initFetchContent(route.value.query.id as string);
  }

  if (!classify.value || !contentType.value) {
    window.$message?.warning('参数错误');
    await handleError();
    return;
  }

  tabStore.setTabLabel((contentDetail.value ? '编辑' : '新增') + categoryClassifyLabel[classify.value]);

  await initFetchCategory(classify.value);

  showForm();
})
</script>

<template>
  <NSpace vertical :size="16">
    <NCard>
      <OperateContent
        v-if="formVisible"
        :classify="(classify as Api.Cms.CategoryClassifyType)"
        :content-type="(contentType as Api.Cms.ContentType)"
        :categories="categoryList"
        :row-data="contentDetail"
      />
      <NSpace v-else vertical class="pl-15%">
        <NSkeleton class="skeleton" height="80px" width="60%" />
        <NSkeleton class="skeleton" height="40px" width="60%"  />
        <NSkeleton class="skeleton" height="40px" width="46%" />
        <NSkeleton class="skeleton" height="140px" width="20%"  />
        <NSkeleton class="skeleton" height="200px" width="50%" />
        <NSkeleton class="skeleton" height="40px" width="50%" />
        <NSkeleton class="skeleton" height="40px" width="24%" />
        <NSkeleton class="skeleton" height="40px" width="50%" />
        <NSkeleton class="skeleton" height="60px" width="30%" />
      </NSpace>
    </NCard>
  </NSpace>
</template>

<style scoped>
.skeleton {
  --n-color-start: #fafafa !important;
  --n-color-end: #fdfbfb !important;
}
</style>
