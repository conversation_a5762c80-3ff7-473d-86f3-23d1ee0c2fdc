<script setup lang="ts">
import { reactive, ref } from 'vue';
import { $t } from '@/locales';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { createContentCourseChapter, updateContentCourseChapter } from '@/service/api';

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const active = ref(false);
const saveLoading = ref<boolean>(false);

type Model = Pick<Api.Cms.ContentCourseChapter, 'id' | 'content_id' | 'name' | 'sort' | 'status'>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: 0,
    content_id: 0,
    name: '',
    sort: 0,
    status: 1
  };
}

type RuleKey = Extract<keyof Model, 'name'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule
};

function open(contentId: number, data?: Api.Cms.ContentCourseChapter) {
  if (data) {
    Object.assign(model, {
      id: data.id,
      content_id: data.content_id,
      name: data.name,
      sort: data.sort,
      status: data.status
    });
  } else {
    Object.assign(model, { ...createDefaultModel(), content_id: contentId });
  }

  active.value = true;
}

async function handleSubmit() {
  await validate();

  saveLoading.value = true;

  if (model.id) {
    const { error } = await updateContentCourseChapter(model.id, model);

    saveLoading.value = false;

    if (error) {
      return;
    }

    window.$message?.success($t('common.updateSuccess'));
  } else {
    const { error } = await createContentCourseChapter(model);

    saveLoading.value = false;

    if (error) {
      return;
    }

    window.$message?.success($t('common.addSuccess'));
  }

  await restoreValidation();

  emit('reload');

  active.value = false;
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="active" :width="420">
    <NDrawerContent>
      <template #header>{{ model.id ? '修改章' : '新建章' }}</template>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="60">
        <NFormItem label="名称" path="name" class="w-320px">
          <NInput v-model:value="model.name" placeholder="请输入章名称" />
        </NFormItem>
        <NFormItem label="排序" path="sort" class="w-200px">
          <NSpace vertical>
            <NInputNumber v-model:value="model.sort" :min="0" :max="10000" />
            <FormTips :tips="['数字越大越靠前']" />
          </NSpace>
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio :value="1" label="显示" />
            <NRadio :value="0" label="隐藏" />
          </NRadioGroup>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton type="tertiary" @click="active = false">取消</NButton>
          <NButton type="primary" :loading="saveLoading" :disabled="saveLoading" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
