<script setup lang="ts">
import { nextTick, reactive, ref } from 'vue';
import { $t } from '@/locales';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { createContentCourseSection, updateContentCourseSection } from '@/service/api';

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const active = ref(false);
const saveLoading = ref<boolean>(false);
const uploadResponse = ref<Api.System.UploaderFile | null>(null);
const courseSection = ref<Api.Cms.ContentCourseSection>();
const drawerWidth = 680;

type Model = Pick<
  Api.Cms.ContentCourseSection,
  'id' | 'chapter_id' | 'ref_video_id' | 'name' | 'sort' | 'status' | 'filepath'
> & {
  type: number;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: 0,
    chapter_id: 0,
    ref_video_id: 0,
    type: 0,
    name: '',
    filepath: '',
    sort: 0,
    status: 1
  };
}

const rules: Record<string, App.Global.FormRule>[] = [
  {
    type: defaultRequiredRule,
    filepath: defaultRequiredRule,
    name: defaultRequiredRule
  },
  {
    type: defaultRequiredRule,
    name: defaultRequiredRule,
    ref_video_id: defaultRequiredRule
  }
];

function open(chapterId: number, data?: Api.Cms.ContentCourseSection) {
  if (data) {
    courseSection.value = data;

    Object.assign(model, {
      id: data.id,
      chapter_id: data.chapter_id,
      ref_video_id: data.ref_video_id,
      type: data.ref_video_id ? 1 : 0,
      name: data.name,
      filepath: data.filepath,
      sort: data.sort,
      status: data.status
    });

    if (!model.type && data.filepath_src) {
      uploadSuccess({
        filename: '',
        url: data.filepath_src,
        key: '',
        mime: '',
        size: 0
      });
    }
  } else {
    Object.assign(model, { ...createDefaultModel(), chapter_id: chapterId });

    uploadSuccess();
  }

  active.value = true;
}

function uploadSuccess(res?: Api.System.UploaderFile) {
  uploadResponse.value = res || null;
}

async function handleSubmit() {
  await validate();

  saveLoading.value = true;

  // 提交时，根据类型处理数据
  if (model.type) {
    model.filepath = '';
  } else {
    model.ref_video_id = 0;
  }

  if (model.id) {
    const { error } = await updateContentCourseSection(model.id, model);

    saveLoading.value = false;

    if (error) {
      return;
    }

    window.$message?.success($t('common.updateSuccess'));
  } else {
    const { error } = await createContentCourseSection(model);

    saveLoading.value = false;

    if (error) {
      return;
    }

    window.$message?.success($t('common.addSuccess'));
  }

  await restoreValidation();

  emit('reload');

  active.value = false;
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="active" :width="drawerWidth">
    <NDrawerContent>
      <template #header>{{ model.id ? '修改节' : '新建节' }}</template>
      <NForm ref="formRef" :model="model" :rules="rules[model.type]" label-placement="left" :label-width="80">
        <NFormItem label="名称" path="name" class="w-400px">
          <NInput v-model:value="model.name" placeholder="请输入节名称" />
        </NFormItem>
        <NFormItem label="类型" path="type">
          <NRadioGroup v-model:value="model.type" :disabled="!!model.id">
            <NRadio :value="0" label="上传" />
            <NRadio :value="1" label="筛选" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem v-if="model.type === 0" label="视频" path="filepath">
          <NSpace vertical class="w-480px">
            <UploadFile
              v-model:value="model.filepath"
              :preview="uploadResponse?.url"
              drag
              storage="priv"
              tips="请上传视频文件，大小在200M以内"
              @success-response="uploadSuccess"
            />
            <VideoButton v-if="uploadResponse" :src="uploadResponse.url" button-name="预览" />
          </NSpace>
        </NFormItem>
        <NFormItem v-else label="视频" path="ref_video_id">
          <ContentSelectVideo v-model:value="model.ref_video_id" :width="drawerWidth" />
        </NFormItem>
        <NFormItem label="排序" path="sort" class="w-200px">
          <NSpace vertical>
            <NInputNumber v-model:value="model.sort" :min="0" :max="10000" />
            <FormTips :tips="['数字越大越靠前']" />
          </NSpace>
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio :value="1" label="显示" />
            <NRadio :value="0" label="隐藏" />
          </NRadioGroup>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton type="tertiary" @click="active = false">取消</NButton>
          <NButton type="primary" :loading="saveLoading" :disabled="saveLoading" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
