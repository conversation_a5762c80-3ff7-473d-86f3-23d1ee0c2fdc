<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { SelectOption } from 'naive-ui';
import { fetchTopicList } from '@/service/api/train';

defineOptions({
  name: 'TopicSelect'
});

interface Props {
  modelValue: number;
  defaultTopic?: {
    id: number;
    name: string;
  };
}

interface Emits {
  (e: 'update:modelValue', value: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 题库选择相关
const loading = ref(false);
const options = ref<SelectOption[]>([]);

// 计算实际显示的值，当modelValue为0时返回null以显示placeholder
const displayValue = computed(() => {
  return props.modelValue === 0 ? null : props.modelValue;
});

async function handleSearch(query: string) {
  if (!query.length) {
    options.value = [];
    return;
  }

  loading.value = true;

  try {
    const { data } = await fetchTopicList({
      name: query
    });

    if (data && data.records) {
      options.value = data.records.map(item => ({
        label: item.name,
        value: item.id
      }));
    } else {
      options.value = [];
    }
  } catch (error) {
    options.value = [];
  } finally {
    loading.value = false;
  }
}

function handleUpdateValue(value: number | null) {
  emit('update:modelValue', value || 0);
}

// 监听defaultTopic变化，更新选项
watch(
  () => props.defaultTopic,
  newVal => {
    if (newVal && newVal.id !== 0) {
      options.value = [
        {
          label: newVal.name,
          value: newVal.id
        }
      ];
    }
  },
  { immediate: true }
);
</script>

<template>
  <NSelect
    :value="displayValue"
    filterable
    placeholder="输入题库名称搜索"
    :options="options"
    :loading="loading"
    clearable
    remote
    @update:value="handleUpdateValue"
    @search="handleSearch"
  />
</template>

<style scoped></style>
