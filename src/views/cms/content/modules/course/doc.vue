<script setup lang="tsx">
import { ref } from 'vue';
import { <PERSON><PERSON>utton, NPopconfirm, NSpace } from 'naive-ui';
import { $t } from '@/locales';
import { deleteContentCourseDoc, fetchContentCourseDocList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import DateFormat from '@/components/common/date/date-format.vue';
import OperateDoc from './operate-doc.vue';

defineOptions({
  name: 'ContentCourseDocTable'
});

interface Props {
  contentId: number;
}

const props = defineProps<Props>();

const docRef = ref();
const expandedRowKeys = ref<string[]>([]);

const { columns, data, loading, getData } = useTable<
  Api.Cms.ContentCourseDoc,
  typeof fetchContentCourseDocList,
  'index' | 'operate'
>({
  apiFn: fetchContentCourseDocList,
  apiParams: {
    content_id: props.contentId
  },
  transformer: res => {
    const list = res.data || [];

    return {
      data: list,
      pageNum: 1,
      pageSize: 10,
      total: list.length
    };
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      width: 100
    },
    {
      key: 'filename',
      title: '名称',
      align: 'center',
      width: 180
    },
    {
      key: 'sort',
      title: '排序',
      align: 'center',
      width: 100
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 200,
      render: row => (
        <NSpace justify="center">
          <NButton type="primary" text size="small" onClick={() => handleEdit(row)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row)}>
            {{
              default: () => '确认删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </NSpace>
      )
    }
  ]
});

function handleAdd() {
  docRef.value.open(props.contentId);
}

function handleEdit(row: Api.Cms.ContentCourseChapterSection) {
  docRef.value.open(row.content_id, row);
}

async function handleDelete(row: Api.Cms.ContentCourseChapterSection) {
  const { error } = await deleteContentCourseDoc(row.id);
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}
</script>

<template>
  <NCard title="&nbsp;" size="small">
    <template #header-extra>
      <NButton type="info" size="small" @click="handleAdd">
        <template #icon>
          <icon-ic-round-plus class="text-icon" />
        </template>
        创建资料
      </NButton>
    </template>
    <NDataTable
      :columns="columns"
      :data="data"
      :loading="loading"
      :row-key="item => item.index"
      :indent="32"
      :default-expanded-row-keys="expandedRowKeys"
      size="small"
      class="sm:h-full"
    />
  </NCard>
  <OperateDoc ref="docRef" @reload="getData" />
</template>

<style scoped></style>
