<script setup lang="ts">
import { reactive, ref } from 'vue';
import { $t } from '@/locales';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { createContentCourseDoc, updateContentCourseDoc } from '@/service/api';
import UploadFile from "@/components/common/upload/upload-file.vue";

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const active = ref(false);
const saveLoading = ref<boolean>(false);
const uploadResponse = ref<Api.System.UploaderFile | null>(null);
const courseDoc = ref<Api.Cms.ContentCourseDoc>();
const drawerWidth = 680;

type Model = Pick<
  Api.Cms.ContentCourseDoc,
  'id' | 'content_id' | 'filename' | 'filepath' | 'filepath_src' | 'sort'
> & {
  type: number;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: 0,
    content_id: 0,
    filename: '',
    filepath: '',
    filepath_src: '',
    sort: 0
  };
}

const rules: Record<string, App.Global.FormRule>[] = [
  {
    filepath: defaultRequiredRule,
    filename: defaultRequiredRule
  },
];

function open(contentId: number, data?: Api.Cms.ContentCourseDoc) {
  if (data) {
    courseDoc.value = data;

    Object.assign(model, {
      id: data.id,
      content_id: data.content_id,
      filename: data.filename,
      filepath: data.filepath,
      filepath_src: data.filepath_src,
      sort: data.sort
    });

    if (data.filepath_src) {
      uploadSuccess({
        filename: '',
        url: data.filepath_src,
        key: '',
        mime: '',
        size: 0
      });
    }
  } else {
    Object.assign(model, { ...createDefaultModel(), content_id: contentId });

    uploadSuccess();
  }

  active.value = true;
}

function uploadSuccess(res?: Api.System.UploaderFile) {
  uploadResponse.value = res || null;
}

async function handleSubmit() {
  await validate();

  saveLoading.value = true;

  if (model.id) {
    const { error } = await updateContentCourseDoc(model.id, model);

    saveLoading.value = false;

    if (error) {
      return;
    }

    window.$message?.success($t('common.updateSuccess'));
  } else {
    const { error } = await createContentCourseDoc(model);

    saveLoading.value = false;

    if (error) {
      return;
    }

    window.$message?.success($t('common.addSuccess'));
  }

  await restoreValidation();

  emit('reload');

  active.value = false;
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="active" :width="drawerWidth">
    <NDrawerContent>
      <template #header>{{ model.id ? '修改资料' : '创建资料' }}</template>
      <NForm ref="formRef" :model="model" :rules="rules[model.type]" label-placement="left" :label-width="80">
        <NFormItem label="名称" path="name" class="w-400px">
          <NInput v-model:value="model.filename" placeholder="请输入资料名称" />
        </NFormItem>
        <NFormItem label="资料" path="filepath">
          <NSpace vertical class="w-480px">
            <UploadFile
              v-model:value="model.filepath"
              :preview="model?.filepath_src"
              drag
              storage="priv"
              :file-type="['doc']"
              tips="请上传PDF、WORD、PPT、EXCEL文件，大小在60M以内"
              @success-response="uploadSuccess"
            />
            <FormTips
              :tips="['所上传资料内，禁止出现手机号、二维码等联系方式。']"
              tip-class="c-orange400 font-size-12px"
            />
          </NSpace>
        </NFormItem>
        <NFormItem label="排序" path="sort" class="w-200px">
          <NSpace vertical>
            <NInputNumber v-model:value="model.sort" :min="0" :max="10000" />
            <FormTips :tips="['数字越大越靠前']" />
          </NSpace>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton type="tertiary" @click="active = false">取消</NButton>
          <NButton type="primary" :loading="saveLoading" :disabled="saveLoading" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
