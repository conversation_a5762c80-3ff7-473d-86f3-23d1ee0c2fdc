<script setup lang="tsx">
import {computed, onMounted, ref} from 'vue';
import { NButton, NPopconfirm, NSpace, NTag } from 'naive-ui';
import { $t } from '@/locales';
import { deleteContentCourseChapter, deleteContentCourseSection, getContentCourseChapterChildren } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { courseSectionStatusLabel } from '@/constants/business';
import DateFormat from '@/components/common/date/date-format.vue';
import OperateChapter from './operate-chapter.vue';
import OperateSection from './operate-section.vue';
import Decimal from 'decimal.js';

defineOptions({
  name: 'ContentCourseChapterSectionTable'
});

interface Props {
  contentId: number;
  hourPerMinutes: number;
}

const props = defineProps<Props>();

const chapterRef = ref();
const sectionRef = ref();
const videoModalRef = ref();
const expandedRowKeys = ref<string[]>([]);
// 计算总课时
const toHour = computed(() => {
  if (!props.hourPerMinutes) {
    return 0;
  }
  let total = 0;
  data.value.forEach(item => {
    item.children.forEach(items => {
      total += items.duration
    })
  })
  let duration = (total / (props.hourPerMinutes * 60));
  let num = new Decimal(duration);
  total = num.toDecimalPlaces(1, Decimal.ROUND_FLOOR).toNumber();
  return total;
});

const { columns, data, loading, getData } = useTable<
  Api.Cms.ContentCourseChapterSection,
  typeof getContentCourseChapterChildren,
  'index' | 'operate'
>({
  apiFn: getContentCourseChapterChildren,
  apiParams: {
    content_id: props.contentId,
    hour_per_minutes: props.hourPerMinutes,
  },
  transformer: res => {
    const categories = res.data || [];

    categories.forEach(item => {
      expandedRowKeys.value.push(item.index);
    });

    return {
      data: categories,
      pageNum: 1,
      pageSize: 10,
      total: categories.length
    };
  },
  columns: () => [
    {
      key: 'name',
      title: '名称',
      align: 'left',
      width: 320,
      render: row => {
        return row.status ? row.name_desc : row.name;
      }
    },
    {
      key: 'index',
      title: '时长',
      align: 'center',
      render: row => {
        let totalSeconds: number
        totalSeconds = row.duration;
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        // 补零为两位数
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      }
    },
    {
      key: 'hour',
      title: '课时',
      align: 'center',
      width: 100,
      render: row => {
        if (!props.hourPerMinutes) {
          return '-'
        }
        let duration = (row.duration / (props.hourPerMinutes * 60));
        let hour =  new Decimal(duration);
        hour = hour.toDecimalPlaces(1, Decimal.ROUND_FLOOR).toNumber();
        return hour;

      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'default',
          1: 'success',
          2: 'info',
          3: 'warning',
        };

        const label = courseSectionStatusLabel[row.status];

        return (
          <NTag type={tagMap[row.status]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'sort',
      title: '排序',
      align: 'center',
      width: 100
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 200,
      render: row => (
        <NSpace justify="center">
          {row.type === 'chapter' && (
            <NButton type="primary" text size="small" onClick={() => handleAddChild(row)}>
              新增节
            </NButton>
          )}
          {row.type === 'section' && (
            <NButton type="primary" size="small" text onClick={() => handlePlay(row)}>
              预览
            </NButton>
          )}
          <NButton type="primary" text size="small" onClick={() => handleEdit(row)}>
            编辑
          </NButton>
          {(!row.children || row.children.length <= 0) && (
            <NPopconfirm onPositiveClick={() => handleDelete(row)}>
              {{
                default: () => '确认删除吗？',
                trigger: () => (
                  <NButton type="error" text size="small">
                    删除
                  </NButton>
                )
              }}
            </NPopconfirm>
          )}
        </NSpace>
      )
    }
  ]
});

function handlePlay(row: Api.Cms.ContentCourseChapterSection) {
  if (row.ref_video_id > 0) {
    videoModalRef.value.play(row.video?.filepath_src);
  } else {
    videoModalRef.value.play(row.filepath_src);
  }
}

function handleAdd() {
  chapterRef.value.open(props.contentId);
}

function handleAddChild(row: Api.Cms.ContentCourseChapterSection) {
  sectionRef.value.open(row.id);
}

function handleEdit(row: Api.Cms.ContentCourseChapterSection) {
  if (row.type === 'chapter') {
    chapterRef.value.open(props.contentId, row);
  } else {
    sectionRef.value.open(row.id, row);
  }
}

async function handleDelete(row: Api.Cms.ContentCourseChapterSection) {
  if (row.type === 'chapter') {
    const { error } = await deleteContentCourseChapter(row.id);
    if (error) {
      return;
    }
  } else {
    const { error } = await deleteContentCourseSection(row.id);
    if (error) {
      return;
    }
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}

onMounted(() => {
  console.log(data.value)
});
</script>

<template>
  <NCard  size="small">
    <template #header>
      <div>总课时 {{toHour}}</div>
    </template>
      <template #header-extra>
        <NFlex justify="space-between">

          <NButton type="info" size="small" @click="handleAdd">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            新建章
          </NButton>
        </NFlex>
      </template>

      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :row-key="item => item.index"
        :indent="32"
        :default-expanded-row-keys="expandedRowKeys"
        size="small"
        class="sm:h-full"
      />
  </NCard>
  <OperateChapter ref="chapterRef" @reload="getData" />
  <OperateSection ref="sectionRef" @reload="getData" />
  <VideoModal ref="videoModalRef" />
</template>

<style scoped></style>
