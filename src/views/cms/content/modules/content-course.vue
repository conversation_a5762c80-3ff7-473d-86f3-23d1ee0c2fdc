<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { rand } from '@vueuse/core';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { getContentCourse, updateContentCourse } from '@/service/api';
import { addCountsRange } from '@/constants/config.js';
import type { SubmitType } from './operate-content.vue';
import FormFooter from './content-form-footer.vue';
import ChapterSection from './course/chapter-section.vue';
import Doc from './course/doc.vue';
import TopicSelect from './course/topic-select.vue';

defineOptions({
  name: 'ContentText'
});

interface Props {
  content: Api.Cms.Content;
}

interface Emits {
  (e: 'finishStep', submitType: SubmitType): void;
  (e: 'changeStep', current: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const formFooterRef = ref();

type Model = Pick<
  Api.Cms.ContentCourse,
  'content_id' | 'teacher_name' | 'hour_per_minutes' | 'try_view_count' | 'learning_count' | 'learning_count_add' | 'topic_id'
> & {
  is_send: number;
};

const model: Model = reactive(createDefaultModel());
const defaultTopic = ref<{ id: number; name: string } | undefined>(undefined);

function createDefaultModel(): Model {
  return {
    content_id: props.content.id,
    is_send: 0,
    teacher_name: '',
    hour_per_minutes: 60,
    try_view_count: 1,
    learning_count: 0,
    topic_id: 0,
    learning_count_add: rand(addCountsRange[0], addCountsRange[1])
  };
}

function lastStep() {
  emit('changeStep', 1);
}

function handleUpdateModelWhenEdit(data: object) {
  Object.assign(model, data);
}

async function handleSubmit(type: SubmitType) {
  await validate();

  if (type === 'draft') {
    model.is_send = 0;
    formFooterRef.value.setSaveGraftLoading(true);
  } else {
    model.is_send = 1;
    formFooterRef.value.setSaveReleaseLoading(true);
  }

  formFooterRef.value.setSaveDisabled(true);

  const { error } = await updateContentCourse(props.content.id, { ...model, learning_count: undefined });

  formFooterRef.value.setSaveGraftLoading(false);
  formFooterRef.value.setSaveReleaseLoading(false);
  formFooterRef.value.setSaveDisabled(false);

  if (error) {
    return;
  }

  emit('finishStep', type);

  await restoreValidation();
}

async function init() {
  const { data } = await getContentCourse(props.content.id);

  if (data) {
    handleUpdateModelWhenEdit({
      teacher_name: data.teacher_name,
      hour_per_minutes: data.hour_per_minutes,
      try_view_count: data.try_view_count,
      learning_count: data.learning_count,
      learning_count_add: data.learning_count_add,
      topic_id: data.topic_id
    });

    if (data.topic_id && data.topic) {
      defaultTopic.value = {
        id: data.topic_id,
        name: data.topic.name
      };
    }
  }
}

onMounted(async () => {
  await init();
});
</script>

<template>
  <NSpace vertical>
    <NForm ref="formRef" :model="model" label-placement="left" :label-width="100">
      <NFormItem label="讲师名称" path="teacher_name" class="w-300px">
        <NInput v-model:value="model.teacher_name" placeholder="请输入讲师名称" />
      </NFormItem>
      <NFormItem label="试听节数" path="try_view_count" class="w-200px">
        <NInputNumber v-model:value="model.try_view_count" :min="0" :max="10" />
      </NFormItem>
      <NFormItem label="单位课时时长" path="try_view_count" class="w-250px">
        <NFlex>
            <NInputNumber v-model:value="model.hour_per_minutes" :min="1" >
              <template #suffix>
                分钟
              </template>
            </NInputNumber>
        </NFlex>
      </NFormItem>
      <NFormItem label="章节管理">
        <ChapterSection :content-id="content.id" :hour-per-minutes="model.hour_per_minutes"  />
      </NFormItem>
      <NFormItem label="资料管理">
        <Doc :content-id="content.id" />
      </NFormItem>
      <NFormItem label="题库选择">
        <TopicSelect v-model:modelValue="model.topic_id" :default-topic="defaultTopic" />
      </NFormItem>
      <NFormItem label="学习人数" path="learning_count" class="w-300px">
        <NPopover placement="right">
          <template #trigger>
            <NInputNumber v-model:value="model.learning_count_add" :min="0">
              <template #prefix>{{ model.learning_count }} +</template>
            </NInputNumber>
          </template>
          真实学习人数（不可修改） + 叠加学习人数
        </NPopover>
      </NFormItem>
    </NForm>
    <FormFooter ref="formFooterRef" @last-step="lastStep" @submitied="handleSubmit" />
  </NSpace>
</template>

<style scoped></style>
