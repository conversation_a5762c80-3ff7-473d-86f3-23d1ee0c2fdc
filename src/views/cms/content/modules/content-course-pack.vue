<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { getCoursePackage, updateCoursePackage } from '@/service/api';
import TopicSelect from '@/views/cms/content/modules/course/topic-select.vue';
import type { SubmitType } from './operate-content.vue';
import FormFooter from './content-form-footer.vue';

defineOptions({
  name: 'ContentCoursePack'
});

interface Props {
  content: Api.Cms.Content;
}

interface Emits {
  (e: 'finishStep', submitType: SubmitType): void;
  (e: 'changeStep', current: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type RuleKey = Extract<keyof Model, 'course_ids'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  course_ids: defaultRequiredRule
};

const formFooterRef = ref();

type Model = Pick<Api.Cms.ContentCoursePack, 'content_id'> & {
  is_send: number;
  topic_id: number;
  course_ids: number[];
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    content_id: props.content.id,
    is_send: 0,
    topic_id: 0,
    course_ids: []
  };
}

function lastStep() {
  emit('changeStep', 1);
}

async function handleSubmit(type: SubmitType) {
  await validate();

  if (type === 'draft') {
    model.is_send = 0;
    formFooterRef.value.setSaveGraftLoading(true);
  } else {
    model.is_send = 1;
    formFooterRef.value.setSaveReleaseLoading(true);
  }

  formFooterRef.value.setSaveDisabled(true);

  const { error } = await updateCoursePackage(props.content.id, { ...model });

  formFooterRef.value.setSaveGraftLoading(false);
  formFooterRef.value.setSaveReleaseLoading(false);
  formFooterRef.value.setSaveDisabled(false);

  if (error) {
    return;
  }

  emit('finishStep', type);

  await restoreValidation();
}

const loading = ref<boolean>(true);
const defaultTopic = ref<{ id: number; name: string } | undefined>(undefined);

async function init() {
  const { data } = await getCoursePackage(props.content.id);
  if (data) {
    if (data.topic) {
      model.topic_id = data.topic_id;
      defaultTopic.value = {
        id: data.topic_id,
        name: data.topic.name
      };
    }
    const courses = data.courses;
    if (courses) {
      model.course_ids = courses.map(item => {
        return item.id;
      });
    }
  }
  loading.value = false;
}

onMounted(() => {
  init();
});
</script>

<template>
  <NSpace vertical>
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100" class="w-230">
      <NFormItem label="选择课程" path="course_ids">
        <ContentRelation v-if="!loading" v-model:value="model.course_ids" classify="course" draggable />
      </NFormItem>
      <NFormItem label="题库选择">
        <TopicSelect v-model:modelValue="model.topic_id" :default-topic="defaultTopic" />
      </NFormItem>
    </NForm>
    <FormFooter ref="formFooterRef" @last-step="lastStep" @submitied="handleSubmit" />
  </NSpace>
</template>

<style scoped></style>
