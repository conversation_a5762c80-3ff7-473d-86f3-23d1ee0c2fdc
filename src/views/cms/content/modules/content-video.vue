<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { getContentVideo, updateContentVideo } from '@/service/api';
import UploadFile from '@/components/common/upload/upload-file.vue';
import VideoButton from '@/components/common/video/video-button.vue';
import type { SubmitType } from './operate-content.vue';
import FormFooter from './content-form-footer.vue';

defineOptions({
  name: 'ContentVideo'
});

interface Props {
  content: Api.Cms.Content;
}

interface Emits {
  (e: 'finishStep', submitType: SubmitType): void;
  (e: 'changeStep', current: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const loading = ref<boolean>(true);
const formFooterRef = ref();
const video = ref<Api.Cms.ContentVideo | null>(null);
const uploadResponse = ref<Api.System.UploaderFile | null>(null);

type Model = Pick<Api.Cms.ContentVideo, 'content_id' | 'filepath'> & {
  is_send: number;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    content_id: props.content.id,
    is_send: 0,
    filepath: ''
  };
}

type RuleKey = Extract<keyof Model, 'filepath'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  filepath: defaultRequiredRule
};

function lastStep() {
  emit('changeStep', 1);
}

function uploadSuccess(res?: Api.System.UploaderFile) {
  uploadResponse.value = res || null;
}

function handleUpdateModelWhenEdit(data: object) {
  Object.assign(model, data);
}

async function handleSubmit(type: SubmitType) {
  await validate();

  if (type === 'draft') {
    model.is_send = 0;
    formFooterRef.value.setSaveGraftLoading(true);
  } else {
    model.is_send = 1;
    formFooterRef.value.setSaveReleaseLoading(true);
  }

  formFooterRef.value.setSaveDisabled(true);

  const { error } = await updateContentVideo(props.content.id, model);

  formFooterRef.value.setSaveGraftLoading(false);
  formFooterRef.value.setSaveReleaseLoading(false);
  formFooterRef.value.setSaveDisabled(false);

  if (error) {
    return;
  }

  emit('finishStep', type);

  await restoreValidation();
}

async function init() {
  const { data } = await getContentVideo(props.content.id);

  loading.value = false;

  if (data) {
    video.value = data;

    handleUpdateModelWhenEdit({
      filepath: data.filepath
    });

    uploadSuccess({
      filename: '',
      url: data.filepath_src,
      key: '',
      mime: '',
      size: 0
    });
  }
}

onMounted(async () => {
  await init();
});
</script>

<template>
  <NSpace vertical>
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
      <NFormItem label="上传视频" path="filepath" class="w-500px">
        <UploadFile
          v-if="!loading"
          v-model:value="model.filepath"
          :preview="video?.filepath_src"
          drag
          storage="priv"
          :file-type="['video']"
          tips="请上传视频文件，大小在200M以内"
          @success-response="uploadSuccess"
        />
      </NFormItem>
      <NFormItem v-if="uploadResponse" label="&nbsp;">
        <VideoButton :src="uploadResponse.url" button-name="预览" />
      </NFormItem>
    </NForm>
    <FormFooter ref="formFooterRef" @last-step="lastStep" @submitied="handleSubmit" />
  </NSpace>
</template>

<style scoped></style>
