<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { rand } from '@vueuse/core';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { createScreenshotTask, getContentDoc, getScreenshotResult, updateContentDoc } from '@/service/api';
import { addCountsRange } from '@/constants/config.js';
import UploadImage from '@/components/common/upload/upload-image.vue';
import UploadFile from '@/components/common/upload/upload-file.vue';
import FormFooter from './content-form-footer.vue';
import type { SubmitType } from './operate-content.vue';

defineOptions({
  name: 'ContentDocument'
});

interface Props {
  content: Api.Cms.Content;
}

interface Emits {
  (e: 'finishStep', submitType: SubmitType): void;
  (e: 'changeStep', current: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const loading = ref<boolean>(true);
const uploadImageRef = ref();
const formFooterRef = ref();
const doc = ref<Api.Cms.ContentDoc | null>(null);
const docResponse = ref<Api.System.UploaderFile>();
const generateLoading = ref<boolean>(false);

type Model = Pick<Api.Cms.ContentDoc, 'content_id' | 'page_count' | 'filepath' | 'filename' | 'preview_images' | 'download_count' | 'download_count_add'> & {
  is_send: number;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    content_id: props.content.id,
    is_send: 0,
    page_count: 1,
    filepath: '',
    filename: '',
    preview_images: [],
    download_count: 0,
    download_count_add: rand(addCountsRange[0], addCountsRange[1])
  };
}

type RuleKey = Extract<keyof Model, 'page_count' | 'filepath' | 'preview_images'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  page_count: defaultRequiredRule,
  filepath: defaultRequiredRule,
  preview_images: defaultRequiredRule
};

function lastStep() {
  emit('changeStep', 1);
}

function handleUpdateModelWhenEdit(data: object) {
  Object.assign(model, data);
}

function docUploadSuccess(res?: Api.System.UploaderFile) {
  docResponse.value = res;
  model.filename = res ? res.filename : '';
}

async function handleGenerate() {
  if (!docResponse.value?.url) {
    window.$message?.warning('请先上传资料');
    return;
  }

  generateLoading.value = true;

  // 创建截图任务
  const { data: task, error } = await createScreenshotTask({
    filepath: docResponse.value.url,
    filename: docResponse.value.filename
  });
  if (error) {
    generateLoading.value = false;
    return;
  }

  // 处理截图结果
  handleGenerateResult(task);
}

function handleGenerateResult(task: Api.System.AsyncTask, max: number = 60) {
  if (max <= 0) {
    generateLoading.value = false;
    window.$message?.warning('生成结果超时');
    return;
  }

  // eslint-disable-next-line no-param-reassign,no-plusplus
  max--;

  setTimeout(async () => {
    const { data: result, error: resultError } = await getScreenshotResult({ task_id: task.id });

    if (resultError) {
      generateLoading.value = false;
      return;
    }

    if (!result.success) {
      handleGenerateResult(task, max);
      return;
    }

    generateLoading.value = false;

    const files = [];
    const screenshotImages: string[] = [];

    result.data.forEach(item => {
      screenshotImages.push(item.key);

      files.push({
        name: item.filename,
        imageKey: item.key,
        url: item.url
      })
    });

    handleUpdateModelWhenEdit({ preview_images: screenshotImages });

    uploadImageRef.value.setPreviewFiles(files);
  }, 1000);
}

async function handleSubmit(type: SubmitType) {
  await validate();

  if (type === 'draft') {
    model.is_send = 0;
    formFooterRef.value.setSaveGraftLoading(true);
  } else {
    model.is_send = 1;
    formFooterRef.value.setSaveReleaseLoading(true);
  }

  formFooterRef.value.setSaveDisabled(true);

  const { error } = await updateContentDoc(props.content.id, { ...model, download_count: undefined });

  formFooterRef.value.setSaveGraftLoading(false);
  formFooterRef.value.setSaveReleaseLoading(false);
  formFooterRef.value.setSaveDisabled(false);

  if (error) {
    return;
  }

  emit('finishStep', type);

  await restoreValidation();
}

async function init() {
  const { data } = await getContentDoc(props.content.id);

  loading.value = false;

  if (data) {
    doc.value = data;

    handleUpdateModelWhenEdit({
      status: 0,
      page_count: data.page_count,
      filepath: data.filepath,
      filename: data.filename,
      preview_images: data.preview_images_src,
      download_count: data.download_count,
      download_count_add: data.download_count_add
    });

    docUploadSuccess({
      filename: data.filename,
      url: data.filepath_src,
      key: '',
      mime: '',
      size: 0
    });

    const files = [];

    model.preview_images.forEach(item => {
      const name = item.split('/').pop();

      files.push({
        name,
        url: item,
        imageKey: item
      });
    })

    uploadImageRef.value.setPreviewFiles(files);
  }
}

onMounted(async () => {
  await init();
});
</script>

<template>
  <NSpace vertical>
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
      <NFormItem label="资料页数" path="page_count" class="w-240px">
        <NInputNumber v-model:value="model.page_count" :min="0" />
      </NFormItem>
      <NFormItem label="上传资料" path="filepath" class="w-500px">
        <NSpace>
          <UploadFile
            v-if="!loading"
            v-model:value="model.filepath"
            :preview="doc?.filepath_src"
            drag
            storage="priv"
            :file-type="['doc']"
            tips="请上传PDF、WORD、PPT、EXCEL文件，大小在60M以内"
            @success-response="docUploadSuccess"
          />
          <FormTips
            :tips="['所上传资料内，禁止出现手机号、二维码等联系方式。']"
            tip-class="c-orange400 font-size-12px"
          />
        </NSpace>
      </NFormItem>
      <NFormItem label="资料预览图" path="preview_images">
        <NButton type="info" :loading="generateLoading" :disabled="generateLoading" @click="handleGenerate">
          生成资料截图
        </NButton>
      </NFormItem>
      <NFormItem label="&nbsp;">
        <UploadImage ref="uploadImageRef" v-model:value="model.preview_images" multiple :max="10" />
      </NFormItem>
      <NFormItem label="下载数" path="download_count_add" class="w-300px">
        <NPopover placement="right">
          <template #trigger>
            <NInputNumber v-model:value="model.download_count_add" :min="0">
              <template #prefix>{{ model.download_count }} +</template>
            </NInputNumber>
          </template>
          真实下载数（不可修改） + 叠加下载数
        </NPopover>
      </NFormItem>
    </NForm>
    <FormFooter ref="formFooterRef" @last-step="lastStep" @submitied="handleSubmit" />
  </NSpace>
</template>

<style scoped></style>
