<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { getContentRichText, updateContentRichText } from '@/service/api';
import type { SubmitType } from './operate-content.vue';
import FormFooter from './content-form-footer.vue';

defineOptions({
  name: 'ContentText'
});

interface Props {
  content: Api.Cms.Content;
}

interface Emits {
  (e: 'finishStep', submitType: SubmitType): void;
  (e: 'changeStep', current: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const formFooterRef = ref();

type Model = Pick<Api.Cms.ContentRichText, 'content_id' | 'content'> & {
  is_send: number;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    content_id: props.content.id,
    is_send: 0,
    content: ''
  };
}

type RuleKey = Extract<keyof Model, 'content'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  content: defaultRequiredRule
};

function lastStep() {
  emit('changeStep', 1);
}

function handleUpdateModelWhenEdit(data: object) {
  Object.assign(model, data);
}

async function handleSubmit(type: SubmitType) {
  await validate();

  if (type === 'draft') {
    model.is_send = 0;
    formFooterRef.value.setSaveGraftLoading(true);
  } else {
    model.is_send = 1;
    formFooterRef.value.setSaveReleaseLoading(true);
  }

  formFooterRef.value.setSaveDisabled(true);

  const { error } = await updateContentRichText(props.content.id, model);

  formFooterRef.value.setSaveGraftLoading(false);
  formFooterRef.value.setSaveReleaseLoading(false);
  formFooterRef.value.setSaveDisabled(false);

  if (error) {
    return;
  }

  await restoreValidation();

  emit('finishStep', type);
}

async function init() {
  const { data } = await getContentRichText(props.content.id);

  if (data) {
    handleUpdateModelWhenEdit({
      content: data.content
    });
  }
}

onMounted(async () => {
  await init();
});
</script>

<template>
  <NSpace vertical>
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
      <NFormItem label="文章内容" path="content" class="w-860px">
        <EditorTinymce v-model:value="model.content" :form-params="{ content_id: content.id }" />
      </NFormItem>
    </NForm>
    <FormFooter ref="formFooterRef" @last-step="lastStep" @submitied="handleSubmit" />
  </NSpace>
</template>

<style scoped></style>
