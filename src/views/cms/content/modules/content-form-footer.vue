<script setup lang="ts">
import { ref } from 'vue';
import type { SubmitType } from './operate-content.vue';

defineOptions({
  name: 'ContentFormFooter'
});

interface Emits {
  (e: 'submitied', submitType: SubmitType): void;
  (e: 'lastStep'): void;
}

const emit = defineEmits<Emits>();

const saveReleaseLoading = ref<boolean>(false);
const saveGraftLoading = ref<boolean>(false);
const saveDisabled = ref<boolean>(false);

function lastStep() {
  emit('lastStep');
}

function handleSubmit(type: SubmitType) {
  emit('submitied', type);
}

function setSaveReleaseLoading(bool: boolean) {
  saveReleaseLoading.value = bool;
}

function setSaveGraftLoading(bool: boolean) {
  saveGraftLoading.value = bool;
}

function setSaveDisabled(bool: boolean) {
  saveDisabled.value = bool;
}

defineExpose({
  setSaveReleaseLoading,
  setSaveGraftLoading,
  setSaveDisabled
});
</script>

<template>
  <NFlex class="pl-100px" :size="30">
    <NButton type="info" :disabled="saveDisabled" :loading="saveReleaseLoading" @click="handleSubmit('release')">
      发布
    </NButton>
    <NButton type="info" :disabled="saveDisabled" :loading="saveGraftLoading" @click="handleSubmit('draft')">
      保存
    </NButton>
    <NButton type="primary" @click="lastStep()">上一步</NButton>
  </NFlex>
</template>

<style scoped></style>
