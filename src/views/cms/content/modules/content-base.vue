<script setup lang="ts">
import { reactive, ref } from 'vue';
import { rand } from '@vueuse/core';
import type { CascaderOption } from 'naive-ui';
import { createContent, updateContent } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { addCountsRange } from '@/constants/config';
import UploadImage from '@/components/common/upload/upload-image.vue';

defineOptions({
  name: 'ContentBase'
});

interface Props {
  classify: Api.Cms.CategoryClassifyType;
  contentType: Api.Cms.ContentType;
  categories: Api.Cms.Category[];
  currentCategoryId?: number;
  rowData?: Api.Cms.Content;
}

interface Emits {
  (e: 'nextStep', content: Api.Cms.Content): void;
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const saveLoading = ref<boolean>(false);

let syncChargeCredit: number = 0;

type Model = Pick<
  Api.Cms.Content,
  | 'id'
  | 'title'
  | 'intro'
  | 'type'
  | 'cover'
  | 'view_limit'
  | 'charge_credit'
  | 'charge_amount'
  | 'source'
  | 'status'
  | 'views'
  | 'views_add'
> & {
  category_id: number | null;
  recommend: number;
  relation_ids: number[];
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  const defaultModel = {
    id: 0,
    title: '',
    category_id: props.currentCategoryId ? props.currentCategoryId : null,
    intro: '',
    cover: '',
    type: props.contentType,
    view_limit: 0,
    views: 0,
    views_add: rand(addCountsRange[0], addCountsRange[1]),
    charge_credit: 0,
    sync_charge_credit: 0,
    charge_amount: '0',
    status: 0,
    source: '',
    recommend: 0,
    relation_ids: []
  };

  // eslint-disable-next-line default-case
  switch (props.classify) {
    case 'material':
      defaultModel.view_limit = 1;
      defaultModel.charge_credit = 30;
      break;

    case 'course':
      defaultModel.view_limit = 2;
      defaultModel.charge_amount = '0';
      break;
  }

  return defaultModel;
}

type RuleKey = Extract<keyof Model, 'title' | 'category_id'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  title: defaultRequiredRule,
  category_id: defaultRequiredRule
};

const formConfig: Record<Api.Cms.CategoryClassifyType, any> = {
  material: {
    cover_tips: ['注：无需上传封面']
  },
  course: {
    cover_tips: ['注：必传，建议宽高比1:1，分辨率不低于600*600']
  },
  news: {
    cover_tips: ['注：建议尺寸：900*383']
  },
  course_pack: {
    cover_tips: ['注：建议尺寸：900*383']
  }
};

function close() {
  emit('close');
}

function handleUpdateModelWhenEdit(content?: Api.Cms.Content) {
  if (content) {
    const relationIds: number[] = [];

    content.relation_list?.forEach(item => {
      relationIds.push(item.related_id);
    });

    Object.assign(model, {
      id: content.id,
      title: content.title,
      category_id: content.category_id,
      intro: content.intro,
      cover: content.cover,
      type: content.type,
      view_limit: content.view_limit,
      views: content.views,
      views_add: content.views_add,
      charge_credit: content.charge_credit,
      charge_amount: content.charge_amount,
      status: content.status,
      source: content.source,
      recommend: content.recommend_at ? 1 : 0,
      relation_ids: relationIds
    });
  } else {
    Object.assign(model, createDefaultModel());
  }
}

handleUpdateModelWhenEdit(props.rowData);

async function handleSubmit() {
  await validate();

  saveLoading.value = true;

  const updateData = { ...model, views: undefined };

  if (model.id) {
    const { data, error } = await updateContent(model.id as number, updateData);

    saveLoading.value = false;

    if (error) {
      return;
    }

    emit('nextStep', data);
  } else {
    const { data, error } = await createContent(updateData);

    saveLoading.value = false;

    if (error) {
      return;
    }

    emit('nextStep', data);
  }

  await restoreValidation();
}

function getCredit() {
  syncChargeCredit = parseFloat(model.charge_amount) * 10
}
function syncCredit() {
  model.charge_credit = syncChargeCredit
}
</script>

<template>
  <NSpace vertical>
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
      <NFormItem label="内容名称" path="title" class="w-400px">
        <NInput v-model:value="model.title" placeholder="请输入内容名称" />
      </NFormItem>
      <NFormItem label="内容分类" path="category_id" class="w-300px">
        <NCascader
          v-model:value="model.category_id"
          :options="categories as CascaderOption[]"
          value-field="id"
          label-field="name"
          check-strategy="child"
        />
      </NFormItem>
      <NFormItem label="内容封面" path="cover">
        <NSpace vertical>
          <UploadImage v-model:value="model.cover" :preview="rowData?.cover_src" />
          <FormTips :tips="formConfig[classify].cover_tips" />
        </NSpace>
      </NFormItem>
      <NFormItem label="内容简介" path="intro" class="w-600px">
        <NInput v-model:value="model.intro" type="textarea" :rows="7" />
      </NFormItem>
      <NFormItem label="收费类型" path="view_limit" class="w-500px">
        <NRadioGroup v-model:value="model.view_limit">
          <NFlex>
            <NRadio :value="0" label="免费"></NRadio>
            <NRadio :value="1" label="积分"></NRadio>
            <NRadio :value="2" label="金额"></NRadio>
            <NRadio :value="3" label="金额/积分"></NRadio>
          </NFlex>
        </NRadioGroup>
      </NFormItem>
      <NFormItem v-if="model.view_limit === 2 || model.view_limit === 3" label="&nbsp;" path="charge_amount" class="w-300px">
        <NInput v-model:value="model.charge_amount" :on-input="getCredit" placeholder="请输入金额">
          <template #prefix>金额</template>
          <template #suffix>元</template>
        </NInput>
      </NFormItem>
      <NFormItem v-if="model.view_limit === 1 || model.view_limit === 3" label="&nbsp;" path="charge_credit">
        <NInputNumber v-model:value="model.charge_credit" :min="0" class="w-200px" placeholder="请输入积分">
          <template #prefix>积分</template>
        </NInputNumber>
        <NButton v-if="parseFloat(model.charge_amount) > 0 && model.view_limit === 3" type="info" style="margin-left: 10px" class="w-120px" @click="syncCredit">设为{{ syncChargeCredit }}积分</NButton>
      </NFormItem>
      <NFormItem label="推荐" path="recommend">
        <NSwitch v-model:value="model.recommend" :checked-value="1" :unchecked-value="0" />
      </NFormItem>
      <NFormItem label="内容来源" path="source" class="w-300px">
        <NInput v-model:value="model.source" placeholder="请输入内容来源" />
      </NFormItem>
      <NFormItem label="浏览数" path="views_add" class="w-300px">
        <NPopover placement="right">
          <template #trigger>
            <NInputNumber v-model:value="model.views_add" :min="0">
              <template #prefix>{{ model.views }} +</template>
            </NInputNumber>
          </template>
          真实浏览数（不可修改） + 叠加浏览数
        </NPopover>
      </NFormItem>
      <NFormItem label="关联内容" path="relation_ids" v-if="props.classify != 'course_pack'">
        <ContentRelation v-model:value="model.relation_ids" :admin-id="rowData?.admin_id" :max="5" />
      </NFormItem>
    </NForm>
    <NFlex class="pl-100px" :size="30">
      <NButton type="info" :loading="saveLoading" :disabled="saveLoading" @click="handleSubmit()">
        {{ saveLoading ? '保存中' : '下一步' }}
      </NButton>
      <NButton type="default" @click="close()">关闭</NButton>
    </NFlex>
  </NSpace>
</template>

<style scoped></style>
