<script setup lang="ts">
import { ref } from 'vue';
import { categoryClassifyLabel, contentTypeLabel } from '@/constants/business';
import { useRouterPush } from '@/hooks/common/router';
import { closeTab } from '@/utils/tab';
import ContentBase from './content-base.vue';
import ContentDoc from './content-doc.vue';
import ContentVideo from './content-video.vue';
import ContentText from './content-text.vue';
import ContentCourse from './content-course.vue';
import ContentCoursePack from './content-course-pack.vue';

defineOptions({
  name: 'ContentOperate'
});

export type SubmitType = 'release' | 'release_next' | 'draft';

interface Props {
  classify: Api.Cms.CategoryClassifyType;
  contentType: Api.Cms.ContentType;
  categories: Api.Cms.Category[];
  rowData?: Api.Cms.Content;
}

const props = defineProps<Props>();

const { routerPushByKey, route } = useRouterPush();

const classifyLabel = ref<string>(categoryClassifyLabel[props.classify]);
const typeLabel = ref<string>(contentTypeLabel[props.contentType]);
const stepCurrent = ref<number>(1);
const currentCategoryId = ref<number>(0);
const content = ref<Api.Cms.Content>();
const submitType = ref<SubmitType>('draft');

if (props.rowData) {
  content.value = props.rowData;
}

function changeStep(current: number) {
  stepCurrent.value = current;
}

function nextStep(data: Api.Cms.Content) {
  content.value = data;

  currentCategoryId.value = data.category_id;

  changeStep(2);
}

function finishStep(type: SubmitType) {
  submitType.value = type;

  changeStep(3);
}

async function close() {
  await closeTab(route.value.name as string);

  // eslint-disable-next-line default-case
  switch (props.classify) {
    case 'material':
      await routerPushByKey('cms_material', { query: { page: '1' } });
      break;
    case 'news':
      await routerPushByKey('cms_news', { query: { page: '1' } });
      break;
    case 'course':
      await routerPushByKey('cms_course', { query: { page: '1' } });
      break;
  }
}

function continueCreate() {
  content.value = undefined;

  changeStep(1);
}
</script>

<template>
  <NSteps :current="stepCurrent" class="pl-12% pt-20px">
    <NStep title="基础信息" />
    <NStep :title="`${typeLabel}信息`" />
    <NStep title="发布信息" />
  </NSteps>

  <NFlex justify="start" class="mb-60px mt-30px">
    <div v-if="stepCurrent === 1" class="pl-15%">
      <ContentBase
        :classify="classify"
        :content-type="contentType"
        :categories="categories"
        :row-data="content"
        :current-category-id="currentCategoryId"
        @next-step="nextStep"
        @close="close"
      />
    </div>
    <div v-if="stepCurrent === 2 && content" class="pl-15%">
      <ContentDoc v-if="contentType === 1" :content="content" @finish-step="finishStep" @change-step="changeStep" />
      <ContentText
        v-else-if="contentType === 2"
        :content="content"
        @finish-step="finishStep"
        @change-step="changeStep"
      />
      <ContentVideo
        v-else-if="contentType === 3"
        :content="content"
        @finish-step="finishStep"
        @change-step="changeStep"
      />
      <ContentCourse
        v-else-if="contentType === 4"
        :content="content"
        @finish-step="finishStep"
        @change-step="changeStep"
      />
      <ContentCoursePack
        v-else-if="contentType === 5"
        :content="content"
        @finish-step="finishStep"
        @change-step="changeStep"
      />
      <NText v-else>暂不支持</NText>
    </div>
    <div v-if="stepCurrent === 3 && content" class="mb-150px ml-36% mt-100px">
      <NResult
        status="success"
        :title="submitType === 'release' ? '发布成功' : '保存成功'"
        :description="content.title"
      >
        <template #footer>
          <NFlex justify="center">
            <NButton type="success" @click="close">返回列表页</NButton>
            <NButton type="info" @click="continueCreate()">继续新建{{ classifyLabel }}</NButton>
          </NFlex>
        </template>
      </NResult>
    </div>
  </NFlex>
</template>

<style scoped></style>
