<script setup lang="ts">
import { updateContent } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

interface Props {
  rowData: Api.Cms.Content;
}

interface Emits {
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();
const props = defineProps<Props>();

const { loading, startLoading, endLoading } = useLoading();

async function handleChange(status: number) {
  startLoading();

  await updateContent(props.rowData.id, { status });

  endLoading();

  emit('refresh');
}

</script>

<template>
  <NButton v-if="rowData.status === 2" type="warning" ghost size="small" :disabled="loading" @click="handleChange(3)">
    隐藏
  </NButton>
  <NButton v-else-if="rowData.status === 3" type="primary" ghost size="small" :disabled="loading" @click="handleChange(2)">
    显示
  </NButton>
</template>

<style scoped></style>
