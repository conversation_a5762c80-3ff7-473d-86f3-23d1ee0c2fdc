<script setup lang="ts">
import { ref } from 'vue';
import { updateContent } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

interface Props {
  rowData: Api.Cms.Content;
}

const props = defineProps<Props>();

const { loading, startLoading, endLoading } = useLoading();

const recommendCheck = ref<boolean>(!!props.rowData.recommend_at);

async function handleChange(value: boolean) {
  recommendCheck.value = value;

  startLoading();

  await updateContent(props.rowData.id, { recommend: value ? 1 : 0 });

  endLoading();
}

</script>

<template>
  <NSwitch :value="recommendCheck" :disabled="loading" @update:value="handleChange">
    <template #checked>
      开
    </template>
    <template #unchecked>
      关
    </template>
  </NSwitch>
</template>

<style scoped></style>
