<script setup lang="ts">
import { ref } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { getCourseStatistic } from '@/service/api';

defineOptions({
  name: 'CardData'
});

interface Props {
  contentId: string;
}

const props = defineProps<Props>();

interface CardData {
  key: string;
  title: string;
  count: number;
  color: {
    start: string;
    end: string;
  };
  icon: string;
}

const cardData = ref<CardData[]>([
  {
    key: 'buy_count',
    title: '已购课程人数',
    count: 0,
    color: {
      start: '#ec4786',
      end: '#b955a4'
    },
    icon: 'mingcute:user-add-fill'
  },
  {
    key: 'studying_count',
    title: '在学人数',
    count: 0,
    color: {
      start: '#00CC99',
      end: '#00CCFF'
    },
    icon: 'mingcute:user-add-fill'
  },
  {
    key: 'study_finish_count',
    title: '已学人数',
    count: 0,
    color: {
      start: '#336699',
      end: '#3366FF'
    },
    icon: 'mingcute:user-add-fill'
  }
]);

interface GradientBgProps {
  gradientColor: string;
}

const [DefineGradientBg, GradientBg] = createReusableTemplate<GradientBgProps>();

function getGradientColor(color: CardData['color']) {
  return `linear-gradient(to bottom right, ${color.start}, ${color.end})`;
}

async function init() {
  const { data, error } = await getCourseStatistic({ content_id: props.contentId });

  if (error) {
    return;
  }

  cardData.value[0].count = data.buy_count;
  cardData.value[1].count = data.studying_count;
  cardData.value[2].count = data.study_finish_count;
}

init();
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <!-- define component start: GradientBg -->
    <DefineGradientBg v-slot="{ $slots, gradientColor }">
      <div class="rd-8px px-16px pb-4px pt-8px text-white" :style="{ backgroundImage: gradientColor }">
        <component :is="$slots.default" />
      </div>
    </DefineGradientBg>
    <!-- define component end: GradientBg -->

    <NGrid cols="s:1 m:2 l:4" responsive="screen" :x-gap="16" :y-gap="16">
      <NGi v-for="item in cardData" :key="item.key">
        <GradientBg :gradient-color="getGradientColor(item.color)" class="flex-1">
          <h3 class="text-16px">{{ item.title }}</h3>
          <div class="flex justify-between pt-12px">
            <SvgIcon :icon="item.icon" class="text-32px" />
            <CountTo
              :start-value="0"
              :end-value="item.count"
              class="text-30px text-white dark:text-dark"
            />
          </div>
        </GradientBg>
      </NGi>
    </NGrid>
  </NCard>
</template>

<style scoped></style>
