<script setup lang="ts">
import type { FilteredColumn } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { useLoading } from "~/packages/hooks";
import { exportCourseProgress } from "@/service/api";

defineOptions({
  name: 'courseProgressTableHeaderOperation'
});

const { routerPushByKey } = useRouterPush();

interface Props {
  itemAlign?: NaiveUI.Align;
  loadingRefresh?: boolean;
  contentId?: number;
  phone?: string;
  nickname?: string;
  progressStatus?: number;
  orgId?: number;
}

const props = defineProps<Props>();

const { loading, startLoading, endLoading } = useLoading();

interface Emits {
  (e: 'reset'): void;
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<FilteredColumn[]>('columns', {
  default: () => []
});

async function exportProgress() {
  startLoading();
  let params = {content_id: props.contentId};
  if (props.phone) {
    params.phone = props.phone;
  }
  if (props.nickname) {
    params.nickname = props.nickname;
  }
  if (props.progressStatus) {
    params.progress_status = props.progressStatus;
  }
  if (props.orgId) {
    params.org_id = props.orgId;
  }
  const { data, error } = await exportCourseProgress(params);
  endLoading();
  if (error) {
    window.$message?.warning('导出失败');
    return;
  }
  const blob = new Blob([data])
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = '课程学习进度.xlsx' // 获取文件名
  link.click()
  URL.revokeObjectURL(url)

  window.$message?.success('导出成功');

  emit('reset');
}

function refresh() {
  emit('refresh');
}
</script>

<template>
  <NSpace :align="itemAlign" wrap justify="end" class="<sm:w-200px">
    <slot name="prefix"></slot>
    <slot name="default">
      <NButton size="small" ghost type="primary" @click="exportProgress" :loading="loading">
        <SvgIcon icon="file-icons:microsoft-excel" />
        {{ loading ? '导出中' : '导出' }}
      </NButton>
    </slot>
    <NButton size="small" @click="refresh">
      <template #icon>
        <icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loadingRefresh }" />
      </template>
      {{ $t('common.refresh') }}
    </NButton>
    <TableColumnSetting v-model:columns="columns" />
    <slot name="suffix"></slot>
  </NSpace>
</template>

<style scoped></style>
