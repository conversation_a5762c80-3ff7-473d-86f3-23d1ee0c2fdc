<script setup lang="ts">
import { $t } from '@/locales';
import { courseProgressStatusOptions } from '@/constants/business';

defineOptions({
  name: 'CourseSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type SearchParams = CommonType.RecordNullable<Api.SystemManage.CommonSearchParams & {
  phone: string;
  nickname: string;
  progress_status: number;
  org_id: number;
}>;

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:4" label="手机号" path="phone" class="pr-24px">
          <NInput v-model:value="model.phone" placeholder="请输入手机号" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="昵称" path="nickname" class="pr-24px">
          <NInput v-model:value="model.nickname" placeholder="请输入昵称" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="学习状态" path="progress_status" class="pr-24px">
          <NSelect v-model:value="model.progress_status" :options="courseProgressStatusOptions" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="所属机构" path="org_id" class="pr-24px">
          <SearchOrg v-model:value="model.org_id" clearable></SearchOrg>
        </NFormItemGi>
        <NFormItemGi span="24 s:3">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
