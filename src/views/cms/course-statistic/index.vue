<script setup lang="tsx">
import { NText } from 'naive-ui';
import { fetchCourseProgressList} from '@/service/api';
import { useTable } from '@/hooks/common/table';
import TableSearch from './modules/table-search.vue';
import TableHeaderOperation from './modules/table-header-operation.vue';
import CardData from "./modules/card-data.vue";
import DateFormat from '@/components/common/date/date-format.vue';
import { onBeforeMount, ref } from "vue";
import { useRouterPush } from "@/hooks/common/router";

const { route } = useRouterPush();

const contentId = ref();

onBeforeMount(() => {
  contentId.value = route.value.query.content_id;
});

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Cms.CourseProgress, typeof fetchCourseProgressList, 'index' | 'operate'>({
  apiFn: fetchCourseProgressList,
  apiParams: {
    current: 1,
    size: 20,
    content_id: route.value.query.content_id,
    phone: null,
    nickname: null,
    progress_status: null,
    org_id: null
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'phone',
      title: '手机号',
      align: 'center',
      minWidth: 100,
      render: row => {
        return row.user?.phone;
      }
    },
    {
      key: 'nickname',
      title: '昵称',
      align: 'center',
      minWidth: 120,
      render: row => {
        return row.user?.nickname;
      }
    },
    {
      key: 'org_name',
      title: '所属机构',
      align: 'center',
      minWidth: 120,
      render: row => {
        return row.user?.org?.name;
      }
    },
    {
      key: 'progress_status',
      title: '学习状态',
      align: 'center',
      minWidth: 120,
      render: row => {
        if (row.progress_status === 1) {
          return <NText>未学</NText>
        } else if(row.progress_status === 2) {
          return <NText>学习中</NText>
        } else {
          return <NText>已学</NText>
        }
      }
    },
    {
      key: 'progress',
      title: '学习进度',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'created_at',
      title: '购买时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    }
  ]
});

const checkedRowKeys = ref<string[]>([]);

</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <CardData :content-id="contentId" />
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="课程学习进度" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :showAdd="false"
          :showBatchDelete="false"
          :loadingRefresh="loading"
          :contentId="contentId"
          :phone="searchParams.phone"
          :nickname="searchParams.nickname"
          :progressStatus="searchParams.progress_status"
          :orgId="searchParams.org_id"
          @refresh="getData"

        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
