<script setup lang="tsx">
import { ref } from 'vue';
import { NButton, NFlex, NPopconfirm, NTag, NText } from 'naive-ui';
import { fetchContentList, deleteContent, batchDeleteContent } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { useRouterPush } from '@/hooks/common/router';
import { contentStatusLabel } from '@/constants/business';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnEllipsis from '@/components/common/table/column-ellipsis.vue';
import TableHeaderOperation from './modules/table-header-operation.vue';
import ActionRecommend from '../content/components/action-recommend.vue';
import ActionDisplay from '../content/components/action-display.vue';
import ContentPageCopy from '@/components/common/content/content-page-copy.vue';

const { routerPushByKey } = useRouterPush();

const classify = ref<Api.Cms.CategoryClassifyType>('course');

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Cms.Content, typeof fetchContentList, 'index' | 'operate'>({
  apiFn: fetchContentList,
  apiParams: {
    current: 1,
    size: 20,
    classify: classify.value,
    id: null,
    status: [1, 2, 3],
    keyword: null,
    category_id: null,
    recommend: null,
    created_at: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'category_id',
      title: '所属分类',
      align: 'left',
      minWidth: 100,
      render: row => {
        return row.category?.name;
      }
    },
    {
      key: 'title',
      title: '课程名称',
      align: 'left',
      minWidth: 120,
      render: row => {
        return <ColumnEllipsis text={row.title} line={160} />
      }
    },
    {
      key: 'teacher_name' as any,
      title: '讲师',
      align: 'left',
      width: 80,
      render: row => {
        return row.resource?.teacher_name;
      }
    },
    {
      key: 'view_limit',
      title: '课程价格',
      align: 'center',
      width: 100,
      render: row => {
        if (row.view_limit === 0) {
          return <NText>免费</NText>
        } else if(row.view_limit === 1) {
          return <NText>{row.charge_credit} 积分</NText>
        } else {
          return <NText>{row.charge_amount} 元</NText>
        }
      }
    },
    {
      key: 'course.hour' as any,
      title: '课时',
      align: 'center',
      width: 80,
      render: row => {
        return row.resource ? row.resource.hour : '-' ;
      }
    },
    {
      key: 'resource.hour_per_minutes' as any,
      title: ' 单位课时时长',
      align: 'center',
      width: 120,
      render: row => {
        return row.resource ? row.resource.hour_per_minutes+"分钟" : '-' ;
      }
    },
    {
      key: 'buy_course_users' as any,
      title: '购买人数',
      align: 'center',
      width: 80,
      render: row => {
        return row.resource ? row.resource?.buy_course_users : '-' ;
      }
    },
    {
      key: 'course.learning_count' as any,
      title: '学习人数',
      align: 'center',
      width: 80,
      render: row => {
        return row.resource ? row.resource.learning_count : '-' ;
      }
    },
    {
      key: 'recommend_at',
      title: '推荐',
      align: 'center',
      width: 80,
      render: row => {
        return <ActionRecommend rowData={row} />;
      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 80,
      render: row => {
        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'warning',
          2: 'success',
        };

        const label = contentStatusLabel[row.status];

        return <NTag type={tagMap[row.status]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'release_at',
      title: '发布时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.release_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 280,
      render: row => {
        return <NFlex size="small">
          <ContentPageCopy content={row} />
          <NButton type="primary" ghost size="small" disabled={row.status === 1} onClick={() => handleEdit(row)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
          <ActionDisplay rowData={row} onRefresh={getData} />
          <NButton type="primary" ghost size="small" onClick={() => goStatistic(row)}>
            课程统计
          </NButton>
        </NFlex>
      }
    }
  ]
});

const checkedRowKeys = ref<string[]>([]);

async function handleBatchDelete() {
  const { error } = await batchDeleteContent({ ids: checkedRowKeys.value })
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  checkedRowKeys.value = [];

  await getData();
}

async function handleDelete(row: Api.Cms.Content) {
  const { error } = await deleteContent(row.id)
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}

function handleEdit(row: Api.Cms.Content) {
  routerPushByKey('cms_content', { query: { id: row.id.toString() } }, true)
}

function getIndex(index: number) {
  return String(index);
}

function goStatistic(row: Api.Cms.Content) {
  routerPushByKey('cms_course-statistic', { query: { content_id: row.id.toString() } })
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch :classify="classify" v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="课程列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :classify="classify"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
