<script setup lang="tsx">
import { onBeforeMount, ref } from 'vue';
import { NButton, NPopconfirm, NSpace, NTag } from 'naive-ui';
import { batchDeleteSubject, deleteSubject, fetchSubjectList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { closeTab } from '@/utils/tab';
import { $t } from '@/locales';
import { subjectTypeLabel } from '@/constants/business';
import { useRouterPush } from '@/hooks/common/router';
import TableSearch from './modules/table-search.vue';
import TableHtml from './modules/table-html.vue';
import DateFormat from '@/components/common/date/date-format.vue';

const { route, routerPushByKey } = useRouterPush();

const topicId = ref<number>(Number.parseInt(route.value.query.topic_id as string, 10));

async function handleError() {
  await closeTab(route.value.name as string);

  await routerPushByKey('train_topic');
}

onBeforeMount(async () => {
  if (!topicId.value) {
    window.$message?.warning('参数错误');
    await handleError();
  }
});

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Train.Subject, typeof fetchSubjectList, 'index' | 'operate'>({
  apiFn: fetchSubjectList,
  apiParams: {
    current: 1,
    size: 20,
    id: null,
    topic_id: topicId.value,
    chapter_id: null,
    section_id: null,
    type: null,
    keyword: null,
    created_at: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'topic.name' as any,
      title: '题库名称',
      align: 'center',
      minWidth: 260
    },
    {
      key: 'intro',
      title: '试题题目',
      align: 'center',
      minWidth: 520,
      render: row => {
        return <TableHtml html={row.intro} />;
      }
    },
    {
      key: 'type',
      title: '试题类型',
      align: 'center',
      minWidth: 100,
      render: row => {
        const label = subjectTypeLabel[row.type];

        return (
          <NTag type="info" bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      minWidth: 150,
      render: row => {
        return (
          <NSpace justify="center">
            <NButton type="primary" size="small" onClick={() => handleEdit(row)}>
              编辑
            </NButton>
            <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
              {{
                default: () => $t('common.confirmDelete'),
                trigger: () => (
                  <NButton type="error" ghost size="small">
                    {$t('common.delete')}
                  </NButton>
                )
              }}
            </NPopconfirm>
          </NSpace>
        );
      }
    }
  ]
});

const checkedRowKeys = ref<string[]>([]);

function handleAdd() {
  routerPushByKey('train_subject-edit', { query: { topic_id: topicId.value.toString() } });
}

function handleEdit(row: Api.Train.Subject) {
  routerPushByKey('train_subject-edit', { query: { id: row.id.toString() } });
}

async function handleBatchDelete() {
  const { error } = await batchDeleteSubject({ ids: checkedRowKeys.value });
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  // eslint-disable-next-line require-atomic-updates
  checkedRowKeys.value = [];

  await getData();
}

async function handleDelete(id: number) {
  const { error } = await deleteSubject(id);
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="题目列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
