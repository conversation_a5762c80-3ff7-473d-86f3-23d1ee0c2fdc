<script setup lang="ts">
import { ref, onMounted } from "vue";
import type { TreeSelectOption } from "naive-ui";
import { searchChapter } from "@/service/api";
import { $t } from '@/locales';
import { subjectTypeOptions } from '@/constants/business';

defineOptions({
  name: 'SubjectSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const chapterOptions = ref<TreeSelectOption[]>([]);
const chapterSectionId = ref<string | null>(null);

type SearchParams = CommonType.RecordNullable<
  Api.SystemManage.CommonSearchParams & {
    id: string;
    keyword: string;
    topic_id: number;
    type: number;
    chapter_id: number;
    section_id: number;
  }
>;

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

function handleSelectChapterSectionId(value: string, option: TreeSelectOption ) {
  model.value.chapter_id = option.chapter_id as number;
  model.value.section_id = option.section_id as number;
}

async function handleChapterSections(topicId: number) {
  const { data } = await searchChapter({ topic_id: topicId });

  const options: TreeSelectOption[] = [];
  data?.forEach((item) => {
    const option: TreeSelectOption = {
      key: `c-${item.id}`,
      label: item.name,
      chapter_id: item.id,
      section_id: 0,
      children: [],
    };

    item.sections?.forEach(section => {
      option.children?.push({
        key: `s-${section.id}`,
        label: section.name,
        chapter_id: item.id,
        section_id: section.id
      })
    });

    options.push(option);
  });

  chapterOptions.value = options;
}

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}

onMounted(async () => {
  await handleChapterSections(model.value.topic_id);
})
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:4" label="ID" path="id" class="pr-24px">
          <NInput v-model:value="model.id" placeholder="输入ID" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="关键词" path="keyword" class="pr-24px">
          <NInput v-model:value="model.keyword" placeholder="输入关键词" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="类型" path="type" class="pr-24px">
          <NSelect v-model:value="model.type" :options="subjectTypeOptions" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:10" label="选择章节" path="type" class="pr-24px">
          <n-tree-select
            v-model:value="chapterSectionId"
            clearable
            placeholder="请选择章节"
            :default-expand-all="true"
            :options="chapterOptions"
            @update:value="handleSelectChapterSectionId"
          />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:7" label="创建时间" path="created_at" class="pr-24px">
          <DatetimeRange v-model:value="model.created_at" />
        </NFormItemGi>
        <NFormItemGi span="24 s:3">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
