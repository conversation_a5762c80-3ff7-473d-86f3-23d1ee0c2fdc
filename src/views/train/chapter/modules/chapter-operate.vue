<script setup lang="tsx">
import { computed, reactive, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { createChapter, updateChapter } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

/**
 * the type of operation
 *
 * - add: add user
 * - edit: edit user
 */
export type OperateType = 'add' | 'edit';

const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = ref<boolean>(false);
const operateType = ref<OperateType>('add');
const topicId = ref<number>(0);
const rowData = ref<Api.Train.Chapter | null>(null);

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: `新增章`,
    edit: `编辑章`
  };
  return titles[operateType.value];
});

type Model = {
  topic_id: number;
  name: string;
  example: number;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    topic_id: topicId.value,
    name: '',
    example: 0,
  };
}

const rules: Record<string, App.Global.FormRule> = {
  topic_id: defaultRequiredRule,
  name: defaultRequiredRule,
};

async function handleUpdateModelWhenEdit() {
  if (operateType.value === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (operateType.value === 'edit' && rowData.value) {
    Object.assign(model, {
      name: rowData.value.name,
      topic_id: rowData.value.topic_id,
      example: rowData.value.example
    });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  if (operateType.value === 'add') {
    const { error } = await createChapter(model);
    endLoading();
    if (error) {
      return;
    }

    window.$message?.success($t('common.addSuccess'));
  } else {
    const { error } = await updateChapter(rowData.value?.id as number, model);
    endLoading();
    if (error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  closeDrawer();
  emit('submitted');
}

function open(type: OperateType, selectTopicId: number, row: Api.Train.Chapter | null = null) {
  operateType.value = type;
  topicId.value = selectTopicId;
  rowData.value = row;

  handleUpdateModelWhenEdit();
  restoreValidation();

  visible.value = true;
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="680">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem label="名称" path="name" class="w-420px">
          <NInput v-model:value="model.name" />
        </NFormItem>
        <NFormItem label="案例" path="example" >
          <NSwitch
            v-model:value="model.example"
            :checked-value="1"
            :unchecked-value="0"
            :disabled="operateType == 'edit'"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
