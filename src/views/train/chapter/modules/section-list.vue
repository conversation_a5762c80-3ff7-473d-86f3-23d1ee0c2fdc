<script setup lang="tsx">
import { <PERSON><PERSON><PERSON><PERSON>, NPopconfirm, NSpace } from 'naive-ui';
import { ref } from "vue";
import { $t } from '@/locales';
import { useTable } from '@/hooks/common/table';
import { deleteSection, fetchSectionList } from '@/service/api';
import DateFormat from '@/components/common/date/date-format.vue';
import SectionOperate from "./section-operate.vue";

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const visible = ref<boolean>(false);
const chapter = ref<Api.Train.Chapter | null>(null);
const sectionOperateRef = ref();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Train.Section, typeof fetchSectionList, 'index' | 'operate'>({
  immediate: false,
  apiFn: fetchSectionList,
  apiParams: {
    current: 1,
    size: 10,
    chapter_id: null,
    created_at: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      minWidth: 80
    },
    {
      key: 'name',
      title: '名称',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'subjects_count',
      title: '题目数量',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      minWidth: 150,
      render: row => {
        return (
          <NSpace justify="center">
            <NButton type="primary" size="small" onClick={() => handleEdit(row)}>
              编辑
            </NButton>
            <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
              {{
                default: () => $t('common.confirmDelete'),
                trigger: () => (
                  <NButton type="error" ghost size="small">
                    {$t('common.delete')}
                  </NButton>
                )
              }}
            </NPopconfirm>
          </NSpace>
        );
      }
    }
  ]
});

function handleEdit(row: Api.Train.Section) {
  sectionOperateRef.value.open('edit', row.chapter_id, row, !!row.chapter?.example);
}

async function handleDelete(id: number) {
  const { error } = await deleteSection(id);

  if (error) {
    return;
  }

  window.$message?.success('操作成功');

  await getData();

  emit('reload');
}

function getIndex(index: number) {
  return String(index);
}

function open(row: Api.Train.Chapter) {
  chapter.value = row;

  updateSearchParams({
    chapter_id: row.id
  });

  getData().then(() => {
    visible.value = true;
  });
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="980">
    <NDrawerContent title="节列表" :native-scrollbar="false" closable>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NDrawerContent>
  </NDrawer>
  <SectionOperate ref="sectionOperateRef" @submitted="getData" />
</template>

<style scoped></style>
