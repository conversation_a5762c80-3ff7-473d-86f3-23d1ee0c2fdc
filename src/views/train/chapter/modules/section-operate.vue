<script setup lang="tsx">
import { computed, reactive, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { createSection, updateSection } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';
import type { AttachmentFile } from '@/components/common/form/form-attachment.vue';

/**
 * the type of operation
 *
 * - add: add user
 * - edit: edit user
 */
export type OperateType = 'add' | 'edit';

const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = ref<boolean>(false);
const operateType = ref<OperateType>('add');
const chapterId = ref<number>(0);
const example = ref<boolean>(false);
const rowData = ref<Api.Train.Section | null>(null);
const attachmentFiles = ref<AttachmentFile[]>([]);

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: `新增节`,
    edit: `编辑节`
  };
  return titles[operateType.value];
});

type Model = {
  chapter_id: number;
  name: string;
  example_content?: string;
  upload_files?: AttachmentFile[];
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    chapter_id: chapterId.value,
    name: '',
    example_content: undefined,
    upload_files: undefined,
  };
}

function getRules() {
  let rules: Record<string, App.Global.FormRule>;

  if (example.value) {
    rules = {
      chapter_id: defaultRequiredRule,
      name: defaultRequiredRule,
      example_content: defaultRequiredRule
    };
  } else {
    rules = {
      chapter_id: defaultRequiredRule,
      name: defaultRequiredRule,
    };
  }

  return rules;
}

async function handleUpdateModelWhenEdit() {
  if (operateType.value === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (operateType.value === 'edit' && rowData.value) {
    let exampleContent: string = '';

    if (rowData.value?.example) {
      exampleContent = rowData.value.example.content;
    }

    Object.assign(model, {
      name: rowData.value.name,
      chapter_id: rowData.value.chapter_id,
      example_content: exampleContent
    });
  }
}

function closeDrawer() {
  visible.value = false;
}

function handleUploadSuccess(file: Api.System.UploaderFile) {
  attachmentFiles.value.push({
    type: 'example',
    type_id: 0,
    filename: file.filename,
    filepath: file.key,
    url: file.url,
    created_at: file.created_at as string
  });
}

async function handleSubmit() {
  await validate();

  if (attachmentFiles.value.length > 0) {
    model.upload_files = attachmentFiles.value;
  }

  startLoading();

  if (operateType.value === 'add') {
    const {error} = await createSection(model);
    endLoading();
    if (error) {
      return;
    }

    window.$message?.success($t('common.addSuccess'));
  } else {
    const {error} = await updateSection(rowData.value?.id as number, model);
    endLoading();
    if (error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  closeDrawer();
  emit('submitted');
}

function open(type: OperateType, selectChapterId: number, row: Api.Train.Section | null = null, isExample: boolean = false) {
  operateType.value = type;
  chapterId.value = selectChapterId;
  rowData.value = row;
  example.value = isExample;

  handleUpdateModelWhenEdit();
  restoreValidation();

  visible.value = true;
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="680">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="getRules()" label-placement="left" :label-width="100">
        <NFormItem label="名称" path="name" class="w-420px">
          <NInput v-model:value="model.name"/>
        </NFormItem>
        <NFormItem v-if="example" label="案例" path="example_content" class="w-600px">
          <EditorSimple
            v-model:value="model.example_content"
            :height="540"
            @upload-success="handleUploadSuccess"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
