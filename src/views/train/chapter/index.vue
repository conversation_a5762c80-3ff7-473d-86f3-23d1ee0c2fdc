<script setup lang="tsx">
import { onBeforeMount, ref } from 'vue';
import { NButton, NPopconfirm, NSpace } from 'naive-ui';
import { deleteChapter, fetchChapterList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { useRouterPush } from '@/hooks/common/router';
import { closeTab } from '@/utils/tab';
import TableSearch from './modules/chapter-search.vue';
import ChapterOperate from "./modules/chapter-operate.vue";
import SectionOperate from "./modules/section-operate.vue";
import SectionList from './modules/section-list.vue';
import DateFormat from '@/components/common/date/date-format.vue';

const { route, routerPushByKey } = useRouterPush();

const topicId = ref<number>(Number.parseInt(route.value.query.topic_id as string, 10));

const chapterOperateRef = ref();
const sectionOperateRef = ref();
const sectionListRef = ref();

async function handleError() {
  await closeTab(route.value.name as string);

  await routerPushByKey('train_topic');
}

onBeforeMount(async () => {
  if (!topicId.value) {
    window.$message?.warning('参数错误');
    await handleError();
  }
});

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Train.Chapter, typeof fetchChapterList, 'index' | 'operate'>({
  apiFn: fetchChapterList,
  apiParams: {
    current: 1,
    size: 20,
    id: null,
    topic_id: topicId.value,
    created_at: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      minWidth: 80
    },
    {
      key: 'name',
      title: '名称',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'topic_id',
      title: '所属题库',
      align: 'center',
      minWidth: 120,
      render: (row) => {
        return row.topic?.name ?? '-';
      },
    },
    {
      key: 'sections_count',
      title: '节数量',
      align: 'center',
      minWidth: 100,
      render: (row) => {
        return <NButton text tag="a" type="primary" onClick={() => handleSection('list', row)}>{row?.sections_count}</NButton>;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 300,
      render: row => {
        return (
          <NSpace justify="center">
            <NButton type="primary" size="small" onClick={() => handleSection('create', row)}>
              新增节
            </NButton>
            <NButton type="primary" size="small" onClick={() => handleSection('list', row)}>
              节管理
            </NButton>
            <NButton type="primary" size="small" onClick={() => handleEdit(row)}>
              编辑
            </NButton>
            <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
              {{
                default: () => $t('common.confirmDelete'),
                trigger: () => (
                  <NButton type="error" ghost size="small">
                    {$t('common.delete')}
                  </NButton>
                )
              }}
            </NPopconfirm>
          </NSpace>
        );
      }
    }
  ]
});

function handleAdd() {
  chapterOperateRef.value.open('add', topicId.value, null);
}

function handleEdit(row: Api.Train.Chapter) {
  chapterOperateRef.value.open('edit', topicId.value, row);
}

function handleSection(type: string, row: Api.Train.Chapter) {
  switch (type) {
    case 'create':
      sectionOperateRef.value.open('add', row.id, null, !!row.example);
      break;
    case 'list':
      sectionListRef.value.open(row);
      break;
  }
}

async function handleDelete(id: number) {
  const { error } = await deleteChapter(id);
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="章节列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
    <ChapterOperate ref="chapterOperateRef" @submitted="getData" />
    <SectionOperate ref="sectionOperateRef" @submitted="getData" />
    <SectionList ref="sectionListRef" @reload="getData" />
  </div>
</template>

<style scoped></style>
