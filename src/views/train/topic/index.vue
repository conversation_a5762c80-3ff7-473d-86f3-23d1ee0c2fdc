<script setup lang="tsx">
import { ref } from 'vue';
import { NButton, NSpace } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchTopicList, updateTopic } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import DateFormat from '@/components/common/date/date-format.vue';
import OperateSort from '@/components/common/table/operate-sort.vue';
import { useRouterPush } from '@/hooks/common/router';
import TableSearch from './modules/table-search.vue';
import OperateDrawer, { type OperateType } from './modules/operate-drawer.vue';
import TableExamSetting from './modules/table-exam-setting.vue';
import TableImport from './modules/table-import.vue';

const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();
const { routerPushByKey } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Train.Topic, typeof fetchTopicList, 'index' | 'operate'>({
  apiFn: fetchTopicList,
  apiParams: {
    current: 1,
    size: 20,
    id: null,
    name: null,
    created_at: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 100
    },
    {
      key: 'name',
      title: '名称',
      align: 'center',
      width: 200
    },
    {
      key: 'exam_time',
      title: '考试时间',
      align: 'center',
      width: 100,
      render: row => {
        return `${row.exam_time} 分钟`;
      }
    },
    {
      key: 'pass_score',
      title: '通过分数',
      align: 'center',
      width: 100
    },
    {
      key: 'subjects_count',
      title: '题目数量',
      align: 'center',
      width: 100,
      render: row => {
        return (
          <NButton type="primary" size="small" text onClick={() => handleSubject(row.id, 'list')}>
            {row.subjects_count}
          </NButton>
        );
      }
    },
    {
      key: 'sort',
      title: '排序',
      align: 'center',
      width: 100,
      render: row => {
        return <OperateSort id={row.id} sort={row.sort} updateSort={updateTopic} onReset={getData} />;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 240,
      render: row => {
        return (
          <NSpace justify="start" size="small">
            <NButton type="primary" size="small" onClick={() => handleSubject(row.id, 'subject-list')}>
              试题管理
            </NButton>
            <NButton type="primary" size="small" onClick={() => handleSubject(row.id, 'chapter')}>
              章节管理
            </NButton>
            <TableImport rowData={row} onReset={getData} />
            <NButton type="primary" size="small" onClick={() => handleEdit(row)}>
              编辑信息
            </NButton>
            <NButton type="primary" size="small" onClick={() => handleSetting(row)}>
              试卷配置
            </NButton>
          </NSpace>
        );
      }
    }
  ]
});

const examRef = ref();
const operateType = ref<OperateType>('add');
const editingData = ref<Api.Train.Topic | null>(null);

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;
  openDrawer();
}

function handleEdit(row: Api.Train.Topic) {
  operateType.value = 'edit';
  editingData.value = row;
  openDrawer();
}

function handleSetting(row: Api.Train.Topic) {
  examRef.value.open(row);
}

function handleSubject(id: number, type: string) {
  switch (type) {
    case 'subject-create':
      routerPushByKey('train_subject-edit', { query: { topic_id: id.toString() } });
      break;
    case 'subject-list':
      routerPushByKey('train_subject', { query: { topic_id: id.toString() } });
      break;
    case 'chapter':
      routerPushByKey('train_chapter', { query: { topic_id: id.toString() } });
      break;
  }
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="题库列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
      <TableExamSetting ref="examRef" @submitted="getData" />
    </NCard>
  </div>
</template>

<style scoped></style>
