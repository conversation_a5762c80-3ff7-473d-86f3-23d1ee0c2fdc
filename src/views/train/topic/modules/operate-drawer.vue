<script setup lang="tsx">
import { computed, onMounted, reactive, ref, watch } from 'vue';
import type { CascaderOption } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { createTopic, fetchCategoryList, updateTopic } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

defineOptions({
  name: 'TopicOperateDrawer'
});

/**
 * the type of operation
 *
 * - add: add user
 * - edit: edit user
 */
export type OperateType = 'add' | 'edit';

const { loading, startLoading, endLoading } = useLoading();

interface Props {
  operateType: OperateType;
  rowData?: Api.Train.Topic | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: `新增题库`,
    edit: `编辑题库`
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Train.Topic, 'name' | 'sort'> & {
  course_category_id?: number;
  course_content_id?: number;
  course_content_ids: number[];
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    course_category_id: undefined,
    course_content_id: undefined,
    course_content_ids: [],
    sort: 0
  };
}

type RuleKey = Extract<keyof Model, 'name'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule
};

const categoryList = ref<CascaderOption[]>([]);

async function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, {
      name: props.rowData.name,
      course_content_id: props.rowData.course_content_id,
      sort: props.rowData.sort,
      course_category_id: props.rowData.course_category_id ? props.rowData.course_category_id : undefined,
      course_content_ids: props.rowData.course_content_id ? [props.rowData.course_content_id] : []
    });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  if (model.course_content_ids.length > 0) {
    model.course_content_id = model.course_content_ids[0];
  } else {
    model.course_content_id = 0;
  }

  if (!model.course_category_id) {
    model.course_category_id = 0;
  }

  startLoading();

  if (props.operateType === 'add') {
    const { error } = await createTopic(model);
    endLoading();
    if (error) {
      return;
    }

    window.$message?.success($t('common.addSuccess'));
  } else {
    const { error } = await updateTopic(props.rowData?.id as number, model);
    endLoading();
    if (error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});

onMounted(async () => {
  const { data, error } = await fetchCategoryList({ classify: 'course' });

  if (!error) {
    categoryList.value = data;
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="680">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem label="名称" path="name" class="w-420px">
          <NInput v-model:value="model.name" />
        </NFormItem>
        <NFormItem label="讲解课程分类" path="course_category_id" class="w-420px">
          <NCascader
            v-model:value="model.course_category_id"
            :options="categoryList"
            value-field="id"
            label-field="name"
            check-strategy="child"
            clearable
          />
        </NFormItem>
        <NFormItem label="讲解课程" path="course_content_ids">
          <ContentRelation v-model:value="model.course_content_ids" :max="1" classify="course" />
        </NFormItem>
        <NFormItem label="排序" path="sort" class="w-220px">
          <NSpace vertical>
            <NInputNumber v-model:value="model.sort" :min="0" :max="10000" />
            <FormTips :tips="['数字越大越靠前']" />
          </NSpace>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
