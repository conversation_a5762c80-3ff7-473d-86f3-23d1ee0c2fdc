<script setup lang="ts">
import { importTopic } from '@/service/api';
import { useLoading } from '@sa/hooks';

defineOptions({
  name: 'TopicImport'
});

interface Props {
  rowData: Api.Train.Topic;
}

interface Emits {
  (e: 'reset'): void;
}

const emit = defineEmits<Emits>();
const props = defineProps<Props>();

const { loading, startLoading, endLoading } = useLoading();


async function handleUpload(response: Api.System.UploaderFile | undefined) {
  if (!response) {
    window.$message?.warning('导入失败');
    return;
  }

  startLoading();

  const { error } = await importTopic(props.rowData.id, response?.key);

  endLoading();

  if (error) {
    window.$message?.warning('导入失败');
    return;
  }

  window.$message?.success('导入成功');

  emit('reset');
}

</script>

<template>
  <UploadButton @success-response="handleUpload">
    <NButton type="primary" size="small" :disabled="loading">
      {{ loading ? '正在执行' : '导入试题' }}
    </NButton>
  </UploadButton>
</template>

<style scoped></style>
