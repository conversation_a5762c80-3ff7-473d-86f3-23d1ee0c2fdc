<script setup lang="ts">
import { ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { updateTopic, getSubjectTypeCount } from '@/service/api';
import { examConfigTypeRecord } from '@/constants/business';
import { $t } from '@/locales';

type Model = {
  exam_time: number;
  pass_score: number;
  exam_config: Api.Train.TopicExamConfig;
};

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const visible = ref<boolean>(false);
const loading = ref<boolean>(false);
const detail = ref<Api.Train.Topic | null>(null);
const typeCount = ref<Api.Train.TopicSubjectTypeCount>({
  single_choice: 100,
  multiple_choice: 0,
  judge: 0,
});

const model = ref<Model>(createDefaultModel());

function createDefaultModel(): Model {
  return {
    exam_time: 60,
    pass_score: 60,
    exam_config: {
      single_choice: {
        count: 100,
        score: 1
      },
      multiple_choice: {
        count: 0,
        score: 2
      },
      judge: {
        count: 0,
        score: 1
      }
    }
  };
}

const rules: Record<string, App.Global.FormRule> = {
  exam_time: defaultRequiredRule,
  pass_score: defaultRequiredRule,
};

function getScore(type: Api.Train.TopicExamConfigType): number {
  if (model.value.exam_config) {
    const config = model.value.exam_config;

    return config[type].count * config[type].score;
  } else {
    return 0;
  }
}

function subtotal(field: 'count' | 'score') {
  let total = 0;

  if (model.value.exam_config) {
    const config = model.value.exam_config;

    for (const key in config) {
      const k = key as Api.Train.TopicExamConfigType;

      if (field == 'count') {
        total = total + config[k][field];
      } else {
        total = total + getScore(k);
      }
    }
  }

  return total;
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  if (subtotal('score') > 100) {
    window.$message?.error('题目数量或分值设置有误！');
    return;
  }

  if (model.value.exam_config) {
    const config = model.value.exam_config;

    for (const key in config) {
      const k = key as Api.Train.TopicExamConfigType;

      if (config[k].count > typeCount.value[k]) {
        window.$message?.error(`${examConfigTypeRecord[k]}数量不能超过${typeCount.value[k]}`);
        return;
      }
    }
  }

  loading.value = true;

  const { error } = await updateTopic(detail.value?.id as number, model.value);

  loading.value = false;

  if (error) {
    return;
  }

  emit('submitted');

  closeDrawer();
}

async function init(topicId: number) {
  const { error, data } = await getSubjectTypeCount({ topic_id: topicId });

  if (error) {
    return;
  }

  typeCount.value = data;
}

function open(row: Api.Train.Topic) {
  detail.value = row;
  model.value = createDefaultModel();

  model.value.exam_time = row.exam_time;
  model.value.pass_score = row.pass_score;

  if (row.exam_config) {
    model.value.exam_config = row.exam_config;
  }

  init(row.id);

  visible.value = true;
}

defineExpose({
  open
})
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="780">
    <NDrawerContent title="试卷配置" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem v-if="visible" label="试卷配置" path="exam_config">
          <NCard>
            <NSpace vertical>
              <n-grid :x-gap="12" :y-gap="8">
                <n-grid-item :span="3" class="flex-center">
                  单选题
                </n-grid-item>
                <n-grid-item :span="9">
                  <div class="exam-config-item">
                    <div class="pr-2">数量</div>
                    <NInputNumber v-model:value="model.exam_config.single_choice.count" :min="0" :max="100" class="w-120px">
                      <template #suffix>
                        题
                      </template>
                    </NInputNumber>
                    <div class="pl-2">共{{ typeCount.single_choice }}题</div>
                  </div>
                </n-grid-item>
                <n-grid-item :span="7">
                  <div class="exam-config-item">
                    <div class="pr-2">分值</div>
                    <NInputNumber v-model:value="model.exam_config.single_choice.score" :min="0" :max="100" class="w-120px">
                      <template #suffix>
                        分
                      </template>
                    </NInputNumber>
                  </div>
                </n-grid-item>
                <n-grid-item :span="5">
                  <div>合计 <NText class="text-18px font-bold">{{ getScore('single_choice') }}</NText> 分</div>
                </n-grid-item>
              </n-grid>

              <n-grid :x-gap="12" :y-gap="8">
                <n-grid-item :span="3" class="flex-center">
                  多选题
                </n-grid-item>
                <n-grid-item :span="9">
                  <div class="exam-config-item">
                    <div class="pr-2">数量</div>
                    <NInputNumber v-model:value="model.exam_config.multiple_choice.count" :min="0" :max="100" class="w-120px">
                      <template #suffix>
                        题
                      </template>
                    </NInputNumber>
                    <div class="pl-2">共{{ typeCount.multiple_choice }}题</div>
                  </div>
                </n-grid-item>
                <n-grid-item :span="7">
                  <div class="exam-config-item">
                    <div class="pr-2">分值</div>
                    <NInputNumber v-model:value="model.exam_config.multiple_choice.score" :min="0" :max="100" class="w-120px">
                      <template #suffix>
                        分
                      </template>
                    </NInputNumber>
                  </div>
                </n-grid-item>
                <n-grid-item :span="4">
                  <div>合计 <NText class="text-18px font-bold">{{ getScore('multiple_choice') }}</NText> 分</div>
                </n-grid-item>
              </n-grid>

              <n-grid :x-gap="12" :y-gap="8">
                <n-grid-item :span="3" class="flex-center">
                  判断题
                </n-grid-item>
                <n-grid-item :span="9">
                  <div class="exam-config-item">
                    <div class="pr-2">数量</div>
                    <NInputNumber v-model:value="model.exam_config.judge.count" :min="0" :max="100" class="w-120px">
                      <template #suffix>
                        题
                      </template>
                    </NInputNumber>
                    <div class="pl-2">共{{ typeCount.judge }}题</div>
                  </div>
                </n-grid-item>
                <n-grid-item :span="7">
                  <div class="exam-config-item">
                    <div class="pr-2">分值</div>
                    <NInputNumber v-model:value="model.exam_config.judge.score" :min="0" :max="100" class="w-120px">
                      <template #suffix>
                        分
                      </template>
                    </NInputNumber>
                  </div>
                </n-grid-item>
                <n-grid-item :span="5">
                  <div>合计 <NText class="text-18px font-bold">{{ getScore('judge') }}</NText> 分</div>
                </n-grid-item>
              </n-grid>

              <NDivider />

              <NSpace justify="center">
                <div class="w-120px">共计 <NText class="text-18px font-bold">{{ subtotal('count') }}</NText> 题</div>
                <div class="w-120px">共计 <NText class="text-18px font-bold">{{ subtotal('score') }}</NText> 分</div>
              </NSpace>
            </NSpace>
          </NCard>
        </NFormItem>
        <NFormItem label="考试时间" path="exam_time" class="w-150px">
          <NInputNumber v-model:value="model.exam_time" :min="1" :max="180">
            <template #suffix>
              分钟
            </template>
          </NInputNumber>
        </NFormItem>
        <NFormItem label="设置及格分数" path="pass_score" class="w-150px">
          <NInputNumber v-model:value="model.pass_score" :min="1" :max="100">
            <template #suffix>
              分
            </template>
          </NInputNumber>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
  .exam-config-item {
    display: flex;
    align-items: center;
    justify-content: start;
  }
</style>
