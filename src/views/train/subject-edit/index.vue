<script setup lang="ts">
import { onMounted, reactive, ref} from 'vue';
import type { TreeSelectOption } from 'naive-ui'
import { useTabStore } from '@/store/modules/tab';
import { useLoading } from '~/packages/hooks';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { createSubject, getSubject, updateSubject, searchChapter } from '@/service/api';
import { closeTab } from '@/utils/tab';
import { $t } from '@/locales';
import type { AttachmentFile } from '@/components/common/form/form-attachment.vue';
import { useRouterPush } from '@/hooks/common/router';
import FormOptions, { type FormOption } from './modules/form-options.vue';

defineOptions({
  name: 'SubjectEdit'
});

const tabStore = useTabStore();
const { route, routerPushByKey } = useRouterPush();
const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const pageLoading = ref<boolean>(true);
const subject = ref<Api.Train.Subject | null>(null);
const topicId = ref<number>(0);
const attachmentFiles = ref<AttachmentFile[]>([]);
const chapterOptions = ref<TreeSelectOption[]>([]);
const chapterSelectedId = ref<string | null>(null);

type Model = Pick<Api.Train.Subject, 'intro' | 'type' | 'judge_correct' | 'answer' | 'analysis'> & {
  topic_id: number;
  chapter_id: number;
  section_id: number;
  options?: FormOption[];
  option_single?: number | null;
  option_multiple?: number[] | null;
  upload_files?: AttachmentFile[];
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    topic_id: topicId.value,
    chapter_id: 0,
    section_id: 0,
    intro: '',
    type: 1,
    judge_correct: 1,
    answer: '',
    analysis: '',
    options: [],
    option_single: null,
    option_multiple: null,
    upload_files: undefined
  };
}

const rules: Record<string, App.Global.FormRule>[] = [
  {
    topic_id: defaultRequiredRule,
    intro: defaultRequiredRule,
    type: defaultRequiredRule,
    options: defaultRequiredRule,
    option_single: defaultRequiredRule
  },
  {
    topic_id: defaultRequiredRule,
    intro: defaultRequiredRule,
    type: defaultRequiredRule,
    options: defaultRequiredRule,
    option_multiple: defaultRequiredRule
  },
  {
    topic_id: defaultRequiredRule,
    intro: defaultRequiredRule,
    type: defaultRequiredRule,
    judge_correct: defaultRequiredRule
  },
  {
    topic_id: defaultRequiredRule,
    intro: defaultRequiredRule,
    type: defaultRequiredRule,
    answer: defaultRequiredRule
  }
];

function handleUpdateModelWhenEdit() {
  if (subject.value) {
    let optionSingle = 0;
    const optionMultiple: number[] = [];

    if (subject.value.type === 1) {
      subject.value.options?.forEach((item, index) => {
        if (item.is_correct === 1) {
          optionSingle = index;
        }
      });
    } else if (subject.value.type === 2) {
      subject.value.options?.forEach((item, index) => {
        if (item.is_correct === 1) {
          optionMultiple.push(index);
        }
      });
    }

    Object.assign(model, {
      topic_id: subject.value.topic_id,
      intro: subject.value.intro,
      type: subject.value.type,
      judge_correct: subject.value.judge_correct,
      answer: subject.value.answer,
      analysis: subject.value.analysis,
      options: subject.value.options,
      option_single: optionSingle,
      option_multiple: optionMultiple
    });

    if (subject.value.attachments && subject.value.attachments.length > 0) {
      subject.value.attachments.forEach(item => {
        attachmentFiles.value.push({
          type: item.target_type,
          type_id: item.target_id,
          filename: item.file.filename,
          filepath: '',
          url: '',
          created_at: item.created_at
        });
      });
    }
  } else {
    Object.assign(model, createDefaultModel());
  }
}

function handleUploadSuccess(file: Api.System.UploaderFile) {
  attachmentFiles.value.push({
    type: 'subject',
    type_id: 0,
    filename: file.filename,
    filepath: file.key,
    url: file.url,
    created_at: file.created_at as string
  });
}

async function handleSubmit() {
  await validate();

  const formData = { ...model };

  if ([3, 4].includes(formData.type)) {
    formData.options = undefined;
    formData.option_single = undefined;
    formData.option_multiple = undefined;
  } else {
    // 单选题、多选题
    if (!formData.options) {
      return;
    }

    let checkName = true;
    const optionLabel = formData.type === 1 ? '单选题' : '多选题';

    for (const item of formData.options) {
      if (!item.name) {
        checkName = false;
        break;
      }
    }

    if (!checkName) {
      window.$message?.warning(`请设置${optionLabel}的选项`);
      return;
    }

    formData.options.forEach(item => {
      item.is_correct = 0;
    });

    if (formData.type === 1) {
      if (formData.option_single !== null && formData.option_single !== undefined) {
        formData.options[formData.option_single].is_correct = 1;
      }
    } else {
      formData.options.forEach((item, index) => {
        if (formData.option_multiple?.includes(index)) {
          item.is_correct = 1;
        }
      });
    }

    formData.judge_correct = 0;
    formData.option_single = undefined;
    formData.option_multiple = undefined;
  }

  if (attachmentFiles.value.length > 0) {
    formData.upload_files = attachmentFiles.value;
  }

  startLoading();

  if (subject.value) {
    const { error } = await updateSubject(subject.value.id, formData);
    endLoading();
    if (error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  } else {
    const { error } = await createSubject(formData);
    endLoading();
    if (error) {
      return;
    }

    window.$message?.success($t('common.addSuccess'));
  }

  await restoreValidation();

  await closeTab(route.value.name as string);

  await routerPushByKey('train_subject', { query: { topic_id: topicId.value.toString() } });
}

// 处理级联数据
function handleSelectChapterSectionId(value: string, option: TreeSelectOption ) {
  model.chapter_id = option.chapter_id as number;
  model.section_id = option.section_id as number;
}

async function handleChapterSections(topicId: number) {
  const { data } = await searchChapter({ topic_id: topicId });

  const options: TreeSelectOption[] = [];
  data?.forEach((item) => {
    const option: TreeSelectOption = {
      key: `c-${item.id}`,
      label: item.name,
      chapter_id: item.id,
      section_id: 0,
      children: [],
    };

    item.sections?.forEach(section => {
      option.children?.push({
        key: `s-${section.id}`,
        label: section.name,
        chapter_id: item.id,
        section_id: section.id
      })
    });

    options.push(option);
  });

  chapterOptions.value = options;
}

async function init(id: string) {
  const { data, error } = await getSubject(Number.parseInt(id, 10));

  if (error) {
    await handleError();
    return;
  }

  subject.value = data;
  topicId.value = data?.topic_id;

  await handleChapterSections(topicId.value);

  if (subject.value?.section_id > 0) {
    setTimeout(() => {
      chapterSelectedId.value = `s-${subject.value?.section_id}`

      console.log(chapterSelectedId.value);
    }, 1000)
  } else if (subject.value?.chapter_id > 0) {
    setTimeout(() => {
      chapterSelectedId.value = `c-${subject.value?.chapter_id}`
    }, 1000)
  }
}

async function handleError() {
  await closeTab(route.value.name as string);

  await routerPushByKey('train_topic');
}

onMounted(async () => {
  if (route.value.query.id) {
    await init(route.value.query.id as string);
  }

  if (!topicId.value && route.value.query.topic_id) {
    topicId.value = Number.parseInt(route.value.query.topic_id as string, 10);

    await handleChapterSections(topicId.value);
  }

  if (!topicId.value) {
    window.$message?.warning('参数错误');
    await handleError();
    return;
  }

  pageLoading.value = false;

  tabStore.setTabLabel(`${subject.value ? '编辑' : '新增'}题目`);

  handleUpdateModelWhenEdit();
});

</script>

<template>
  <NSpace vertical :size="16">
    <NCard v-if="!pageLoading" class="pl-10%">
      <NForm ref="formRef" :model="model" :rules="rules[model.type - 1]">
        <NFormItem label="选择章节" class="w-150">
          <n-tree-select
            v-model:value="chapterSelectedId"
            clearable
            placeholder="请选择章节"
            :default-expand-all="true"
            :options="chapterOptions"
            @update:value="handleSelectChapterSectionId"
          />
        </NFormItem>
        <NFormItem label="试题类型" path="type">
          <NRadioGroup v-model:value="model.type" :disabled="subject !== null">
            <NRadio label="单选题" :value="1" />
            <NRadio label="多选题" :value="2" />
            <NRadio label="判断题" :value="3" />
            <NRadio label="问答题" :value="4" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="试题题目" path="intro">
          <EditorSimple v-model:value="model.intro" :width="700" :height="240" @upload-success="handleUploadSuccess" />
        </NFormItem>
        <view v-if="model.type === 3">
          <NFormItem label="答案" path="judge_correct">
            <NRadioGroup v-model:value="model.judge_correct">
              <NRadio label="对" :value="1" />
              <NRadio label="错" :value="2" />
            </NRadioGroup>
          </NFormItem>
        </view>
        <view v-else-if="model.type === 4">
          <NFormItem label="答案" path="answer">
            <EditorSimple
              v-model:value="model.answer"
              :width="700"
              :height="260"
              @upload-success="handleUploadSuccess"
            />
          </NFormItem>
        </view>
        <view v-else>
          <NFormItem label="试题选项" path="options">
            <FormOptions v-model:value="model.options" v-model:attachment-files="attachmentFiles" />
          </NFormItem>
          <NFormItem v-if="model.type === 1" label="答案" path="option_single">
            <NRadioGroup v-model:value="model.option_single" size="large">
              <NSpace size="large">
                <NRadio v-for="(item, index) in model.options" :key="index" :label="item.prefix" :value="index" />
              </NSpace>
            </NRadioGroup>
          </NFormItem>
          <NFormItem v-else label="答案" path="option_multiple">
            <NCheckboxGroup v-model:value="model.option_multiple" size="large">
              <NSpace size="large">
                <NCheckbox v-for="(item, index) in model.options" :key="index" :label="item.prefix" :value="index" />
              </NSpace>
            </NCheckboxGroup>
          </NFormItem>
        </view>
        <NFormItem label="解析" path="analysis">
          <EditorSimple
            v-model:value="model.analysis"
            :width="700"
            :height="360"
            @upload-success="handleUploadSuccess"
          />
        </NFormItem>
        <NFormItem v-if="attachmentFiles.length > 0" label="关联附件">
          <FormAttachment v-model:value="attachmentFiles" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton type="primary" :disabled="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NCard>
    <NCard v-else class="pl-10%">
      <NSpace vertical>
        <NSkeleton class="skeleton" height="40px" width="60%" />
        <NSkeleton class="skeleton" height="80px" width="60%" />
        <NSkeleton class="skeleton" height="40px" width="60%" />
        <NSkeleton class="skeleton" height="40px" width="60%" />
        <NSkeleton class="skeleton" height="40px" width="60%" />
        <NSkeleton class="skeleton" height="40px" width="60%" />
        <NSkeleton class="skeleton" height="50px" width="50%" />
        <NSkeleton class="skeleton" height="200px" width="60%" />
      </NSpace>
    </NCard>
  </NSpace>
</template>

<style scoped>

</style>
