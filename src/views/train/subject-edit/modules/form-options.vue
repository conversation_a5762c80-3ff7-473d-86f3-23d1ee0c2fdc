<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import type { AttachmentFile } from '@/components/common/form/form-attachment.vue';

export type FormOption = {
  id: number;
  prefix: string;
  name: string;
  is_correct: number;
};

const formRef = ref(null);
const optionList = ref<FormOption[]>([]);
const prefixOptions = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];

const value = defineModel<FormOption[]>('value', {
  default: []
});

const attachmentFiles = defineModel<AttachmentFile[]>('attachmentFiles', {
  default: []
});

function handleAdd() {
  const length = optionList.value.length;

  if (length >= prefixOptions.length) {
    return;
  }

  optionList.value.push({
    id: 0,
    prefix: prefixOptions[length],
    name: '',
    is_correct: 0
  });
}

function handleRemove(index: number) {
  optionList.value.splice(index, 1);

  optionList.value.forEach((item, rIndex) => {
    item.prefix = prefixOptions[rIndex];
  });
}

function handleUploadSuccess(file: Api.System.UploaderFile) {
  attachmentFiles.value.push({
    type: 'subject',
    type_id: 0,
    filename: file.filename,
    filepath: file.key,
    url: file.url,
    created_at: file.created_at as string
  });
}

watch(optionList.value, () => {
  value.value = optionList.value;
});

onMounted(() => {
  if (value.value?.length > 0) {
    value.value.forEach((item, index) => {
      optionList.value.push({
        id: item.id,
        prefix: prefixOptions[index],
        name: item.name,
        is_correct: item.is_correct
      });
    });
  } else {
    let index = 0;
    while (index < 4) {
      optionList.value.push({
        id: 0,
        prefix: prefixOptions[index],
        name: '',
        is_correct: 0
      });
      index += 1;
    }
  }
});
</script>

<template>
  <NForm ref="formRef" label-placement="left" :label-width="20" size="small">
    <NFormItem v-for="(item, index) in optionList" :key="item.prefix">
      <NSpace item-style="display: flex;" align="center">
        <NText class="w-25px">{{ item.prefix }}、</NText>
        <EditorSimple v-model:value="item.name" :width="660" :height="200" @upload-success="handleUploadSuccess" />
        <NButton quaternary circle type="warning" @click="handleRemove(index)">
          <template #icon>
            <icon-ic-round-delete class="text-icon" />
          </template>
        </NButton>
      </NSpace>
    </NFormItem>
    <NFormItem>
      <NButton type="info" size="small" ghost @click="handleAdd">
        <template #icon>
          <icon-ic-round-plus class="text-icon" />
        </template>
        添加选项
      </NButton>
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
