<script setup lang="tsx">
import { computed, onMounted, ref, reactive, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { createExpert, getExpertConfig, updateExpert } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';
import UploadFiles, { type AttachmentType } from "@/components/common/upload/upload-files.vue";
import UploadFile from "@/components/common/upload/upload-file.vue";

/**
 * the type of operation
 *
 * - add: add user
 * - edit: edit user
 */
export type OperateType = 'add' | 'edit';

const { loading, startLoading, endLoading } = useLoading();

interface Props {
  operateType: OperateType;
  rowData?: Api.Expert.Expert;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: `创建专家`,
    edit: `编辑专家`
  };
  return titles[props.operateType];
});

const educationOptions = ref([]);
const servicesOptions = ref([]);
const fieldsOptions = ref([]);
const certsRef = ref();
const scenePhotosRef = ref();

type Model = Pick<Api.Expert.Expert,
  'user_id' | 'name' | 'gender' | 'phone' | 'photo' | 'education' | 'occupation' | 'industry' | 'work_year' | 'fields' |
  'services' | 'course_scopes' | 'typical_cases' | 'serve_customers' | 'teaching_styles' | 'remark' | 'major' | 'residence' | 'safety_work_experience'
> & {
  certs_add?: string[];
  certs_remove?: string[];
  scene_photos_add?: string[];
  scene_photos_remove?: string[];
  certs: AttachmentType[];
  scene_photos: AttachmentType[];
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    user_id: undefined,
    name: '',
    gender: 1,
    photo: '',
    phone: '',
    education: undefined,
    occupation: '',
    industry: '',
    residence: '',
    major: '',
    safety_work_experience: '',
    work_year: 10,
    fields: '',
    services: undefined,
    certs: [],
    scene_photos: [],
    course_scopes: '',
    typical_cases: '',
    serve_customers: '',
    teaching_styles: '',
    remark: ''
  };
}

const rules: Record<string, App.Global.FormRule> = {
  name: defaultRequiredRule,
  gender: defaultRequiredRule,
  phone: defaultRequiredRule,
  photo: defaultRequiredRule,
  education: defaultRequiredRule,
  occupation: defaultRequiredRule,
  industry: defaultRequiredRule,
  work_year: defaultRequiredRule,
  fields: defaultRequiredRule,
  services: defaultRequiredRule,
  certs: defaultRequiredRule,
  scene_photos: defaultRequiredRule,
  course_scopes: defaultRequiredRule,
  typical_cases: defaultRequiredRule,
  residence: defaultRequiredRule,
  major: defaultRequiredRule,
  safety_work_experience: defaultRequiredRule,
};

async function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, {
      user_id: props.rowData.user_id,
      name: props.rowData.name,
      gender: props.rowData.gender,
      photo: props.rowData.photo,
      phone: props.rowData.phone,
      residence: props.rowData.residence,
      major: props.rowData.major,
      safety_work_experience: props.rowData.safety_work_experience,
      education: props.rowData.education,
      occupation: props.rowData.occupation,
      industry: props.rowData.industry,
      work_year: props.rowData.work_year,
      fields: props.rowData.fields,
      services: props.rowData.services,
      certs: props.rowData.certs,
      scene_photos: props.rowData.scene_photos,
      course_scopes: props.rowData.course_scopes,
      typical_cases: props.rowData.typical_cases,
      serve_customers: props.rowData.serve_customers,
      teaching_styles: props.rowData.teaching_styles,
      remark: props.rowData.remark
    });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const certsAdd = certsRef.value.getAddFiles();
  const certsRemove = certsRef.value.getRemoveFiles();
  const scenePhotosAdd = scenePhotosRef.value.getAddFiles();
  const scenePhotosRemove = scenePhotosRef.value.getRemoveFiles();

  const post = { ...model, certs: undefined, scene_photos: undefined };

  if (!post.user_id) {
    post.user_id = 0;
  }

  if (certsAdd.length > 0) {
    post.certs_add = certsAdd;
  }

  if (certsRemove.length > 0) {
    post.certs_remove = certsRemove;
  }

  if (scenePhotosAdd.length > 0) {
    post.scene_photos_add = scenePhotosAdd;
  }

  if (scenePhotosRemove.length > 0) {
    post.scene_photos_remove = scenePhotosRemove;
  }

  startLoading();

  if (props.operateType === 'add') {
    const { error } = await createExpert(post);

    endLoading();

    if(error) {
      return;
    }
    window.$message?.success($t('common.addSuccess'));
  } else {
    const { error } = await updateExpert(props.rowData.id, post);

    endLoading();

    if(error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});

onMounted(() => {
  getExpertConfig().then(res => {
    res.data.educations.forEach(item => {
      educationOptions.value.push({
        label: item,
        value: item
      })
    });

    res.data.services.forEach(item => {
      servicesOptions.value.push({
        label: item,
        value: item
      })
    });

    res.data.fields.forEach(item => {
      fieldsOptions.value.push({
        label: item,
        value: item
      })
    });
  });
})
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="720">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="140">
        <NFormItem label="选择用户" path="user_id" class="w-260px">
          <SearchUser v-model:value="model.user_id" clearable />
        </NFormItem>
        <NFormItem label="姓名" path="name" class="w-260px">
          <NInput v-model:value="model.name" />
        </NFormItem>
        <NFormItem label="联系电话" path="phone" class="w-360px">
          <NInput v-model:value="model.phone" />
        </NFormItem>
        <NFormItem label="性别" path="gender">
          <NRadioGroup v-model:value="model.gender">
            <NRadio :value="1" label="男" />
            <NRadio :value="2" label="女" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="常住地" path="residence" class="w-260px">
          <NInput v-model:value="model.residence" />
        </NFormItem>
        <NFormItem label="最高学历" path="education" class="w-260px">
          <NSelect v-model:value="model.education" :options="educationOptions"></NSelect>
        </NFormItem>
        <NFormItem label="毕业所学专业" path="major" class="w-360px">
          <NInput v-model:value="model.major" />
        </NFormItem>
        <NFormItem label="职称/职务" path="occupation" class="w-360px">
          <NInput v-model:value="model.occupation" />
        </NFormItem>
        <NFormItem label="从事行业" path="industry" class="w-360px">
          <NInput v-model:value="model.industry" />
        </NFormItem>
        <NFormItem label="从事行业年限" path="work_year" class="w-260px">
          <NInputNumber v-model:value="model.work_year" :min="1" :max="70">
            <template #suffix>
              年
            </template>
          </NInputNumber>
        </NFormItem>
        <NFormItem label="擅长领域和行业" path="fields" class="w-560px">
          <NSelect v-model:value="model.fields" multiple :options="fieldsOptions"></NSelect>
        </NFormItem>
        <NFormItem label="安全服务方向" path="services" class="w-560px">
          <NSelect v-model:value="model.services" multiple :options="servicesOptions"></NSelect>
        </NFormItem>
        <NFormItem label="个人照片" path="photo" class="w-460px">
          <UploadFile
              v-model:value="model.photo"
              :preview="rowData?.photo_url"
              drag
              storage="pub"
              :file-type="['image']"
              tips="支持图片格式的文件"
          />
        </NFormItem>
        <NFormItem label="资格证书" path="certs" class="w-460px">
          <UploadFiles
            ref="certsRef"
            v-model:value="model.certs"
            drag
            :limit="9"
            :upload-config="{
              file_types: ['image', 'doc'],
              storage: 'pub',
              prefix: 0,
              max_size: 102400
            }"
            tips="支持PDF、DOC、PPT、PNG、JPG等格式"
          />
        </NFormItem>
        <NFormItem label="安全从业经历" path="safety_work_experience" class="w-560px">
          <NInput type="textarea" v-model:value="model.safety_work_experience" :rows="4" />
        </NFormItem>
        <NFormItem label="授课范围" path="course_scopes" class="w-560px">
          <NInput type="textarea" v-model:value="model.course_scopes" :rows="4" />
        </NFormItem>
        <NFormItem label="安全咨询与培训典型案例" path="typical_cases" class="w-560px">
          <NInput type="textarea" v-model:value="model.typical_cases" :rows="8" />
        </NFormItem>
        <NFormItem label="现场照片" path="certs" class="w-460px">
          <UploadFiles
            ref="scenePhotosRef"
            v-model:value="model.scene_photos"
            drag
            :limit="9"
            :upload-config="{
              file_types: ['image', 'doc'],
              storage: 'pub',
              prefix: 0,
              max_size: 102400
            }"
            tips="支持PDF、DOC、PPT、PNG、JPG等格式"
          />
        </NFormItem>
        <NFormItem label="服务客户" path="serve_customers" class="w-560px">
          <NInput type="textarea" v-model:value="model.serve_customers" :rows="6" />
        </NFormItem>
        <NFormItem label="讲师教学风格" path="teaching_styles" class="w-560px">
          <NInput type="textarea" v-model:value="model.teaching_styles" :rows="6" />
        </NFormItem>
        <NFormItem label="备注" path="remark" class="w-560px">
          <NInput type="textarea" v-model:value="model.remark" :rows="6" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">保存</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
