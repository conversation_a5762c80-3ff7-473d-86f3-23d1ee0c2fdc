<script setup lang="ts">
import {nextTick, onMounted, ref} from "vue";
import { getExpertConfig } from "@/service/api";
import { $t } from '@/locales';
import { expertStatusOptions, expertIsVisibleOptions } from '@/constants/business';

const genderOptions = [
  { label: '男', value: 1 },
  { label: '女', value: 2 }
];

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type SearchParams = CommonType.RecordNullable<Api.SystemManage.CommonSearchParams & {
  id: string;
  status: number;
  is_visible: number;
  search: string;
  fields: string;
  phone: string;
  user_id: string;
  name: string;
  gender: number;
  education: string;
  occupation: string;
  industry: string;
  residence: string;
  work_year: number;
  services: string;
  keyword: string;
}>;

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

const fieldsOptions = ref([]);
const educationOptions = ref([]);
const servicesOptions = ref([]);
const showAll = ref(false);

function reset() {
  emit('reset');
}
function search() {
  emit('search');
}

function toggleShowAll() {
  showAll.value = !showAll.value;
}

onMounted(() => {
  getExpertConfig().then(res => {
    res.data.fields.forEach(item => {
      fieldsOptions.value.push({
        label: item,
        value: item
      })
    });

    res.data.educations.forEach(item => {
      educationOptions.value.push({
        label: item,
        value: item
      })
    });

    res.data.services.forEach(item => {
      servicesOptions.value.push({
        label: item,
        value: item
      })
    });
  });
})
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left" label-width="auto">
      <NGrid responsive="screen" item-responsive cols="24">
        <!-- 第一行：基本信息 -->
        <NFormItemGi span="24 s:12 m:6" label="关键词" path="keyword" class="pr-5px">
          <NInput v-model:value="model.keyword" placeholder="输入关键词" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="用户" path="user_id" class="pr-5px">
          <SearchUser v-model:value="model.user_id" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="姓名" path="name" class="pr-5px">
          <NInput v-model:value="model.name" placeholder="输入姓名" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="联系电话" path="phone" class="pr-5px">
          <NInput v-model:value="model.phone" placeholder="输入联系电话" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="常住地" path="residence" class="pr-5px">
          <NInput v-model:value="model.residence" placeholder="输入常住地" clearable />
        </NFormItemGi>
        

        <!-- 其余行：用v-show控制显示 -->
        <template v-if="showAll">
          <!-- 第二行：专业信息 -->
          <NFormItemGi span="24 s:12 m:6" label="擅长领域" path="fields" class="pr-5px">
            <NSelect v-model:value="model.fields" :options="fieldsOptions" clearable />
          </NFormItemGi>
          <NFormItemGi span="24 s:12 m:6" label="服务方向" path="services" class="pr-5px">
            <NSelect v-model:value="model.services" :options="servicesOptions" clearable />
          </NFormItemGi>
          <NFormItemGi span="24 s:12 m:6" label="学历" path="education" class="pr-5px">
            <NSelect v-model:value="model.education" :options="educationOptions" clearable />
          </NFormItemGi>
          <NFormItemGi span="24 s:12 m:6" label="职务" path="occupation" class="pr-5px">
            <NInput v-model:value="model.occupation" placeholder="输入职务" clearable />
          </NFormItemGi>

          <!-- 第三行：工作信息 -->
          <NFormItemGi span="24 s:12 m:6" label="从事行业" path="industry" class="pr-5px">
            <NInput v-model:value="model.industry" placeholder="输入行业" clearable />
          </NFormItemGi>
          <NFormItemGi span="24 s:12 m:6" label="性别" path="gender" class="pr-5px">
            <NSelect v-model:value="model.gender" :options="genderOptions" clearable />
          </NFormItemGi>
          <NFormItemGi span="24 s:12 m:6" label="工作年限" path="work_year" class="pr-5px">
            <NInputNumber v-model:value="model.work_year" :min="1" :max="70" class="w-full">
              <template #suffix>
                年
              </template>
            </NInputNumber>
          </NFormItemGi>

          <!-- 第四行：状态和时间 -->
          <NFormItemGi span="24 s:12 m:6" label="审核状态" path="status" class="pr-5px">
            <NSelect v-model:value="model.status" :options="expertStatusOptions" clearable />
          </NFormItemGi>
          <NFormItemGi span="24 s:12 m:6" label="显隐状态" path="is_visible" class="pr-5px">
            <NSelect v-model:value="model.is_visible" :options="expertIsVisibleOptions" clearable />
          </NFormItemGi>
          <NFormItemGi span="24 s:12 m:6" label="创建时间" path="created_at" class="pr-5px">
            <DatetimeRange v-model:value="model.created_at" />
          </NFormItemGi>
        </template>

        <!-- 操作按钮行，始终显示 -->
        <NFormItemGi span="24 s:12 m:6" class="pt-5px">
          <NSpace class="w-full" justify="end">
            <NButton text @click="toggleShowAll" class="ml-2">
              <template #icon>
                <icon-ic-round-expand-more v-if="!showAll" />
                <icon-ic-round-expand-less v-else />
              </template>
              {{ showAll ? '收起' : '展开' }}
            </NButton>
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
