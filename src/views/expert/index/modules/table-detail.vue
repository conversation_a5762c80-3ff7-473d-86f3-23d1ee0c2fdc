<script setup lang="ts">
import { ref } from 'vue';
import { useLoading } from "~/packages/hooks";
import { resolveExpert } from '@/service/api';
import { getFileSize } from "@/utils/common";
import ImageSingle from "@/components/common/image/image-single.vue";
import DetailReject from './detail-reject.vue';

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const { loading, startLoading, endLoading } = useLoading();

const visible = ref<boolean>(false);
const detail = ref<Api.Expert.Expert>();
const rejectRef = ref();


function open(row: Api.Expert.Expert) {
  detail.value = row;
  visible.value = true;
}

function close() {
  visible.value = false;
}

function reload() {
  close();
  emit('reload');
}

async function handleReject() {
  rejectRef.value.open(detail.value);
}

async function handleResolve() {
  const { error } = await resolveEx<PERSON>(detail.value.id);

  if(error) {
    return;
  }

  window.$message?.success('操作成功');

  reload();
}

function toGender(gender: number) {
  let label = '未设置';
  let type = '';

  if (gender == 1) {
    label = '男';
    type = 'success';
  } else if (gender == 2) {
    label = '女';
    type = 'error';
  }

  return { type, label };
}

function toFilename(filename: string) {
  if (filename.length > 24) {
    return filename.slice(0, 10) + '****' + filename.slice(-10);
  }

  return filename;
}

defineExpose({
  open
})
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="640">
    <NDrawerContent title="专家详情" :native-scrollbar="false" closable>
      <n-space justify="center">
        <ImageSingle :src="detail.photo_url" :width="120" />
      </n-space>
      <n-space justify="center">
        <div class="font-800 font-size-20px">{{ detail.name }}</div>
      </n-space>

      <n-descriptions class="mt-6" label-placement="top" :column="2" size="large" :label-style="{ 'font-size': '20px', 'font-weight': 600 }">
        <n-descriptions-item label="ID">
          {{ detail.id }}
        </n-descriptions-item>
        <n-descriptions-item label="用户">
          {{ detail.user?.nickname ?? '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="性别">
          {{ toGender(detail.gender).label }}
        </n-descriptions-item>
        <n-descriptions-item label="联系电话" >
          {{ detail.phone }}
        </n-descriptions-item>
        <n-descriptions-item label="常住地" >
          {{ detail.residence }}
        </n-descriptions-item>
        <n-descriptions-item label="最高学历">
          {{ detail.education }}
        </n-descriptions-item>
        <n-descriptions-item label="毕业所学专业">
          {{ detail.major }}
        </n-descriptions-item>
        <n-descriptions-item label="职称/职务">
          {{ detail.occupation }}
        </n-descriptions-item>
        <n-descriptions-item label="从事行业">
          {{ detail.industry }}
        </n-descriptions-item>
        <n-descriptions-item label="从事行业年限">
          {{ detail.work_year }}年
        </n-descriptions-item>
        <n-descriptions-item label="擅长领域和行业" :span="2">
          {{ detail.fields.join('、') }}
        </n-descriptions-item>
        <n-descriptions-item label="安全服务方向" :span="2">
          {{ detail.services.join('、') }}
        </n-descriptions-item>
        <n-descriptions-item label="资格证书" :span="2">
          <NSpace vertical :size="0">
            <div v-for="(item, index) in detail.certs" :key="index">
              <NSpace>
                <NText>{{ toFilename(item.filename) }}</NText>
                <NText>{{ getFileSize(item.filesize) }}</NText>
                <NButton text tag="a" target="_blank" type="primary" :href="item.path_src">查看</NButton>
              </NSpace>
            </div>
          </NSpace>
        </n-descriptions-item>
        <n-descriptions-item label="安全从业经历" :span="2">
          {{ detail.safety_work_experience }}
        </n-descriptions-item>
        <n-descriptions-item label="授课范围" :span="2">
          {{ detail.course_scopes }}
        </n-descriptions-item>
        <n-descriptions-item label="安全咨询与培训典型案例" :span="2">
          {{ detail.typical_cases }}
        </n-descriptions-item>
        <n-descriptions-item label="现场照片" :span="2">
          <NSpace vertical :size="0">
            <div v-for="(item, index) in detail.scene_photos" :key="index">
              <NSpace>
                <NText>{{ toFilename(item.filename) }}</NText>
                <NText>{{ getFileSize(item.filesize) }}</NText>
                <NButton text tag="a" target="_blank" type="primary" :href="item.path_src">查看</NButton>
              </NSpace>
            </div>
          </NSpace>
        </n-descriptions-item>
        <n-descriptions-item label="服务客户" :span="2">
          {{ detail.serve_customers ? detail.serve_customers : '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="讲师教学风格" :span="2">
          {{ detail.teaching_styles ? detail.teaching_styles : '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="备注" :span="2">
          {{ detail.remark ? detail.remark : '-' }}
        </n-descriptions-item>
        <n-descriptions-item v-if="detail.status == 2" label="拒绝原因" :span="2">
          <n-text type="error">{{ detail.reason }}</n-text>
        </n-descriptions-item>
      </n-descriptions>

      <template #footer>
        <NSpace :size="16">
          <NButton @click="close">取消</NButton>
          <NButton v-if="!detail.status" type="error" :disabled="loading" @click="handleReject">拒绝</NButton>
          <NButton v-if="!detail.status" type="success" :disabled="loading" @click="handleResolve">通过</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>

  <DetailReject ref="rejectRef" @success="reload" />
</template>

<style scoped>
</style>
