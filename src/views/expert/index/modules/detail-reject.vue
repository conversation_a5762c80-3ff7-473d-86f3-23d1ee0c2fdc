<script setup lang="tsx">
import { ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { rejectExpert } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = {
  reason: string;
};

interface Emits {
  (e: 'success'): void;
}

const emit = defineEmits<Emits>();

const visible = ref<boolean>(false);
const detail = ref<Api.Expert.Expert>();


const model = ref<Model>({
  reason: ''
});

const rules: Record<string, App.Global.FormRule> = {
  reason: defaultRequiredRule,
};

function open(row: Api.Expert.Expert) {
  detail.value = row;
  visible.value = true;
}

function close() {
  visible.value = false;
}

async function handleReject() {
  await validate();

  startLoading();

  const { error } = await rejectExpert(detail.value.id, model.value);

  endLoading();

  if(error) {
    return;
  }

  window.$message?.success('操作成功');

  close();
  emit('success');
}

defineExpose({
  open
})
</script>

<template>
  <n-modal v-model:show="visible">
    <n-card
      style="width: 600px"
      title="审核未通过"
      :bordered="false"
    >
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem label="原因" path="reason" class="w-420px">
          <NInput v-model:value="model.reason" placeholder="请输入未通过原因" />
        </NFormItem>

      </NForm>
      <template #footer>
        <NSpace :size="16" justify="end">
          <NButton @click="close">取消</NButton>
          <NButton type="primary" @click="handleReject" :disabled="loading">确认</NButton>
        </NSpace>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped></style>
