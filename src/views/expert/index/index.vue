<script setup lang="tsx">
import { ref } from 'vue';
import { NButton, NTag, useDialog, NInputNumber } from 'naive-ui';
import { fetchExpertList, updateExpert } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useBoolean } from '@sa/hooks';
import { expertStatusLabel, expertIsVisibleLabel } from '@/constants/business';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import OperateDrawer, { type OperateType } from './modules/operate-drawer.vue';
import ImageSingle from '@/components/common/image/image-single.vue';
import TableDetail from './modules/table-detail.vue';

const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();
const dialog = useDialog();
const detailRef = ref();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Expert.Expert, typeof fetchExpertList, 'index' | 'operate'>({
  apiFn: fetchExpertList,
  apiParams: {
    current: 1,
    size: 20,
    user_id: null,
    name: null,
    phone: null,
    status: null,
    is_visible: null,
    fields: null,
    created_at: [],
    gender: null,
    education: null,
    occupation: null,
    industry: null,
    work_year: null,
    services: null,
    keyword: null,
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'user',
      title: '用户',
      align: 'center',
      minWidth: 100,
      render: row => {
        return <ColumnUser user_id={row.user_id} user={row.user} />
      }
    },
    {
      key: 'name',
      title: '姓名',
      align: 'center',
      minWidth: 80
    },
    {
      key: 'gender',
      title: '性别',
      align: 'center',
      minWidth: 80,
      render: row => {
        let label = '未设置';
        let tagType = '';

        if (row.gender == 1) {
          label = '男';
          tagType = 'success';
        } else if (row.gender == 2) {
          label = '女';
          tagType = 'error';
        }

        return <NTag type={tagType} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'photo',
      title: '个人照片',
      align: 'center',
      width: 80,
      render: row => {
        return <ImageSingle src={row.photo_url} width={48} />
      }
    },
    {
      key: 'phone',
      title: '联系方式',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'education',
      title: '学历',
      align: 'center',
      minWidth: 80
    },
    {
      key: 'occupation',
      title: '职务',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'industry',
      title: '从事行业',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'work_year',
      title: '工作年限',
      align: 'center',
      minWidth: 80
    },
    {
      key: 'status',
      title: '审核状态',
      align: 'center',
      minWidth: 80,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'primary',
          1: 'success',
          2: 'error',
        };

        const label = expertStatusLabel[row.status];

        return <NTag type={tagMap[row.status]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'is_visible',
      title: '显隐状态',
      align: 'center',
      minWidth: 80,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'warning',
          1: 'success',
        };

        const label = expertIsVisibleLabel[row.is_visible];

        return <NTag type={tagMap[row.is_visible]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'sort',
      title: '排序',
      align: 'center',
      minWidth: 100,
      render: row => {
        return (
          <NInputNumber
            value={row.sort}
            min={0}
            step={1}
            onUpdateValue={(value: number) => handleSortChange(row, value)}
          />
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      minWidth: 80,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleShow(row)}>
            { row.status ? '查看' : '审核' }
          </NButton>
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row)}>
            编辑
          </NButton>
          <NButton type="primary" v-show={row.is_visible == 0} ghost size="small" onClick={() => handleIsVisible(row, 1)}>
            显示
          </NButton>
          <NButton type="primary" v-show={row.is_visible == 1} ghost size="small" onClick={() => handleIsVisible(row, 0)}>
            隐藏
          </NButton>
        </div>
      )
    }
  ]
});

const operateType = ref<OperateType>('add');

/** the editing row data */
const editingData = ref<Api.Expert.Expert | null>(null);

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;
  openDrawer();
}

const checkedRowKeys = ref<string[]>([]);

async function handleEdit(row: Api.Expert.Expert) {
  operateType.value = 'edit';
  editingData.value = row;
  openDrawer();
}

function handleShow(row: Api.Expert.Expert) {
  detailRef.value.open(row);
}

async function handleSortChange(row: Api.Expert.Expert, sort: number) {
  try {
    const post = { ...row };
    post.sort = sort;

    const { error } = await updateExpert(row.id, post);

    if (error) {
      window.$message?.error('更新排序失败');
      return;
    }

    row.sort = sort;
    window.$message?.success('更新排序成功');
  } catch (error) {
    window.$message?.error('更新排序失败');
  }
}

async function handleIsVisible(row: Api.Expert.Expert, is_visible: number) {
  if (is_visible == 1 && (row.status === 0 || row.status === 2)) {
    window.$message?.error('未通过审核的专家不能显示');
    return;
  }

  dialog.warning({
    title: '确认操作',
    content: `确定要${is_visible ? '显示' : '隐藏'}该专家吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const post = { ...row };
        post.is_visible = is_visible;

        console.log(post);
        const { error } = await updateExpert(row.id, post);

        if (error) {
          window.$message?.error(`${is_visible ? '显示' : '隐藏'}失败`);
          return;
        }

        row.is_visible = is_visible;
        getData();
        window.$message?.success(`${is_visible ? '显示' : '隐藏'}成功`);
      } catch (error) {
        window.$message?.error(`${is_visible ? '显示' : '隐藏'}失败`);
      }
    }
  });
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="专家列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />

      <TableDetail ref="detailRef" @reload="getData" />
    </NCard>
  </div>
</template>

<style scoped></style>
