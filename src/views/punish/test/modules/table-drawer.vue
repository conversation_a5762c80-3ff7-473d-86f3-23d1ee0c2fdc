<script setup lang="tsx">
import { ref } from 'vue';
import { useTable } from '@/hooks/common/table';
import { fetchPunishTestSubjectList } from '@/service/api';
import DateFormat from '@/components/common/date/date-format.vue';

const visible = ref<boolean>(false);

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Punish.TestSubject, typeof fetchPunishTestSubjectList, 'index' | 'operate'>({
  apiFn: fetchPunishTestSubjectList,
  apiParams: {
    current: 1,
    size: 20,
    test_id: 0
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  immediate: false,
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'subject_id',
      title: '违法行为',
      align: 'center',
      minWidth: 200,
      render: (row) => {
        return <ColumnEllipsis text={row.subject?.intro} line={2} />;
      },
    },
    {
      key: 'option_id',
      title: '适用条件',
      align: 'center',
      minWidth: 200,
      render: (row) => {
        if (row.option_id > 0) {
          return <ColumnEllipsis text={row.option?.name} line={2} />;
        } else {
          return '-';
        }
      },
    },
    {
      key: 'option.level',
      title: '载量阶次',
      align: 'center',
      width: 80,
      render: (row) => {
        if (row.option_id > 0) {
          return row.option?.level;
        } else {
          return '-';
        }
      },
    },
    {
      key: 'option.standard',
      title: '具体标准',
      align: 'center',
      minWidth: 200,
      render: (row) => {
        if (row.option_id > 0) {
          return <ColumnEllipsis text={row.option?.standard} line={2} />;
        } else {
          return '-';
        }
      },
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 170,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    }
  ]
});

function getIndex(index: number) {
  return String(index);
}

function open(testId: number) {
  resetSearchParams();
  updateSearchParams({
    test_id: testId
  });

  getData();

  visible.value = true;
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="1024">
    <NDrawerContent title="选项列表" :native-scrollbar="false" closable>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
