<script setup lang="tsx">
import { $t } from '@/locales';
import DatetimeRange from '@/components/common/date/datetime-range.vue';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

type SearchParams = CommonType.RecordNullable<Api.SystemManage.CommonSearchParams & {
  user_id: string;
  topic_id: string;
}>;

const model = defineModel<SearchParams>('model', { required: true });

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:3" label="用户" path="user_id" class="pr-24px">
          <SearchUser v-model:value="model.user_id" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:3" label="题库ID" path="topic_id" class="pr-24px">
          <NInput v-model:value="model.topic_id" placeholder="输入题库ID" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:7" label="创建时间" path="created_at" class="pr-24px">
          <DatetimeRange v-model:value="model.created_at"  />
        </NFormItemGi>
        <NFormItemGi span="24 s:3">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
