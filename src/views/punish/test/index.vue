<script setup lang="tsx">
import { ref } from 'vue';
import { NButton } from 'naive-ui';
import { fetchPunishTestList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnUser from '@/components/common/table/column-user.vue';
import TableSearch from './modules/table-search.vue';
import TableDrawer from './modules/table-drawer.vue';

const drawerRef = ref();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Punish.Test, typeof fetchPunishTestList, 'index' | 'operate'>({
  apiFn: fetchPunishTestList,
  apiParams: {
    current: 1,
    size: 20,
    user_id: null,
    topic_id: null,
    created_at: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      minWidth: 80
    },
    {
      key: 'user_id',
      title: '用户ID',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'topic_id',
      title: '题库ID',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'user',
      title: '用户',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <ColumnUser user_id={row.user_id} user={row.user} />;
      }
    },
    {
      key: 'topic',
      title: '题库',
      align: 'center',
      minWidth: 160,
      render: row => {
        return row.topic?.name ?? '-';
      }
    },
    {
      key: 'amount_tol',
      title: '处罚金额（元）',
      align: 'center',
      minWidth: 120,
    },
    {
      key: 'created_at',
      title: '开始时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '结束时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      minWidth: 100,
      render: row => (
        <NButton type="primary" ghost size="small" onClick={() => handleShow(row)}>
          查看选项
        </NButton>
      )
    }
  ]
});

const checkedRowKeys = ref<number[]>([]);

function handleShow(row: Api.Punish.Test) {
  drawerRef.value.open(row.id);
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="自测列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :loading="loading"
          :show-add="false"
          :show-batch-delete="false"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
      <TableDrawer ref="drawerRef" />
    </NCard>
  </div>
</template>

<style scoped></style>
