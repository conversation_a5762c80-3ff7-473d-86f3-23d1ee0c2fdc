<script setup lang="tsx">
import { ref } from 'vue';
import { N<PERSON>utton, NPopconfirm, NTag } from 'naive-ui';
import { fetchAnswerList, deleteAnswer, batchDeleteAnswer } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { questionStatusLabel } from '@/constants/business';
import { useRouterPush } from '@/hooks/common/router';
import TableSearch from './modules/table-search.vue';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnEllipsis from '@/components/common/table/column-ellipsis.vue';
import ColumnUser from '@/components/common/table/column-user.vue';

const { route } = useRouterPush();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Qa.Answer, typeof fetchAnswerList, 'index' | 'operate'>({
  apiFn: fetchAnswerList,
  apiParams: {
    current: 1,
    size: 20,
    id: null,
    status: null,
    question_id: route.value.query.question_id ? route.value.query.question_id : null,
    user_id: null,
    created_at: [],
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'question_id',
      title: '问题ID',
      align: 'center',
      width: 100
    },
    {
      key: 'user_id',
      title: '用户',
      align: 'left',
      width: 160,
      render: row => {
        return <ColumnUser user_id={row.user_id} user={row.user} />
      }
    },
    {
      key: 'question',
      title: '问题',
      align: 'left',
      width: 200,
      render: row => {
        return <ColumnEllipsis text={row.question?.title} line={2} />;
      }
    },
    {
      key: 'content',
      title: '回答',
      align: 'left',
      width: 200,
      render: row => {
        return <ColumnEllipsis text={row.content} line={2} />;
      }
    },
    {
      key: 'like_count',
      title: '赞数量',
      align: 'center',
      width: 80
    },
    {
      key: 'dislike_count',
      title: '踩数量',
      align: 'center',
      width: 80
    },
    {
      key: 'anonymous',
      title: '匿名',
      align: 'center',
      width: 80,
      render: row => {
        return row.anonymous ? '是' : '否';
      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 80,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'info',
          1: 'success',
          2: 'error'
        };

        const label = questionStatusLabel[row.status];

        return <NTag type={tagMap[row.status]} bordered={false}>{label}</NTag>;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 100,
      render: row => (
        <div class="flex-center gap-8px">
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const checkedRowKeys = ref<number[]>([]);

async function handleBatchDelete() {
  const { error } = await batchDeleteAnswer(checkedRowKeys.value)
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  checkedRowKeys.value = [];

  await getData();
}

async function handleDelete(id: number) {
  const { error } = await deleteAnswer(id)
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    question_id: null
  });
}


function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="回答列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="false"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
