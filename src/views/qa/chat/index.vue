<script setup lang="tsx">
import { ref } from 'vue';
import { NButton } from 'naive-ui';
import { fetchChatSessionList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useBoolean } from '~/packages/hooks/src';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnEllipsis from '@/components/common/table/column-ellipsis.vue';
import ColumnUser from '@/components/common/table/column-user.vue';
import TableSearch from './modules/table-search.vue';
import MessageDrawer from './modules/message-drawer.vue';
import {$t} from "@/locales";

const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Chat.Session, typeof fetchChatSessionList, 'index' | 'operate'>({
  apiFn: fetchChatSessionList,
  apiParams: {
    current: 1,
    size: 20,
    title: null,
    user_id: null,
    created_at: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'user',
      title: '用户',
      align: 'center',
      minWidth: 120,
      render: row => {
        return <ColumnUser user_id={row.user_id} user={row.user}/>;
      }
    },
    {
      key: 'title',
      title: '问题',
      align: 'center',
      minWidth: 240,
      render: row => {
        return <ColumnEllipsis text={row.title} line={2}/>;
      }
    },
    {
      key: 'messages_count',
      title: '回答数',
      align: 'center',
      minWidth: 100,
      render: row => {
        return (
          <NButton text type="primary" onClick={() => handleShow(row)}>
            {row.messages_count}
          </NButton>
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.created_at}/>;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      minWidth: 160,
      render: row => {
        return <DateFormat date={row.updated_at}/>;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 150,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleShow(row)}>
            查看
          </NButton>
        </div>
      )
    }
  ]
});

const checkedRowKeys = ref<number[]>([]);
const editingData = ref<Api.Chat.Session | null>(null);

function handleShow(row: Api.Chat.Session) {
  editingData.value = row;
  openDrawer();
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData"/>
    <NCard title="会话列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="false"
          :show-batch-delete="false"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
      <MessageDrawer v-if="drawerVisible" v-model:visible="drawerVisible" :row-data="editingData"/>
    </NCard>
  </div>
</template>

<style scoped></style>
