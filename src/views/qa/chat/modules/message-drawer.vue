<script setup lang="tsx">
import { useTable } from '@/hooks/common/table';
import { fetchChatMessageList } from '@/service/api';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnEllipsis from '@/components/common/table/column-ellipsis.vue';

interface Props {
  rowData: Api.Chat.Session;
}

const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const {
  columns,
  data,
  loading,
  pagination,
  getData,
  updateSearchParams
} = useTable<Api.Chat.Message, typeof fetchChatMessageList, 'index' | 'operate'>({
  apiFn: fetchChatMessageList,
  apiParams: {
    current: 1,
    size: 20,
    session_id: props.rowData.id
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 100
    },
    {
      key: 'prompt',
      title: '问题',
      align: 'center',
      width: 180,
      render: row => {
        return <ColumnEllipsis text={row.prompt} line={2}/>;
      }
    },
    {
      key: 'completion',
      title: '回答',
      align: 'center',
      minWidth: 240,
      render: row => {
        return <ColumnEllipsis text={row.completion} line={2}/>;
      }
    },
    {
      key: 'like',
      title: '赞',
      align: 'center',
      width: 100,
      render: row => {
        const labels = ['-', '👍', '👎'];

        return labels[row.like];
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at}/>;
      }
    }
  ]
});


function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="1024">
    <NDrawerContent title="会话详情" :native-scrollbar="false" closable>
      <NThing>
        <template #avatar>
          <n-avatar :src="rowData.user?.avatar" />
        </template>
        <template #header>
          {{ rowData.user?.nickname }}
        </template>
        <template #description>
          {{ rowData.title }}
        </template>
        <NDataTable
          :columns="columns"
          :data="data"
          size="small"
          :scroll-x="702"
          :loading="loading"
          remote
          :pagination="pagination"
          :row-key="item => item.id"
          class="sm:h-full"
        />
      </NThing>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
