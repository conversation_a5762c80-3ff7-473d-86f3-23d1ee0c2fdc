<script setup lang="ts">
import { h } from 'vue';
import { N<PERSON>utton, NPopconfirm, NTag } from 'naive-ui';
import { $t } from '@/locales';
import { useTable } from '@/hooks/common/table';
import { deleteAnswer, fetchAnswerList } from '@/service/api';
import { questionStatusLabel } from '@/constants/business';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnEllipsis from '@/components/common/table/column-ellipsis.vue';
import ColumnUser from '@/components/common/table/column-user.vue';

defineOptions({
  name: 'AnswerDrawer'
});

interface Props {
  rowData: Api.Qa.Question;
}

const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { columns, data, loading, pagination, getData, updateSearchParams } = useTable<
  Api.Qa.Answer,
  typeof fetchAnswerList,
  'index' | 'operate'
>({
  apiFn: fetchAnswerList,
  apiParams: {
    current: 1,
    size: 20,
    question_id: props.rowData.id
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'user_id',
      title: '用户',
      align: 'left',
      width: 140,
      render: row => {
        return h(ColumnUser, { user_id: row.user_id, user: row.user });
      }
    },
    {
      key: 'content',
      title: '回答',
      align: 'left',
      width: 180,
      render: row => {
        return h(ColumnEllipsis, { text: row.content, line: 2 });
      }
    },
    {
      key: 'like_count',
      title: '赞数量',
      align: 'center',
      width: 80
    },
    {
      key: 'dislike_count',
      title: '踩数量',
      align: 'center',
      width: 80
    },
    {
      key: 'anonymous',
      title: '匿名',
      align: 'center',
      width: 80,
      render: row => {
        return row.anonymous ? '是' : '否';
      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 80,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'info',
          1: 'success',
          2: 'error'
        };

        const label = questionStatusLabel[row.status];

        return h(
          NTag,
          {
            type: tagMap[row.status],
            bordered: false
          },
          {
            default: () => label
          }
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return h(DateFormat, { date: row.created_at });
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 100,
      render: row => {
        return h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete(row.id)
          },
          {
            default: () => '确认删除',
            trigger: () => {
              return h(
                NButton,
                {
                  type: 'error',
                  size: 'small',
                  ghost: true
                },
                { default: () => '删除' }
              );
            }
          }
        );
      }
    }
  ]
});

async function handleDelete(id: number) {
  const { error } = await deleteAnswer(id);
  if (error) {
    return;
  }

  await getData();
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="1024">
    <NDrawerContent title="问题详情" :native-scrollbar="false" closable>
      <NThing>
        <template #avatar>
          <n-avatar :src="rowData.user?.avatar" />
        </template>
        <template #header>
          {{ rowData.title }}
        </template>
        <template #description>
          {{ rowData.content }}
        </template>
        <NDataTable
          :columns="columns"
          :data="data"
          size="small"
          :scroll-x="702"
          :loading="loading"
          remote
          :pagination="pagination"
          :row-key="item => item.id"
          class="sm:h-full"
        />
      </NThing>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
