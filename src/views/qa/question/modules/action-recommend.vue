<script setup lang="ts">
import { ref } from 'vue';
import { recommendQuestion } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

interface Props {
  rowData: Api.Qa.Question;
}

const props = defineProps<Props>();

const { loading, startLoading, endLoading } = useLoading();

const recommendCheck = ref<boolean>(<PERSON><PERSON><PERSON>(props.rowData.recommend_at));

async function handleChange(value: boolean) {
  recommendCheck.value = value;

  startLoading();

  await recommendQuestion(props.rowData.id, value ? 1 : 0);

  endLoading();
}

</script>

<template>
  <NSwitch :value="recommendCheck" :disabled="loading" @update:value="handleChange">
    <template #checked>
      开
    </template>
    <template #unchecked>
      关
    </template>
  </NSwitch>
</template>

<style scoped></style>
