<script setup lang="tsx">
import { ref } from 'vue';
import { N<PERSON>utton, NPopconfirm, NTag } from 'naive-ui';
import { batchDeleteQuestion, deleteQuestion, fetchQuestionList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { $t } from '@/locales';
import { useBoolean } from '~/packages/hooks/src';
import { questionStatusLabel } from '@/constants/business';
import DateFormat from '@/components/common/date/date-format.vue';
import ColumnEllipsis from '@/components/common/table/column-ellipsis.vue';
import ColumnUser from '@/components/common/table/column-user.vue';
import TableSearch from './modules/table-search.vue';
import ActionRecommend from './modules/action-recommend.vue';
import AnswerDrawer from './modules/answer-drawer.vue';

const { routerPushByKey } = useRouterPush();

const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Qa.Question, typeof fetchQuestionList, 'index' | 'operate'>({
  apiFn: fetchQuestionList,
  apiParams: {
    current: 1,
    size: 20,
    id: null,
    status: null,
    title: null,
    user_id: null,
    created_at: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'user_id',
      title: '用户',
      align: 'left',
      width: 160,
      render: row => {
        return <ColumnUser user_id={row.user_id} user={row.user} />;
      }
    },
    {
      key: 'content',
      title: '问题',
      align: 'left',
      minWidth: 240,
      render: row => {
        return <ColumnEllipsis text={row.content} line={2} />;
      }
    },
    {
      key: 'answer_count',
      title: '回答数',
      align: 'center',
      width: 80,
      render: row => {
        return (
          <NButton text type="primary" onClick={() => goAnswer(row.id)}>
            {row.answer_count}
          </NButton>
        );
      }
    },
    {
      key: 'anonymous',
      title: '匿名',
      align: 'center',
      width: 80,
      render: row => {
        return row.anonymous ? '是' : '否';
      }
    },
    {
      key: 'recommend_at',
      title: '推荐',
      align: 'center',
      width: 80,
      render: row => {
        return <ActionRecommend rowData={row} />;
      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 80,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'info',
          1: 'success',
          2: 'error'
        };

        const label = questionStatusLabel[row.status];

        return (
          <NTag type={tagMap[row.status]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 150,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleShow(row)}>
            查看
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const checkedRowKeys = ref<number[]>([]);
const editingData = ref<Api.Qa.Question | null>(null);

function handleShow(row: Api.Qa.Question) {
  editingData.value = row;
  openDrawer();
}

async function handleBatchDelete() {
  const { error } = await batchDeleteQuestion(checkedRowKeys.value);
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  // eslint-disable-next-line require-atomic-updates
  checkedRowKeys.value = [];

  await getData();
}

async function handleDelete(id: number) {
  const { error } = await deleteQuestion(id);
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}

function goAnswer(id: number) {
  routerPushByKey('qa_answer', { query: { question_id: id.toString() } });
}

function getIndex(index: number) {
  return String(index);
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="问题列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="false"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
      <AnswerDrawer v-if="drawerVisible && editingData" v-model:visible="drawerVisible" :row-data="editingData" />
    </NCard>
  </div>
</template>

<style scoped></style>
