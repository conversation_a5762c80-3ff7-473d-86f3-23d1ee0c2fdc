<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getStatisticPermission } from "@/service/api";
import AdminStats from './admin/stats.vue';
import GuestStatus from './guest/stats.vue';

const permission = ref<Api.Stat.Permission | null>(null);

onMounted(async () => {
  const { data, error } = await getStatisticPermission();

  if (error) {
    return;
  }

  permission.value = data;
});
</script>

<template>
  <NSpace v-if="permission" vertical :size="16">
    <AdminStats v-if="permission.permission === 'root'" />
    <GuestStatus v-else />
  </NSpace>
</template>

<style scoped></style>
