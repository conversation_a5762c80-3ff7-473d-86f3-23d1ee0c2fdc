<script setup lang="ts">
import { useEcharts } from '@/hooks/chart/use-echarts';
import { PaymentType } from './tab-chart.vue';

defineOptions({
  name: '<PERSON><PERSON><PERSON>'
});

interface Props {
  payment: PaymentType;
}

const props = defineProps<Props>();

const { domRef, updateOptions } = useEcharts(() => ({
  title: {
    text: '付费占比',
    left: '1%'
  },
  tooltip: {
    trigger: 'item'
  },
  legend: {
    bottom: '1%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    }
  },
  series: [
    {
      color: ['#5da8ff', '#8e9dff', '#fedc69', '#26deca'],
      name: '付费占比',
      type: 'pie',
      radius: ['35%', '80%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 1
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '12'
        }
      },
      labelLine: {
        show: false
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

async function init() {
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });

  updateOptions((opts, factory) => {
    const originOpts = factory();

    opts.series[0].name = originOpts.series[0].name;

    opts.series[0].data = [
      { name: '积分', value: props.payment.paymentCreditAmount },
      { name: '题库', value: props.payment.paymentTopicAmount },
      { name: '课程', value: props.payment.paymentCourseAmount },
      { name: '资料', value: props.payment.platformMaterialAmount },
    ];

    return opts;
  });
}


init();
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <div ref="domRef" class="h-360px overflow-hidden"></div>
  </NCard>
</template>

<style scoped></style>
