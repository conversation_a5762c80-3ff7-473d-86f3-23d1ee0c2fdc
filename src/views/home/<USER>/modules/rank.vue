<script setup lang="ts">
import {ref} from "vue";
import {useTable} from "@/hooks/common/table";
import {adminContentRank} from "@/service/api";

defineOptions({
  name: 'Rank'
});

interface Props {
  adminId?: number;
}
const props = defineProps<Props>();

const checkedRowKeys = ref<string[]>([]);

const {
  columns,
  data,
  loading,
} = useTable<Api.Cms.ContentDoc, typeof adminContentRank, 'index' | 'operate'>({
  apiFn: adminContentRank,
  apiParams: {
    admin_id: props.adminId
  },
  transformer: res => {
    res.data.forEach((item, index) => {
      item.rank = index + 1
    });

    return {
      data: res.data
    };
  },
  columns: () => [
    {
      key: 'rank',
      title: '排名',
      align: 'center',
      width: 100
    },
    {
      key: 'content.title' as any,
      title: '资料名称',
      align: 'center',
      width: 100
    },
    {
      key: 'download_count' as any,
      title: '下载数量',
      align: 'center',
      width: 100
    }
  ]
});

</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <NCard title="资料下载排行榜" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped>

</style>
