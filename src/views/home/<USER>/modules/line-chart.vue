<script setup lang="ts">
import { useEcharts } from '@/hooks/chart/use-echarts';
import { dateFormat } from '@/utils/date';
import { useAppStore } from '@/store/modules/app';
import { round } from 'lodash-es';

export interface ChartSeries {
  name: string;
  data: number[];
}

const appStore = useAppStore();

interface Props {
  xAxis: string[];
  series: ChartSeries[];
  unit?: string;
  colors?: string[];
  totalIndex?: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  unit: '个',
  totalIndex: null,
  colors: () => ['#26deca', '#8e9dff', '#CC66FF', '#FFCC66', '#CCFF66', '#CCCC66']
});

const { domRef, updateOptions } = useEcharts(() => ({
  title: {
    text: '',
    left: '1%'
  },
  tooltip: {
    trigger: 'axis',
    formatter: '',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    }
  },
  legend: {
    data: ['加载中'],
    top: '1%',
    right: '1%'
  },
  grid: {
    left: '1%',
    right: '1%',
    bottom: '1%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      data: ['06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00', '24:00']
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      color: '#26deca',
      name: '加载中',
      type: 'line',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0.25,
              color: '#26deca'
            },
            {
              offset: 1,
              color: '#fff'
            }
          ]
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: [12, 50, 69, 45, 30, 58, 89, 69, 78, 100]
    }
  ]
}));


async function init() {
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });

  updateOptions(chartOptions => {
    chartOptions.title.text = '';
    chartOptions.xAxis[0].data = [];
    chartOptions.legend.data = [];
    chartOptions.series = [];
    const days: number = props.xAxis.length;
    let total: number = 0;
    let avg: number = 0;

    if (props.xAxis.length > 0) {
      chartOptions.title.text = `${props.xAxis[0]}~${props.xAxis[props.xAxis.length - 1]}`;
      props.xAxis.forEach(item => {
        chartOptions.xAxis[0].data.push(dateFormat(item, 'MM-DD'));
      });
    }

    props.series.forEach((item, index) => {
      if (props.totalIndex === null) {
        total += item.data.reduce((pre, curr) => pre + curr);
      } else if (props.totalIndex === index) {
        total += item.data.reduce((pre, curr) => pre + curr);
      }
      if (props.unit === '%') {
        chartOptions.tooltip.formatter = '{c}%';
      }
      chartOptions.legend.data.push(item.name);
      chartOptions.series.push({
        color: props.colors[index],
        name: item.name,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0.25,
                color: props.colors[index]
              },
              {
                offset: 1,
                color: '#fff'
              }
            ]
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: item.data
      });
    });

    if (days > 0) {
      avg = round(total / days, 2);
    }

    total = round(total, 2);

    if (props.unit === '%') {
      chartOptions.title.text = `${chartOptions.title.text} 均${avg}${props.unit}`;
    } else {
      chartOptions.title.text = `${chartOptions.title.text} 共${total}${props.unit}/${days}天，均${avg}${props.unit}/天`;
    }

    if (appStore.isMobile) {
      chartOptions.title.text = '';
    }

    return chartOptions;
  });
}

init();
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <div ref="domRef" class="h-360px overflow-hidden"></div>
  </NCard>
</template>

<style scoped></style>
