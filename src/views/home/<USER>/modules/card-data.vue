<script setup lang="ts">
import { ref } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { getPromoterStatisticSubtotalData } from '@/service/api';
import { getFileSize } from '@/utils/common';

defineOptions({
  name: 'CardData'
});

interface Props {
  adminId?: number;
}

const props = defineProps<Props>();

interface CardData {
  key: string;
  title: string;
  decimals?: number;
  value: number;
  today: number;
  yesterday: number;
  unit: string;
  color: {
    start: string;
    end: string;
  };
  icon: string;
}

const cardData = ref<CardData[]>([
  {
    key: 'content_material',
    title: '上传资料总量',
    value: 0,
    today: 0,
    yesterday: 0,
    unit: '',
    color: {
      start: '#00CC99',
      end: '#00CCFF'
    },
    icon: 'iconoir:google-docs'
  },
  {
    key: 'content_course',
    title: '上传课程总量',
    value: 0,
    today: 0,
    yesterday: 0,
    unit: '',
    color: {
      start: '#336699',
      end: '#3366FF'
    },
    icon: 'tdesign:course'
  },
  {
    key: 'content_special',
    title: '专题总量',
    value: 0,
    today: 0,
    yesterday: 0,
    unit: '',
    color: {
      start: '#ddbe56',
      end: '#efbb07'
    },
    icon: 'material-symbols:folder-special-outline'
  },
  // {
  //   key: 'content_download',
  //   title: '资料下载总量',
  //   value: 0,
  //   today: 0,
  //   yesterday: 0,
  //   unit: '',
  //   color: {
  //     start: '#56cdf3',
  //     end: '#719de3'
  //   },
  //   icon: 'carbon:document-download'
  // },
  {
    key: 'consume_credit',
    title: '消耗积分总量',
    value: 0,
    today: 0,
    yesterday: 0,
    unit: '',
    color: {
      start: '#990099',
      end: '#9900FF'
    },
    icon: 'heroicons:credit-card'
  },
  {
    key: 'payment_amount',
    title: '成交总额',
    decimals: 2,
    value: 0,
    today: 0,
    yesterday: 0,
    unit: '¥',
    color: {
      start: '#865ec0',
      end: '#5144b4'
    },
    icon: 'ant-design:money-collect-outlined'
  },
  // {
  //   key: 'payment_order',
  //   title: '成交总量',
  //   value: 0,
  //   today: 0,
  //   yesterday: 0,
  //   unit: '',
  //   color: {
  //     start: '#fcbc25',
  //     end: '#f68057'
  //   },
  //   icon: 'ant-design:trademark-circle-outlined'
  // }
]);

interface GradientBgProps {
  gradientColor: string;
}

const [DefineGradientBg, GradientBg] = createReusableTemplate<GradientBgProps>();

function getGradientColor(color: CardData['color']) {
  return `linear-gradient(to bottom right, ${color.start}, ${color.end})`;
}

async function init() {
  const { data, error } = await getPromoterStatisticSubtotalData({ admin_id: props.adminId });

  if (error) {
    return;
  }

  cardData.value[0].value = data.subtotal.platform_content_material;
  cardData.value[0].today = data.today.content_material;
  cardData.value[0].yesterday = data.yesterday.content_material;

  cardData.value[1].value = data.subtotal.platform_content_course;
  cardData.value[1].today = data.today.content_course;
  cardData.value[1].yesterday = data.yesterday.content_course;

  cardData.value[2].value = data.subtotal.content_special;
  cardData.value[2].today = data.today.content_special;
  cardData.value[2].yesterday = data.yesterday.content_special;

  // cardData.value[2].value = data.subtotal.content_download;
  // cardData.value[2].today = data.today.content_download;
  // cardData.value[2].yesterday = data.yesterday.content_download;

  cardData.value[3].value = data.subtotal.consume_credit;
  cardData.value[3].today = data.today.consume_credit;
  cardData.value[3].yesterday = data.yesterday.consume_credit;

  cardData.value[4].value = Number.parseFloat(data.subtotal.payment_amount);
  cardData.value[4].today = Number.parseFloat(data.today.payment_amount);
  cardData.value[4].yesterday = Number.parseFloat(data.yesterday.payment_amount);

  // cardData.value[4].value = data.subtotal.payment_order;
  // cardData.value[4].today = data.today.payment_order;
  // cardData.value[4].yesterday = data.yesterday.payment_order;


}

init();
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <!-- define component start: GradientBg -->
    <DefineGradientBg v-slot="{ $slots, gradientColor }">
      <div class="rd-8px px-16px pb-4px pt-8px text-white" :style="{ backgroundImage: gradientColor }">
        <component :is="$slots.default" />
      </div>
    </DefineGradientBg>
    <!-- define component end: GradientBg -->

    <NGrid cols="s:1 m:2 l:4" responsive="screen" :x-gap="16" :y-gap="16">
      <NGi v-for="item in cardData" :key="item.key">
        <GradientBg :gradient-color="getGradientColor(item.color)" class="flex-1">
          <h3 class="text-16px">{{ item.title }}</h3>
          <div class="flex justify-between pt-12px">
            <SvgIcon :icon="item.icon" class="text-32px" />
            <CountTo
              :prefix="item.unit"
              :start-value="0"
              :end-value="item.value"
              :decimals="item.decimals"
              class="text-30px text-white dark:text-dark"
            />
          </div>
          <NSpace v-if="item.key !== 'attachment'" justify="end">
            <div>昨天：{{ item.yesterday }}</div>
            <div>今日：{{ item.today }}</div>
          </NSpace>
          <NSpace v-else justify="end">
            <div>总大小：{{ getFileSize(item.today) }}</div>
          </NSpace>
        </GradientBg>
      </NGi>
    </NGrid>
  </NCard>
</template>

<style scoped></style>
