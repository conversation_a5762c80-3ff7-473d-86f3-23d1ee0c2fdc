<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useLoading } from '~/packages/hooks';
import { getStatisticDateList } from '@/service/api';
import { round } from 'lodash-es';
import LineChart, { ChartSeries } from './line-chart.vue';
import PieChart from './pie-chart.vue';
import {useRouterPush} from "@/hooks/common/router";
import {NButton} from "naive-ui";

defineOptions({
  name: 'TabChat'
});

interface ChartOptions {
  title?: string;
  tab?: string;
  date: string[];
  xAxis: string[];
  data: {
    [key: string]: ChartSeries[];
  };
}

export interface PaymentType {
  consumeCredit: number;
  paymentAmount: number;
  paymentUser: number;
  paymentCreditAmount: number;
  paymentCreditUser: number;
  paymentTopicAmount: number;
  paymentTopicUser: number;
  paymentCourseAmount: number;
  paymentCourseUser: number;
  paymentMaterialUser: number;
  platformMaterialAmount: number;
}

const { loading, startLoading, endLoading } = useLoading(false);

const chartData = reactive<ChartOptions>({
  tab: 'payment',
  date: [],
  xAxis: [],
  data: {
    payment: [],
    credit: [],
    user: [],
    content: [],
    special: []
  }
});

const paymentData = ref<PaymentType>({
  consumeCredit: 0,
  paymentAmount: 0,
  paymentUser: 0,
  paymentCreditAmount: 0,
  paymentCreditUser: 0,
  paymentTopicAmount: 0,
  paymentTopicUser: 0,
  paymentCourseAmount: 0,
  paymentCourseUser: 0,
  paymentMaterialUser: 0,
  platformMaterialAmount: 0
});

const { routerPushByKey } = useRouterPush();
async function init() {
  startLoading();

  chartData.xAxis = [];

  const { data, error } = await getStatisticDateList({ date_range: chartData.date });

  endLoading();

  if (error) {
    return;
  }

  let paymentTemp: PaymentType = {
    consumeCredit: 0,
    paymentAmount: 0,
    paymentUser: 0,
    paymentCreditAmount: 0,
    paymentCreditUser: 0,
    paymentTopicAmount: 0,
    paymentTopicUser: 0,
    paymentCourseAmount: 0,
    paymentCourseUser: 0,
    platformMaterialAmount: 0,
    paymentMaterialUser: 0,

  };

  data.forEach((item, index) => {
    chartData.xAxis.push(item.date);

    const paymentAmount = parseFloat(item.payment_amount);
    const paymentCreditAmount = parseFloat(item.payment_credit_amount);
    const paymentTopicAmount = parseFloat(item.payment_topic_amount);
    const paymentCourseAmount = parseFloat(item.payment_course_amount);
    const paymentMaterialAmount = parseFloat(item.platform_material_amount);
    const paymentMaterialUser = parseFloat(item.payment_material_user);

    paymentTemp.paymentAmount = round(paymentTemp.paymentAmount + paymentAmount, 2);
    paymentTemp.paymentCreditAmount = round(paymentTemp.paymentCreditAmount + paymentCreditAmount, 2);
    paymentTemp.paymentTopicAmount = round(paymentTemp.paymentTopicAmount + paymentTopicAmount, 2);
    paymentTemp.paymentCourseAmount = round(paymentTemp.paymentCourseAmount + paymentCourseAmount, 2);
    paymentTemp.platformMaterialAmount = round(paymentTemp.platformMaterialAmount + paymentMaterialAmount, 2);

    paymentTemp.paymentUser += item.payment_user;
    paymentTemp.paymentCreditUser += item.payment_credit_user;
    paymentTemp.paymentTopicUser += item.payment_topic_user;
    paymentTemp.paymentCourseUser += item.payment_course_user;
    paymentTemp.paymentMaterialUser += item.payment_material_user;

    paymentTemp.consumeCredit += item.consume_credit;

    if (index > 0) {
      chartData.data.payment[0].data.push(paymentAmount);
      chartData.data.payment[1].data.push(paymentCreditAmount);
      chartData.data.payment[2].data.push(paymentTopicAmount);
      chartData.data.payment[3].data.push(paymentCourseAmount);
      chartData.data.payment[4].data.push(paymentMaterialAmount);

      chartData.data.credit[0].data.push(item.consume_credit);

      chartData.data.user[0].data.push(item.user_register);
      chartData.data.user[1].data.push(item.user_active);

      chartData.data.content[0].data.push(item.content_material);
      chartData.data.content[1].data.push(item.content_course);

      chartData.data.special[0].data.push(item.content_special);
    } else {
      chartData.data = {
        payment: [
          {
            name: '总收入',
            data: [paymentAmount]
          },
          {
            name: '积分',
            data: [paymentCreditAmount]
          },
          {
            name: '题库',
            data: [paymentTopicAmount]
          },
          {
            name: '课程',
            data: [paymentCourseAmount]
          }, {
            name: '资料',
            data: [paymentMaterialAmount]
          }
        ],
        credit: [
          {
            name: '积分数量',
            data: [item.consume_credit]
          }
        ],
        user: [
          {
            name: '注册用户',
            data: [item.user_register]
          },
          {
            name: '活跃用户',
            data: [item.user_active]
          }
        ],
        content: [
          {
            name: '资料数',
            data: [item.content_material]
          },
          {
            name: '课程数',
            data: [item.content_course]
          }
        ],
        special: [
          {
            name: '专题',
            data: [item.content_special]
          }
        ]
      };
    }
  });

  paymentData.value = paymentTemp;
}

init();

function handleTabChange(name) {
  if (name === 'rank') {
    console.log(1)
    routerPushByKey('stat_content-rank')
  } else {
    console.log(2)
    chartData.tab = name
  }
}
</script>

<template>
<NCard :bordered="false" class="h-full rounded-8px shadow-sm">
    <template #header>
      <div class="w-320px">
        <DateRange v-model:value="chartData.date" :days="14" :today="false" @change="init" />
      </div>
    </template>
    <NTabs v-if="!loading" type="line" :value="chartData.tab" animated @update:value="handleTabChange">
      <NTabPane name="payment" tab="付费统计">
        <NCard>
          <NGrid>
            <NGridItem :span="5" >
              <NDescriptions :column="1" label-style="font-size: 18px; font-weight: bold;">
                <NDescriptionsItem label="付费统计">
                  <NDescriptions :column="2" label-style="color: #999999;" content-style="font-size: 16px;width: 150px;">
                    <NDescriptionsItem label="总金额">{{ paymentData.paymentAmount }}</NDescriptionsItem>
                    <NDescriptionsItem label="付费用户总数"> {{ paymentData.paymentUser }}</NDescriptionsItem>
                  </NDescriptions>
                </NDescriptionsItem>
                <NDescriptionsItem label="兑换积分">
                  <NDescriptions :column="2" label-style="color: #999999;" content-style="font-size: 16px;width: 150px;">
                    <NDescriptionsItem label="金额">{{ paymentData.paymentCreditAmount }}</NDescriptionsItem>
                    <NDescriptionsItem label="付费用户数">{{ paymentData.paymentCreditUser }}</NDescriptionsItem>
                  </NDescriptions>
                </NDescriptionsItem>
                <NDescriptionsItem label="购买题库">
                  <NDescriptions :column="2" label-style="color: #999999;" content-style="font-size: 16px;width: 150px;">
                    <NDescriptionsItem label="金额">{{ paymentData.paymentTopicAmount }}</NDescriptionsItem>
                    <NDescriptionsItem label="付费用户数">{{ paymentData.paymentTopicUser }}</NDescriptionsItem>
                  </NDescriptions>
                </NDescriptionsItem>
                <NDescriptionsItem label="购买课程">
                  <NDescriptions :column="2" label-style="color: #999999;" content-style="font-size: 16px;width: 150px;">
                    <NDescriptionsItem label="金额">{{ paymentData.paymentCourseAmount }}</NDescriptionsItem>
                    <NDescriptionsItem label="付费用户数">{{ paymentData.paymentCourseUser }}</NDescriptionsItem>
                  </NDescriptions>
                </NDescriptionsItem>
                <NDescriptionsItem label="购买资料">
                  <NDescriptions :column="2" label-style="color: #999999;" content-style="font-size: 16px;width: 150px;">
                    <NDescriptionsItem label="金额">{{ paymentData.platformMaterialAmount }}</NDescriptionsItem>
                    <NDescriptionsItem label="付费用户数">{{ paymentData.paymentMaterialUser }}</NDescriptionsItem>
                  </NDescriptions>
                </NDescriptionsItem>
              </NDescriptions>
            </NGridItem>
            <NGridItem :span="6">
              <div class="w-400px">
                <PieChart :payment="paymentData" />
              </div>
            </NGridItem>
          </NGrid>
        </NCard>
        <NCard title="收入趋势">
          <LineChart :date="chartData.date" :x-axis="chartData.xAxis" :series="chartData.data.payment" :total-index="0" unit="元" />
        </NCard>
      </NTabPane>
      <NTabPane name="credit" tab="消耗统计">
        <NCard title="积分统计">
          <LineChart :date="chartData.date" :x-axis="chartData.xAxis" :series="chartData.data.credit" :total-index="0" unit="积分" />
        </NCard>
      </NTabPane>
      <NTabPane name="user" tab="用户统计">
        <NCard title="用户统计">
          <LineChart :date="chartData.date" :x-axis="chartData.xAxis" :series="chartData.data.user" />
        </NCard>
      </NTabPane>
      <NTabPane name="content" tab="内容统计">
        <NCard title="内容统计">
          <LineChart :date="chartData.date" :x-axis="chartData.xAxis" :series="chartData.data.content" unit="条" />
        </NCard>
      </NTabPane>
      <NTabPane name="special" tab="专题统计">
        <NCard title="专题统计">
          <LineChart :date="chartData.date" :x-axis="chartData.xAxis" :series="chartData.data.special" unit="个" />
        </NCard>
      </NTabPane>
      <NTabPane name="rank" tab="排行榜"></NTabPane>
    </NTabs>
  </NCard>
</template>

<style scoped></style>
