<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useLoading } from '@sa/hooks';
import { round } from 'lodash-es';
import { getPromoterStatisticDateList } from '@/service/api';
import { permissionStat as permissionStatApi } from '@/service/api';
import type { ChartSeries } from './line-chart.vue';
import LineChart from './line-chart.vue';
import {useRouterPush} from "@/hooks/common/router";

defineOptions({
  name: 'TabChat'
});
const { routerPushByKey } = useRouterPush();

interface Props {
  adminId?: number;
}

const props = defineProps<Props>();
let permissionStat = ref(false);
interface ChartOptions {
  title?: string;
  tab?: string;
  date: string[];
  xAxis: string[];
  data: {
    [key: string]: ChartSeries[];
  };
}

export interface PaymentType {
  consumeCredit: number;
  consumeCreditContent: number;
  consumeCreditSpecial: number;
  paymentAmount: number;
  paymentUser: number;
  paymentCourseAmount: number;
  paymentCourseUser: number;
}

const { loading, startLoading, endLoading } = useLoading(false);

const chartData = reactive<ChartOptions>({
  tab: 'payment',
  date: [],
  xAxis: [],
  data: {
    payment: [],
    credit: [],
    content: [],
    special: []
  }
});

const paymentData = ref<PaymentType>({
  consumeCredit: 0,
  consumeCreditContent: 0,
  consumeCreditSpecial: 0,
  paymentAmount: 0,
  paymentUser: 0,
  paymentCourseAmount: 0,
  paymentCourseUser: 0
});

async function init() {
  startLoading();

  chartData.xAxis = [];

  const { data, error } = await getPromoterStatisticDateList({ date_range: chartData.date, admin_id: props.adminId });

  endLoading();

  if (error) {
    return;
  }

  const paymentTemp: PaymentType = {
    consumeCredit: 0,
    consumeCreditContent: 0,
    consumeCreditSpecial: 0,
    paymentAmount: 0,
    paymentUser: 0,
    paymentCourseAmount: 0,
    paymentCourseUser: 0
  };

  data.forEach((item, index) => {
    chartData.xAxis.push(item.date);

    const paymentAmount = parseFloat(item.payment_amount);
    const paymentCourseAmount = parseFloat(item.payment_course_amount);
    const paymentMaterialAmount = parseFloat(item.platform_material_amount);

    paymentTemp.paymentAmount = round(paymentTemp.paymentAmount + paymentAmount, 2);
    paymentTemp.paymentCourseAmount = round(paymentTemp.paymentCourseAmount + paymentCourseAmount, 2);

    paymentTemp.paymentUser += item.payment_user;
    paymentTemp.paymentCourseUser += item.payment_course_user;

    paymentTemp.consumeCredit += item.consume_credit;
    paymentTemp.consumeCreditContent += item.consume_credit_content;
    paymentTemp.consumeCreditSpecial += item.consume_credit_special;

    if (index > 0) {
      chartData.data.payment[0].data.push(paymentCourseAmount);
      chartData.data.payment[1].data.push(paymentMaterialAmount);

      chartData.data.credit[0].data.push(item.consume_credit);
      chartData.data.credit[1].data.push(item.consume_credit_content);
      chartData.data.credit[2].data.push(item.consume_credit_special);

      chartData.data.content[0].data.push(item.content_material);
      chartData.data.content[1].data.push(item.content_course);

      chartData.data.special[0].data.push(item.content_special);
    } else {
      chartData.data = {
        payment: [
          {
            name: '总收入',
            data: [paymentAmount]
          },
          {
            name: '课程',
            data: [paymentCourseAmount]
          }
          ,{
            name: '资料',
            data: [paymentMaterialAmount]
          }
        ],
        credit: [
          {
            name: '积分数量',
            data: [item.consume_credit]
          },
          {
            name: '内容',
            data: [item.consume_credit_content]
          },
          {
            name: '专题',
            data: [item.consume_credit_special]
          }
        ],
        content: [
          {
            name: '资料数',
            data: [item.content_material]
          },
          {
            name: '课程数',
            data: [item.content_course]
          }
        ],
        special: [
          {
            name: '专题',
            data: [item.content_special]
          }
        ]
      };
    }
  });

  paymentData.value = paymentTemp;
}
function handleTabChange(name) {
  if (name === 'rank') {
    routerPushByKey('stat_content-rank')
  } else {
    chartData.tab = name
  }
}

permissionStatApi().then(res => {
  permissionStat = res.data.permission;
})
init();
</script>

<template>
<NCard :bordered="false" class="h-full rounded-8px shadow-sm">
    <template #header>
      <div class="w-320px">
        <DateRange v-model:value="chartData.date" :days="14" :today="false" @change="init" />
      </div>
    </template>
    <NTabs v-if="!loading" type="line" :value="chartData.tab" animated @update:value="handleTabChange">
      <NTabPane name="payment" tab="付费统计">
        <NCard title="收入趋势">
          <LineChart :date="chartData.date" :x-axis="chartData.xAxis" :series="chartData.data.payment" :total-index="0" unit="元" />
        </NCard>
      </NTabPane>
      <NTabPane name="credit" tab="消耗统计">
        <NCard title="积分统计">
          <LineChart :date="chartData.date" :x-axis="chartData.xAxis" :series="chartData.data.credit" :total-index="0" unit="积分" />
        </NCard>
      </NTabPane>
      <NTabPane name="content" tab="内容统计">
        <NCard title="内容统计">
          <LineChart :date="chartData.date" :x-axis="chartData.xAxis" :series="chartData.data.content" unit="条" />
        </NCard>
      </NTabPane>
      <NTabPane name="special" tab="专题统计">
        <NCard title="专题统计">
          <LineChart :date="chartData.date" :x-axis="chartData.xAxis" :series="chartData.data.special" unit="个" />
        </NCard>
      </NTabPane>
      <NTabPane name="rank" tab="排行榜" v-if="permissionStat">

      </NTabPane>
    </NTabs>
  </NCard>
</template>

<style scoped></style>
