<script setup lang="tsx">
import { <PERSON><PERSON><PERSON><PERSON>, NPopconfirm } from 'naive-ui';
import { ref } from 'vue';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import DateFormat from '@/components/common/date/date-format.vue';
import { deleteOrg, fetchOrgList } from '@/service/api';
import { $t } from '@/locales';
import { closeTab } from '@/utils/tab';
import TableSearch from './modules/table-search.vue';
import TableHeaderOperation from './modules/table-header-operation.vue';
import AdminOperate from './modules/admin-operate.vue';
import BalanceOperate from './modules/balance-operate.vue';
import OrgOperate from './modules/org-operate.vue';
import OrgStatistics from './modules/org-statistics.vue';

const { route, routerPush } = useRouterPush();
const orgRef = ref();
const adminRef = ref();
const balanceRef = ref();
const orgStatisticsRef = ref();

const {
  columns,
  filteredColumns,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable<Api.Org.Org, typeof fetchOrgList, 'index' | 'operate'>({
  apiFn: fetchOrgList,
  apiParams: {
    current: 1,
    size: 20,
    id: route.value.query.user_id ? route.value.query.user_id : null,
    name: null,
    created_at: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: (row): string => getIndex(row.id),
      align: 'center',
      width: 80
    },
    {
      key: 'name',
      title: '名称',
      align: 'center',
      width: 200
    },
    {
      key: 'contact',
      title: '联系方式',
      align: 'center',
      width: 120
    },
    {
      key: 'area_text',
      title: '所在地',
      align: 'center',
      width: 180,
      render: row => {
        return row.area_text.join('-');
      }
    },
    {
      key: 'balance',
      title: '余额',
      align: 'center',
      width: 80,
      render: row => {
        return row.balance.toFixed(2);
      }
    },
    {
      key: 'enable_enroll',
      title: '报名系统',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'default'
        };

        const label = row.enable_enroll === 1 ? '启用' : '禁用';

        return (
          <NTag type={tagMap[row.enable_enroll]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      minWidth: 80,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'last_logged_at' as any,
      title: '最后登录时间',
      align: 'center',
      minWidth: 80,
      render: row => {
        return <DateFormat date={row?.main_admin?.last_logged_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 560,
      render: row => (
        <n-space>
          <NButton type="tertiary" ghost size="small" onClick={() => handleStatistics(row)}>
            机构统计
          </NButton>
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row)}>
            编辑机构
          </NButton>
          <NButton type="warning" ghost size="small" onClick={() => handleAdmin(row.id, row.main_admin)}>
            超管设置
          </NButton>
          <NButton type="success" ghost size="small" onClick={() => handleResource(row)}>
            资源设置
          </NButton>
          <NButton type="default" ghost size="small" onClick={() => handleBalance(row)}>
            余额设置
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </n-space>
      )
    }
  ]
});

function getIndex(index: number) {
  return String(index);
}

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    id: null
  });
}

function handleStatistics(row: Api.Org.Org) {
  orgStatisticsRef.value.open(row);
}

function handleEdit(row: Api.Org.Org) {
  orgRef.value.open(row);
}

function handleAdmin(id: number, row?: Api.Org.Admin) {
  adminRef.value.open(id, row);
}

function handleBalance(row: Api.Org.Org) {
  balanceRef.value.open(row.id, row.name, row.balance);
}

function handleResource(row: Api.Org.Org) {
  closeTab('org_resource');
  routerPush({
    name: 'org_resource',
    query: { name: row.name, id: row.id }
  });
}

async function handleDelete(row: Api.Org.Org) {
  const { error } = await deleteOrg(row.id);
  if (error) {
    return;
  }

  window.$message?.success($t('common.deleteSuccess'));

  await getData();
}
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getData" />
    <NCard title="机构列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="filteredColumns"
          :show-batch-delete="false"
          :show-add="false"
          :loading="loading"
          @refresh="getData"
          @add-admin="handleAdmin"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="702"
        :loading="loading"
        remote
        :pagination="pagination"
        :row-key="item => item.id"
        class="sm:h-full"
      />
    </NCard>
    <OrgOperate ref="orgRef" @reload="getData" />
    <AdminOperate ref="adminRef" @reload="getData" />
    <BalanceOperate ref="balanceRef" @reload="getData" />
    <OrgStatistics ref="orgStatisticsRef" />
  </div>
</template>

<style scoped></style>
