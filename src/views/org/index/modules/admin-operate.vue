<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { createOrgAdmin, updateOrgAdminPassword } from '@/service/api';
import { $t } from '@/locales';

interface Model {
  id: number;
  username: string;
  password: string;
  password_confirmation: string;
}

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();
const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const active = ref(false);
const saveLoading = ref<boolean>(false);
const orgId = ref<number>(0);
const model = reactive<Model>({ ...createDefaultModel() });

const rules = {
  username: defaultRequiredRule,
  password: [
    defaultRequiredRule,
    {
      min: 6,
      message: '密码长度不能小于6位',
      trigger: 'blur'
    }
  ],
  password_confirmation: [
    {
      required: true,
      message: '请输入确认密码',
      trigger: 'blur'
    },
    {
      validator: (_rule: any, value: string) => {
        if (value !== model.password) {
          return new Error('两次输入密码不一致');
        }
        return true;
      },
      trigger: 'blur'
    }
  ]
};

function createDefaultModel(): Model {
  return {
    id: 0,
    username: '',
    password: '',
    password_confirmation: ''
  };
}

function open(oid: number, data?: { id: number; username: string }) {
  if (data) {
    model.id = data.id;
    model.username = data.username;
    model.password = '';
    model.password_confirmation = '';
  } else {
    Object.assign(model, { ...createDefaultModel() });
  }

  orgId.value = oid;
  active.value = true;
}

async function handleSubmit() {
  await validate();
  saveLoading.value = true;

  try {
    if (model.id) {
      const { error } = await updateOrgAdminPassword(orgId.value, model.id, {
        password: model.password,
        password_confirmation: model.password_confirmation
      });

      if (error) {
        return;
      }

      window.$message?.success($t('common.updateSuccess'));
    } else {
      const { error } = await createOrgAdmin(orgId.value, model);

      if (error) {
        return;
      }

      window.$message?.success($t('common.addSuccess'));
    }

    emit('reload');
    active.value = false;
  } catch (error) {
    window.$message?.error('操作失败');
  } finally {
    saveLoading.value = false;
  }
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="active" :width="500">
    <NDrawerContent>
      <template #header>{{ model.id > 0 ? '修改超级管理员' : '创建超级管理员' }}</template>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem label="用户名" path="username">
          <NInput v-model:value="model.username" placeholder="请输入用户名" :disabled="model.id > 0" />
        </NFormItem>
        <NFormItem label="密码" path="password">
          <NInput v-model:value="model.password" type="password" placeholder="请输入密码" show-password-on="click" />
        </NFormItem>
        <NFormItem label="确认密码" path="password_confirmation">
          <NInput
            v-model:value="model.password_confirmation"
            type="password"
            placeholder="请再次输入密码"
            show-password-on="click"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton type="tertiary" @click="active = false">取消</NButton>
          <NButton type="primary" :loading="saveLoading" :disabled="saveLoading" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>
