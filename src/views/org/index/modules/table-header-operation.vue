<script setup lang="ts">
import { ref } from 'vue';
import type { FilteredColumn } from '@/hooks/common/table';
import OrgOperate from './org-operate.vue';

interface Props {
  loading?: boolean;
}

withDefaults(defineProps<Props>(), {});

interface Emits {
  (e: 'refresh'): void;
  (e: 'addAdmin', id: number): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<FilteredColumn[]>('columns', {
  default: () => []
});

const orgRef = ref();

function add() {
  orgRef.value.open();
}

function refresh() {
  emit('refresh');
}

function refreshAndOpenAdmin(id: number) {
  emit('addAdmin', id);
}
</script>

<template>
  <NSpace wrap justify="end" class="<sm:w-200px">
    <slot name="prefix"></slot>
    <slot name="default">
      <NButton size="small" ghost type="primary" @click="add">
        <template #icon>
          <icon-ic-round-plus class="text-icon" />
        </template>
        {{ $t('common.add') }}
      </NButton>
    </slot>
    <NButton size="small" @click="refresh">
      <template #icon>
        <icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loading }" />
      </template>
      {{ $t('common.refresh') }}
    </NButton>
    <TableColumnSetting v-model:columns="columns" />
    <OrgOperate ref="orgRef" @reload="refresh" @add-admin="refreshAndOpenAdmin" />
    <slot name="suffix"></slot>
  </NSpace>
</template>

<style scoped></style>
