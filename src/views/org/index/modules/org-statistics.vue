<script setup lang="tsx">
import { ref } from 'vue';
import {
  ArrowForwardOutline,
  BusinessOutline,
  CalendarOutline,
  CallOutline,
  ChevronBackOutline,
  ChevronForwardOutline,
  IdCardOutline,
  LocationOutline,
  PersonOutline,
  SchoolOutline,
  TimeOutline,
  WalletOutline
} from '@vicons/ionicons5';
import { fetchOrgBalanceRecords } from '@/service/api/org';
import { dateFormat } from '@/utils/date';

const active = ref(false);
const showBalanceRecords = ref(false);
const drawerWidth = 680;
const org = ref<Api.Org.Org>();
const balanceRecords = ref<Api.Org.BalanceRecord[]>([]);

// 分页相关状态
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
});

function open(data: Api.Org.Org) {
  org.value = data;
  active.value = true;
  showBalanceRecords.value = false;
  // 重置分页
  pagination.value.page = 1;
}

function toggleBalanceRecords() {
  showBalanceRecords.value = !showBalanceRecords.value;
  if (showBalanceRecords.value) {
    fetchBalanceRecords();
  }
}

function fetchBalanceRecords() {
  fetchOrgBalanceRecords({
    org_id: org.value?.id as number,
    current: pagination.value.page,
    size: pagination.value.pageSize
  }).then(res => {
    balanceRecords.value = res.data?.records || [];
    pagination.value.total = res.data?.total || 0;
  });
}

function handlePageChange(page: number) {
  pagination.value.page = page;
  fetchBalanceRecords();
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="active" :width="drawerWidth">
    <NDrawerContent>
      <template #header>机构统计</template>
      <NSpace vertical size="large">
        <!-- 机构基本信息 -->
        <NCard title="机构基本信息" :bordered="false" size="small">
          <NSpace vertical>
            <NSpace align="center">
              <NAvatar :size="48" color="rgba(32, 128, 240, 0.1)" round>
                <NIcon size="32" color="#2080f0">
                  <BusinessOutline />
                </NIcon>
              </NAvatar>
              <NSpace vertical size="small">
                <NText strong class="text-18px font-bold">{{ org?.name }}</NText>
                <NText depth="3">{{ org?.alias }}</NText>
              </NSpace>
            </NSpace>

            <NDivider />

            <NGrid :cols="2" :x-gap="16" :y-gap="16">
              <NGi>
                <NSpace vertical size="small">
                  <NSpace align="center">
                    <NIcon size="16" color="#666">
                      <IdCardOutline />
                    </NIcon>
                    <NText depth="3">机构ID</NText>
                  </NSpace>
                  <NText>{{ org?.id }}</NText>
                </NSpace>
              </NGi>

              <NGi>
                <NSpace vertical size="small">
                  <NSpace align="center">
                    <NIcon size="16" color="#666">
                      <CallOutline />
                    </NIcon>
                    <NText depth="3">联系方式</NText>
                  </NSpace>
                  <NText>{{ org?.contact }}</NText>
                </NSpace>
              </NGi>

              <NGi>
                <NSpace vertical size="small">
                  <NSpace align="center">
                    <NIcon size="16" color="#666">
                      <LocationOutline />
                    </NIcon>
                    <NText depth="3">所在地</NText>
                  </NSpace>
                  <NText>{{ org?.area_text?.join('-') }}</NText>
                </NSpace>
              </NGi>

              <NGi>
                <NSpace vertical size="small">
                  <NSpace align="center">
                    <NIcon size="16" color="#666">
                      <WalletOutline />
                    </NIcon>
                    <NText depth="3">余额</NText>
                  </NSpace>
                  <NSpace align="center" justify="space-between" class="w-full">
                    <NText type="info" strong>¥ {{ org?.balance?.toFixed(2) }}</NText>
                    <NTooltip trigger="hover">
                      <template #trigger>
                        <NButton text type="info" size="small" @click="toggleBalanceRecords">
                          <template #icon>
                            <ArrowForwardOutline />
                          </template>
                          查看记录
                        </NButton>
                      </template>
                      点击查看余额变动记录
                    </NTooltip>
                  </NSpace>
                </NSpace>
              </NGi>
            </NGrid>
          </NSpace>
        </NCard>

        <!-- 机构统计数据 -->
        <NCard title="机构统计数据" :bordered="false" size="small">
          <NGrid :cols="3" :x-gap="16">
            <NGi>
              <NCard embedded hoverable>
                <NSpace align="center" size="large">
                  <NAvatar :size="48" color="rgba(32, 128, 240, 0.1)" round>
                    <NIcon size="32" color="#2080f0">
                      <PersonOutline />
                    </NIcon>
                  </NAvatar>
                  <NSpace vertical size="small">
                    <NText depth="3">学员人数</NText>
                    <NStatistic :value="org?.total_students || 0">
                      <template #suffix>
                        <NText depth="2">人</NText>
                      </template>
                    </NStatistic>
                  </NSpace>
                </NSpace>
              </NCard>
            </NGi>

            <NGi>
              <NCard embedded hoverable>
                <NSpace align="center" size="large">
                  <NAvatar :size="48" color="rgba(32, 128, 240, 0.1)" round>
                    <NIcon size="32" color="#2080f0">
                      <SchoolOutline />
                    </NIcon>
                  </NAvatar>
                  <NSpace vertical size="small">
                    <NText depth="3">培训总数</NText>
                    <NStatistic :value="org?.total_trained || 0">
                      <template #suffix>
                        <NText depth="2">次</NText>
                      </template>
                    </NStatistic>
                  </NSpace>
                </NSpace>
              </NCard>
            </NGi>

            <NGi>
              <NCard embedded hoverable>
                <NSpace align="center" size="large">
                  <NAvatar :size="48" color="rgba(32, 128, 240, 0.1)" round>
                    <NIcon size="32" color="#2080f0">
                      <CalendarOutline />
                    </NIcon>
                  </NAvatar>
                  <NSpace vertical size="small">
                    <NText depth="3">开班总数</NText>
                    <NStatistic :value="org?.total_classes || 0">
                      <template #suffix>
                        <NText depth="2">班</NText>
                      </template>
                    </NStatistic>
                  </NSpace>
                </NSpace>
              </NCard>
            </NGi>
          </NGrid>
        </NCard>
      </NSpace>

      <!-- 余额变动记录 -->
      <NCard v-if="showBalanceRecords" title="余额变动记录" :bordered="false" size="small">
        <NList>
          <NListItem v-for="record in balanceRecords" :key="record.id">
            <NSpace vertical :size="12" class="w-full">
              <NSpace justify="space-between" align="center" class="w-full">
                <NSpace align="center" :size="8">
                  <NTag :type="record.type === 'income' ? 'success' : (record.type === 'refund' ? 'warning' : 'error')" size="small" round>
                    {{ record.type === 'income' ? '收入' : (record.type === 'refund' ? '退款' : '支出') }}
                  </NTag>
                  <NText depth="3">{{ record.remark }}</NText>
                </NSpace>
                <NText :type="record.type === 'income' ? 'success' : (record.type === 'refund' ? 'warning' : 'error')" strong>
                  {{ record.type === 'income' || record.type === 'refund' ? `+${record.amount}` : `-${Math.abs(record.amount).toFixed(2)}` }}
                </NText>
              </NSpace>

              <NSpace justify="space-between" align="center" class="w-full">
                <NSpace align="center" :size="8">
                  <NText depth="3" size="small">原始余额: {{ record.origin_balance }}</NText>
                  <NText depth="3" size="small">变动后: {{ Number(record.origin_balance) + Number(record.amount) }}</NText>
                </NSpace>
                <NSpace align="center" size="small">
                  <NIcon size="14" color="#999">
                    <TimeOutline />
                  </NIcon>
                  <NText depth="3">{{ dateFormat(record.created_at, 'YYYY-MM-DD HH:mm') }}</NText>
                </NSpace>
              </NSpace>
            </NSpace>
          </NListItem>

          <!-- 添加空状态提示 -->
          <NEmpty v-if="balanceRecords.length === 0" description="暂无余额变动记录" />
        </NList>

        <!-- 添加分页控件 -->
        <NSpace justify="space-between" align="center" class="mt-4">
          <NSpace align="center" size="small">
            <NText depth="3" size="small">第 {{ pagination.page }} 页</NText>
            <NText depth="3" size="small">共 {{ pagination.total }} 条</NText>
          </NSpace>

          <NSpace align="center" size="small">
            <NButton
              quaternary
              circle
              :disabled="pagination.page <= 1"
              size="small"
              @click="pagination.page > 1 && handlePageChange(pagination.page - 1)"
            >
              <template #icon>
                <NIcon>
                  <ChevronBackOutline />
                </NIcon>
              </template>
            </NButton>

            <NButton
              quaternary
              circle
              :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)"
              size="small"
              @click="
                pagination.page < Math.ceil(pagination.total / pagination.pageSize) &&
                  handlePageChange(pagination.page + 1)
              "
            >
              <template #icon>
                <NIcon>
                  <ChevronForwardOutline />
                </NIcon>
              </template>
            </NButton>
          </NSpace>
        </NSpace>
      </NCard>

      <template #footer>
        <NSpace>
          <NButton type="primary" @click="active = false">关闭</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
