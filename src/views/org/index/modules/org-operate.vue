<script setup lang="tsx">
import areaLevel from '@province-city-china/level';
import { reactive, ref } from 'vue';
import type { CascaderOption } from 'naive-ui';
import { $t } from '@/locales';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { createOrg, updateOrg } from '@/service/api';

interface Emits {
  (e: 'reload'): void;
  (e: 'addAdmin', id: number): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const active = ref(false);
const saveLoading = ref<boolean>(false);
const org = ref<Api.Org.Org>();
const drawerWidth = 680;

type Model = Pick<Api.Org.Org, 'id' | 'name' | 'alias' | 'contact' | 'area_code' | 'area_text' | 'need_photo' | 'enable_enroll' | 'merchant_id' | 'merchant_name' | 'merchant_enable'>;

const model: Model = reactive(createDefaultModel());

const typeOptions = [
  {
    label: '需要',
    value: 1
  },
  {
    label: '不需要',
    value: 0
  }
];

function createDefaultModel(): Model {
  return {
    id: 0,
    name: '',
    alias: '',
    contact: '',
    area_code: '',
    area_text: [],
    need_photo: 0,
    enable_enroll: 0,
    merchant_id: '',
    merchant_name: '',
    merchant_enable: ''
  };
}

const rules: Record<string, App.Global.FormRule> = {
  name: defaultRequiredRule,
  alias: defaultRequiredRule,
  contact: defaultRequiredRule,
  area_code: defaultRequiredRule,
  need_photo: {
    required: true,
  },
  enable_enroll: {
    required: true,
  },
  merchant_id: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入商户ID'
  },
  merchant_name: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入商户名称'
  },
  merchant_enable: {
    required: true,
    message: '请选择商户状态'
  }
};

function open(data?: Api.Org.Org) {
  if (data) {
    org.value = data;

    Object.assign(model, {
      id: data.id,
      name: data.name,
      alias: data.alias,
      contact: data.contact,
      area_code: data.area_code.toString(),
      area_text: data.area_text,
      need_photo: data.need_photo,
      enable_enroll: data.enable_enroll,
      merchant_id: data?.merchant_config?.mch_id || '',
      merchant_name: data?.merchant_config?.mch_name || '',
      merchant_enable: data?.merchant_config?.enabled
    });
  } else {
    Object.assign(model, { ...createDefaultModel() });
  }

  active.value = true;
}

function handleAreaChange(_value: string, _option: CascaderOption, pathValues: CascaderOption[]) {
  const areaText: string[] = [];
  pathValues.forEach(item => {
    areaText.push(item.name as string);
  });
  model.area_text = areaText;
}

async function handleSubmit() {
  await validate();

  saveLoading.value = true;

  try {
    if (model.id) {
      const { error } = await updateOrg(model.id, model);

      if (error) {
        return;
      }

      window.$message?.success($t('common.updateSuccess'));
    } else {
      const { error, data } = await createOrg(model);

      if (error) {
        return;
      }

      window.$message?.success($t('common.addSuccess'));

      window.$dialog?.create({
        title: '提示',
        content: '是否立即创建机构超级管理员？',
        positiveText: '是',
        negativeText: '否',
        onPositiveClick: () => {
          emit('addAdmin', data.id);
        }
      });
    }

    emit('reload');
    active.value = false;
  } finally {
    saveLoading.value = false;
  }
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="active" :width="drawerWidth">
    <NDrawerContent>
      <template #header>{{ model.id ? '修改机构' : '创建机构' }}</template>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80">
        <NFormItem label="名称" path="name">
          <NInput v-model:value="model.name" placeholder="请输入机构名称" clearable />
        </NFormItem>
        <NFormItem label="机构简称" path="alias">
          <NInput v-model:value="model.alias" placeholder="请输入机构简称" clearable />
        </NFormItem>
        <NFormItem label="联系方式" path="contact">
          <NInput v-model:value="model.contact" placeholder="请输入联系方式" clearable />
        </NFormItem>
        <NFormItem label="所在地" path="area_code">
          <NCascader
            v-model:value="model.area_code"
            :options="areaLevel"
            placeholder="请选择所在地"
            label-field="name"
            value-field="code"
            check-strategy="child"
            clearable
            @update:value="handleAreaChange"
          />
        </NFormItem>
        <NFormItem label="证件照" path="need_photo">
          <NRadioGroup v-model:value="model.need_photo">
            <NRadio v-for="item in typeOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </NRadio>
            <span style="margin-left: 8px; font-size: 13px; color: #999">
                用户观看课程时，是否需要上传证件照
            </span>
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="报名系统" path="enable_enroll">
          <NRadioGroup v-model:value="model.enable_enroll">
            <NRadio :value="1">启用</NRadio>
            <NRadio :value="0">禁用</NRadio>
            <span style="margin-left: 8px; font-size: 13px; color: #999">
                是否启用报名系统功能
            </span>
          </NRadioGroup>
        </NFormItem>

        <!-- 商户设置表单 - 仅在报名系统启用时显示 -->
        <template v-if="model.enable_enroll === 1">
          <NDivider title-placement="left">
            <span style="font-size: 14px; color: #666">机构商户设置</span>
          </NDivider>

          <NFormItem label="商户ID" path="merchant_id">
            <NInput v-model:value="model.merchant_id" placeholder="请输入商户ID" clearable />
          </NFormItem>

          <NFormItem label="商户名称" path="merchant_name">
            <NInput v-model:value="model.merchant_name" placeholder="请输入商户名称" clearable />
          </NFormItem>

          <NFormItem label="商户状态" path="merchant_enable">
            <NRadioGroup v-model:value="model.merchant_enable">
              <NRadio :value="1">启用</NRadio>
              <NRadio :value="0">禁用</NRadio>
            </NRadioGroup>
          </NFormItem>
        </template>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton type="tertiary" @click="active = false">取消</NButton>
          <NButton type="primary" :loading="saveLoading" :disabled="saveLoading" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
