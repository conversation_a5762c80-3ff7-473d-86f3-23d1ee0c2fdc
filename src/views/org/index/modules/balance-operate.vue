<script setup lang="ts">
import { reactive, ref, watch} from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { updateOrgBalance } from '@/service/api';

interface Model {
  name: string;
  balance: number;
  type: 'income' | 'expense';
  amount: number;
  remark: string;
}

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const active = ref(false);
const saveLoading = ref<boolean>(false);
const orgId = ref<number>(0);
const model = reactive<Model>(createDefaultModel());

watch([() => model.type, () => model.amount], ([newType, newAmount]) => {
  model.remark = `管理员${newType === 'income' ? '充值' : '扣费'} ${newAmount} 元`;
});

const rules = {
  type: defaultRequiredRule,
  amount: {
    required: true,
    validator: (_rule: any, value: number) => {
      if (value <= 0) {
        return new Error('请输入大于0的金额');
      }
      return true;
    },
    trigger: 'blur'
  },
  remark: defaultRequiredRule
};

const typeOptions = [
  {
    label: '充值',
    value: 'income'
  },
  {
    label: '扣费',
    value: 'expense'
  }
];

function createDefaultModel(): Model {
  return {
    name: '',
    balance: 0,
    type: 'income',
    amount: 1,
    remark: ''
  };
}

function open(id: number, name: string, balance: number) {
  orgId.value = id;
  Object.assign(model, {
    ...createDefaultModel(),
    name,
    balance
  });
  active.value = true;
}

async function handleSubmit() {
  await validate();
  saveLoading.value = true;

  try {
    const { error } = await updateOrgBalance(orgId.value, {
      type: model.type,
      amount: model.amount as number,
      remark: model.remark
    });

    if (error) {
      return;
    }

    window.$message?.success($t('common.updateSuccess'));
    emit('reload');
    active.value = false;
  } finally {
    saveLoading.value = false;
  }
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="active" :width="500">
    <NDrawerContent>
      <template #header>余额设置</template>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem label="机构名称">
          <NText>{{ model.name }}</NText>
        </NFormItem>
        <NFormItem label="当前余额">
          <NText>{{ model.balance.toFixed(2) }}</NText>
        </NFormItem>
        <NFormItem label="操作类型" path="type">
          <NRadioGroup v-model:value="model.type">
            <NRadio v-for="item in typeOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </NRadio>
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="金额" path="amount">
          <NInputNumber v-model:value="model.amount" :min="0" :precision="2" placeholder="请输入金额" />
        </NFormItem>
        <NFormItem label="备注" path="remark">
          <NInput v-model:value="model.remark" type="textarea" placeholder="请输入备注信息" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton type="tertiary" @click="active = false">取消</NButton>
          <NButton type="primary" :loading="saveLoading" :disabled="saveLoading" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>
