<script setup lang="ts">
import areaLevel from '@province-city-china/level';
import { $t } from '@/locales';

defineOptions({
  name: 'OrgSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type AdminSearchParams = CommonType.RecordNullable<{
  id: string;
  name: string;
  contact: string;
  area_code: string;
}>;

const emit = defineEmits<Emits>();

const model = defineModel<AdminSearchParams>('model', { required: true });

function reset() {
  model.value.id = null;
  model.value.name = null;
  model.value.contact = null;
  model.value.area_code = null;
  emit('reset');
}

function search() {
  emit('search');
}
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:4" label="机构ID" path="id" class="pr-24px">
          <NInput v-model:value="model.id" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="机构名称" path="name" class="pr-24px">
          <NInput v-model:value="model.name" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="联系方式" path="contact" class="pr-24px">
          <NInput v-model:value="model.contact" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:4" label="地区" path="area_code" class="pr-24px">
          <NCascader
            v-model:value="model.area_code"
            :options="areaLevel"
            placeholder="请选择所在地"
            label-field="name"
            value-field="code"
            check-strategy="child"
            clearable
          />
        </NFormItemGi>
        <NFormItemGi span="24 s:4">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
