<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { NIcon } from 'naive-ui';
import { SettingsOutline } from '@vicons/ionicons5';
import { closeTab } from '@/utils/tab';
import { useRouterPush } from '@/hooks/common/router';
import CoursePackResource from '@/views/org/resource/modules/course-pack-resource.vue';
import CourseResource from './modules/course-resource.vue';
import TopicResource from './modules/topic-resource.vue';

const route = useRoute();
const { route: router } = useRouterPush();
const orgName = ref('');
const orgId = ref(0);
const courseResourceRef = ref();
const topicResourceRef = ref();
const coursePackResourceRef = ref();

async function handleError() {
  await closeTab(router.value.name as string);
}

onMounted(async () => {
  orgName.value = route.query.name as string;
  orgId.value = Number(route.query.id);

  if (!orgId.value) {
    window.$message?.warning('参数错误');
    await handleError();
  }
});
</script>

<template>
  <div class="flex-vertical-stretch gap-16px <sm:overflow-auto">
    <NCard :bordered="false" size="small">
      <template #header>
        <div class="flex items-center gap-2">
          <NIcon><SettingsOutline /></NIcon>
          <span>{{ orgName }} - 资源设置</span>
        </div>
      </template>
      <NTabs type="line" size="large">
        <NTabPane name="course" tab="课程资源">
          <CourseResource ref="courseResourceRef" :org-id="orgId" />
        </NTabPane>

        <NTabPane name="topic" tab="题库资源">
          <TopicResource ref="topicResourceRef" :org-id="orgId" />
        </NTabPane>

        <NTabPane name="course_pack" tab="课程包资源">
          <CoursePackResource ref="coursePackResourceRef" :org-id="orgId" />
        </NTabPane>
      </NTabs>
    </NCard>
  </div>
</template>

<style scoped></style>
