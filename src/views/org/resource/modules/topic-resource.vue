<script setup lang="tsx">
import { nextTick, onMounted, ref } from 'vue';
import { NButton, NCard, NDataTable, NInput, NInputNumber, NPopconfirm, NSpace, useMessage } from 'naive-ui';
import { deleteOrgTopic, fetchOrgTopics, updateOrgTopicPrice } from '@/service/api';
import { $t } from '@/locales';
import DateFormat from '@/components/common/date/date-format.vue';
import { useTable } from '@/hooks/common/table';
import AddTopic from './add-topic.vue';
import { debounce } from 'lodash-es';

const props = defineProps<{
  orgId: number;
}>();

const message = useMessage();
const addTopicRef = ref();
const selected_topic_ids = ref<number[]>([]);
const updatingRows = ref<Record<string, boolean>>({});
// 用于存储每行的防抖函数
const debounceFunctions = ref<Record<string, Function>>({});

// 题库列表相关
const {
  columns: topicColumns,
  data: topicData,
  loading: topicLoading,
  pagination: topicPagination,
  getData: getTableData,
  searchParams: topicSearchParams,
  updateSearchParams: updateTopicSearchParams,
  resetSearchParams: resetTopicSearchParams
} = useTable<Api.Org.Topic, typeof fetchOrgTopics, 'index' | 'operate'>({
  apiFn: fetchOrgTopics,
  immediate: false,
  apiParams: {
    org_id: props.orgId,
    current: 1,
    size: 20,
    keywords: undefined
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};
    selected_topic_ids.value = res.data?.selected_topic_ids || [];
    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateTopicSearchParams({
      current: page,
      size: pageSize
    });

    getTopicData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      width: 80
    },
    {
      key: 'topic_id',
      title: '题库ID',
      align: 'center',
      width: 120,
      render: row => row.topic_id
    },
    {
      key: 'topic_name',
      title: '题库名称',
      align: 'center',
      width: 200
    },
    {
      key: 'price_original_30',
      title: '原价（30天）',
      align: 'center',
      width: 120,
      render: (row: Api.Org.Topic) => {
        // 确保每行有自己的防抖函数
        if (!debounceFunctions.value[`topic_${row.topic_id}_30`]) {
          debounceFunctions.value[`topic_${row.topic_id}_30`] = debounce(async (price: number) => {
            await handleUpdateTopicPrice(row, price, 'price_original_30');
          }, 800);
        }

        return (
          <div>
            <NInputNumber
              defaultValue={Number(row.price_original_30)}
              size="small"
              min={0}
              precision={2}
              placeholder="请输入题库30天原价"
              loading={updatingRows.value[`${row.topic_id}_price_original_30`]}
              onUpdateValue={(value: number | null) => {
                if (value !== null) {
                  row.price_original_30 = value;
                  // 使用该行专属的防抖函数
                  debounceFunctions.value[`topic_${row.topic_id}_30`](value);
                }
              }}
            />
          </div>
        );
      }
    },
    {
      key: 'price_original_60',
      title: '原价（60天）',
      align: 'center',
      width: 120,
      render: (row: Api.Org.Topic) => {
        // 确保每行有自己的防抖函数
        if (!debounceFunctions.value[`topic_${row.topic_id}_60`]) {
          debounceFunctions.value[`topic_${row.topic_id}_60`] = debounce(async (price: number) => {
            await handleUpdateTopicPrice(row, price, 'price_original_60');
          }, 800);
        }

        return (
          <div>
            <NInputNumber
              defaultValue={Number(row.price_original_60)}
              size="small"
              min={0}
              precision={2}
              placeholder="请输入题库60天原价"
              loading={updatingRows.value[`${row.topic_id}_price_original_60`]}
              onUpdateValue={(value: number | null) => {
                if (value !== null) {
                  row.price_original_60 = value;
                  // 使用该行专属的防抖函数
                  debounceFunctions.value[`topic_${row.topic_id}_60`](value);
                }
              }}
            />
          </div>
        );
      }
    },
    {
      key: 'price_sell_30',
      title: '售价（30天）',
      align: 'center',
      width: 120,
      render: row => row.price_sell_30
    },
    {
      key: 'price_sell_60',
      title: '售价（60天）',
      align: 'center',
      width: 120,
      render: row => row.price_sell_60
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 120,
      render: row => <DateFormat date={row.created_at} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 80,
      render: row => (
        <n-space justify="center">
          <NPopconfirm onPositiveClick={() => handleDeleteTopic(row)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </n-space>
      )
    }
  ]
});

async function getTopicData() {
  updateTopicSearchParams({
    org_id: props.orgId
  });
  await getTableData();
}

function handleSearch() {
  getTopicData();
}

function handleReset() {
  resetTopicSearchParams();
  updateTopicSearchParams({
    keywords: undefined
  });
  getTopicData();
}

async function handleDeleteTopic(row: Api.Org.Topic) {
  const { error } = await deleteOrgTopic(props.orgId, row.topic_id);
  if (error) {
    return;
  }

  message.success($t('common.deleteSuccess'));
  await getTopicData();
}

function handleAddTopic() {
  addTopicRef.value.open(selected_topic_ids.value);
}

async function handleUpdateTopicPrice(row: Api.Org.Topic, price: number, field: string) {
  if (Number.isNaN(price) || price < 0) {
    message.error('价格必须是大于等于0的数字');
    return;
  }

  // 设置更新状态
  updatingRows.value = {
    ...updatingRows.value,
    [`${row.topic_id}_${field}`]: true
  };

  try {
    const { error } = await updateOrgTopicPrice(props.orgId, row.topic_id, price, field);
    if (error) {
      return;
    }

    message.success('题库价格更新成功');
  } finally {
    // 无论成功失败都清除更新状态
    updatingRows.value = {
      ...updatingRows.value,
      [`${row.topic_id}_${field}`]: false
    };
  }
}

onMounted(() => {
  nextTick(() => {
    getTopicData();
  });
});
</script>

<template>
  <NSpace vertical>
    <NCard :bordered="false" size="small">
      <NSpace justify="space-between">
        <NButton type="success" ghost @click="handleAddTopic()">添加题库资源</NButton>
        <NSpace>
          <NInput v-model:value="topicSearchParams.keywords" placeholder="请输入题库ID/名称搜索" clearable />
          <NButton @click="handleReset">
            {{ $t('common.reset') }}
          </NButton>
          <NButton type="primary" ghost @click="handleSearch">
            {{ $t('common.search') }}
          </NButton>
        </NSpace>
      </NSpace>
    </NCard>

    <NDataTable
      :columns="topicColumns"
      :data="topicData"
      size="small"
      :scroll-x="702"
      :loading="topicLoading"
      remote
      :pagination="topicPagination"
      :row-key="(item: Api.Org.Topic) => item.id"
      class="sm:h-full"
    />

    <AddTopic ref="addTopicRef" :org-id="orgId" @success="getTopicData" />
  </NSpace>
</template>

<style scoped></style>
