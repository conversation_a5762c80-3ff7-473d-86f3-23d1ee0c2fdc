<script setup lang="tsx">
import { nextTick, onMounted, ref } from 'vue';
import { NButton, NCard, NDataTable, NInput, NInputNumber, NPopconfirm, NSpace, useMessage } from 'naive-ui';
import { deleteOrgCoursePack, fetchOrgCoursePacks, updateOrgCoursePackPrice } from '@/service/api';
import { $t } from '@/locales';
import DateFormat from '@/components/common/date/date-format.vue';
import { useTable } from '@/hooks/common/table';
import AddCoursePack from './add-course-pack.vue';
import { debounce } from 'lodash-es';

const props = defineProps<{
  orgId: number;
}>();

const message = useMessage();
const addCoursePackRef = ref();
const selected_ids = ref<number[]>([]);
const updatingRows = ref<Record<string, boolean>>({});
// 用于存储每行的防抖函数
const debounceFunctions = ref<Record<string, Function>>({});

// 题库列表相关
const { columns, data, loading, pagination, getData, searchParams, updateSearchParams, resetSearchParams } = useTable<
  Api.Org.CoursePack,
  typeof fetchOrgCoursePacks,
  'index' | 'operate'
>({
  apiFn: fetchOrgCoursePacks,
  immediate: false,
  apiParams: {
    org_id: props.orgId,
    current: 1,
    size: 20,
    keywords: undefined
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};
    selected_ids.value = res.data?.selected_ids || [];
    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getCoursePackData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      width: 80
    },
    {
      key: 'course_pack_id',
      title: '课程包ID',
      align: 'center',
      width: 120,
      render: row => row.course_pack_id
    },
    {
      key: 'course_pack_name',
      title: '题库名称',
      align: 'center',
      width: 200
    },
    {
      key: 'price_original',
      title: '原价',
      align: 'center',
      width: 120,
      render: (row: Api.Org.CoursePack) => {
        // 确保每行有自己的防抖函数
        if (!debounceFunctions.value[`pack_${row.course_pack_id}`]) {
          debounceFunctions.value[`pack_${row.course_pack_id}`] = debounce(async (price: number) => {
            await handleUpdatePrice(row, price);
          }, 800);
        }

        return (
          <div>
            <NInputNumber
              defaultValue={Number(row.price_original)}
              size="small"
              min={0}
              precision={2}
              placeholder="请输入题库原价"
              loading={updatingRows.value[`${row.course_pack_id}`]}
              onUpdateValue={(value: number | null) => {
                if (value !== null) {
                  row.price_original = value;
                  // 使用该行专属的防抖函数
                  debounceFunctions.value[`pack_${row.course_pack_id}`](value);
                }
              }}
            />
          </div>
        );
      }
    },
    {
      key: 'price_sell',
      title: '售价',
      align: 'center',
      width: 120,
      render: row => row.price_sell
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 120,
      render: row => <DateFormat date={row.created_at} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 80,
      render: row => (
        <n-space justify="center">
          <NPopconfirm onPositiveClick={() => handleDeleteRow(row)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </n-space>
      )
    }
  ]
});

async function getCoursePackData() {
  updateSearchParams({
    org_id: props.orgId
  });
  await getData();
}

function handleSearch() {
  getCoursePackData();
}

function handleReset() {
  resetSearchParams();
  updateSearchParams({
    keywords: undefined
  });
  getCoursePackData();
}

async function handleDeleteRow(row: Api.Org.CoursePack) {
  const { error } = await deleteOrgCoursePack(props.orgId, row.course_pack_id);
  if (error) {
    return;
  }

  message.success($t('common.deleteSuccess'));
  await getCoursePackData();
}

function handleAdd() {
  addCoursePackRef.value.open(selected_ids.value);
}

async function handleUpdatePrice(row: Api.Org.CoursePack, price: number) {
  if (Number.isNaN(price) || price < 0) {
    message.error('价格必须是大于等于0的数字');
    return;
  }

  // 设置更新状态
  updatingRows.value = {
    ...updatingRows.value,
    [`${row.course_pack_id}`]: true
  };

  try {
    const { error } = await updateOrgCoursePackPrice(props.orgId, row.course_pack_id, price);
    if (error) {
      return;
    }

    message.success('课程包价格更新成功');
  } finally {
    // 无论成功失败都清除更新状态
    updatingRows.value = {
      ...updatingRows.value,
      [`${row.course_pack_id}`]: false
    };
  }
}

onMounted(() => {
  nextTick(() => {
    getCoursePackData();
  });
});
</script>

<template>
  <NSpace vertical>
    <NCard :bordered="false" size="small">
      <NSpace justify="space-between">
        <NButton type="success" ghost @click="handleAdd()">添加课程包资源</NButton>
        <NSpace>
          <NInput v-model:value="searchParams.keywords" placeholder="请输入题库ID/名称搜索" clearable />
          <NButton @click="handleReset">
            {{ $t('common.reset') }}
          </NButton>
          <NButton type="primary" ghost @click="handleSearch">
            {{ $t('common.search') }}
          </NButton>
        </NSpace>
      </NSpace>
    </NCard>

    <NDataTable
      :columns="columns"
      :data="data"
      size="small"
      :scroll-x="702"
      :loading="loading"
      remote
      :pagination="pagination"
      :row-key="(item: Api.Org.CoursePack) => item.id"
      class="sm:h-full"
    />

    <AddCoursePack ref="addCoursePackRef" :org-id="orgId" @success="getCoursePackData" />
  </NSpace>
</template>

<style scoped></style>
