<script setup lang="tsx">
import { nextTick, onMounted, ref, watch } from 'vue';
import { NButton, NDataTable, NDrawer, NDrawerContent, NInput, NInputNumber, NSpace, useMessage } from 'naive-ui';
import { addOrgTopics, fetchTopicList } from '@/service/api';
import { $t } from '@/locales';
import { useTable } from '@/hooks/common/table';

interface Props {
  orgId: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'success'): void;
}>();

const message = useMessage();

// 选中的题库和价格
const checkedRowKeys = ref<number[]>([]);
const priceMap = ref<Map<number, { price30: number; price60: number }>>(new Map());
const searchName = ref<string>('');

// 添加抽屉控制变量
const visible = ref(false);

// 使用 useTable hook 管理表格
const { columns, data, loading, pagination, getData, updateSearchParams, resetSearchParams } = useTable<
  Api.Train.Topic,
  typeof fetchTopicList,
  'index' | 'operate' | 'price30' | 'price60'
>({
  apiFn: fetchTopicList,
  immediate: false,
  apiParams: {
    current: 1,
    size: 10,
    name: undefined,
    keyword: undefined,
    filter_ids: []
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};
    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;
    updateSearchParams({
      current: page,
      size: pageSize
    });
    getData();
  },
  columns: () => [
    { type: 'selection' },
    {
      key: 'id',
      title: '题库ID',
      align: 'center',
      width: 100
    },
    {
      key: 'name',
      title: '题库名称',
      align: 'center',
      width: 200
    },
    {
      key: 'price30',
      title: '原价（30天）',
      align: 'center',
      width: 150,
      render: row => (
        <NSpace vertical justify="center" align="center">
          <NInputNumber
            value={priceMap.value.get(row.id)?.price30}
            min={0}
            precision={2}
            style="width: 120px"
            placeholder="请输入30天价格"
            onUpdateValue={(val: number | null) => {
              if (val !== null) {
                const currentPrices = priceMap.value.get(row.id) || { price30: 0, price60: 0 };
                priceMap.value.set(row.id, {
                  ...currentPrices,
                  price30: val
                });
              }
            }}
          />
        </NSpace>
      )
    },
    {
      key: 'price60',
      title: '原价（60天）',
      align: 'center',
      width: 150,
      render: row => (
        <NSpace vertical justify="center" align="center">
          <NInputNumber
            value={priceMap.value.get(row.id)?.price60}
            min={0}
            precision={2}
            style="width: 120px"
            placeholder="请输入60天价格"
            onUpdateValue={(val: number | null) => {
              if (val !== null) {
                const currentPrices = priceMap.value.get(row.id) || { price30: 0, price60: 0 };
                priceMap.value.set(row.id, {
                  ...currentPrices,
                  price60: val
                });
              }
            }}
          />
        </NSpace>
      )
    }
  ]
});

// 初始化价格Map
watch(
  () => data.value,
  newData => {
    newData.forEach((item: any) => {
      if (!priceMap.value.has(item.id)) {
        const price30 = Number(item.amount) || 15;
        const price60 = Number(item.amount) || 25;
        priceMap.value.set(item.id, {
          price30,
          price60: item.amount ? price30 * 2 : price60
        });
      }
    });
  },
  { immediate: true }
);

// 处理搜索
function handleSearch() {
  updateSearchParams({
    name: searchName.value
  });
  getData();
}

// 处理重置
function handleReset() {
  resetSearchParams();
  updateSearchParams({
    name: undefined
  });
  searchName.value = '';
  getData();
}

// 修改open函数
function open(ids: number[]) {
  // 设置选中的题库ID
  updateSearchParams({
    filter_ids: ids
  });

  // 获取数据
  getData();

  // 显示抽屉
  visible.value = true;
}

// 添加关闭函数
function handleClose() {
  visible.value = false;
}

// 提交添加题库
async function handleSubmit() {
  if (!checkedRowKeys.value.length) {
    message.warning('请选择要添加的题库');
    return;
  }

  loading.value = true;
  try {
    const selectedIds = [...checkedRowKeys.value];
    const topics = selectedIds.map(id => {
      const prices = priceMap.value.get(id) || { price30: 15, price60: 25 };
      return {
        id,
        price30: prices.price30,
        price60: prices.price60
      };
    });

    await addOrgTopics(props.orgId, { topics });

    message.success('添加成功');
    emit('success');
    handleClose();
    await nextTick(() => {
      checkedRowKeys.value = [];
    });
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  nextTick(() => {
    getData();
  });
});

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" :width="800">
    <NDrawerContent title="添加题库">
      <NSpace vertical size="large">
        <NSpace justify="end">
          <NInput
            v-model:value="searchName"
            placeholder="请输入题库名称搜索"
            @keyup="(e: KeyboardEvent) => e.key === 'Enter' && handleSearch()"
          />
          <NButton @click="handleReset">{{ $t('common.reset') }}</NButton>
          <NButton type="primary" ghost @click="handleSearch">
            {{ $t('common.search') }}
          </NButton>
        </NSpace>

        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="data"
          size="small"
          :scroll-x="702"
          :loading="loading"
          remote
          :pagination="pagination"
          :row-key="(item: any) => item.id"
          class="sm:h-full"
        />
      </NSpace>
      <template #footer>
        <NSpace>
          <NButton @click="handleClose">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确认添加</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
