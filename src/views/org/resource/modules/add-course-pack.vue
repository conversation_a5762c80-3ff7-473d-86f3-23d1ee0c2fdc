<script setup lang="tsx">
import { nextTick, onMounted, ref, watch } from 'vue';
import { NButton, NDataTable, NDrawer, NDrawerContent, NInput, NInputNumber, NSpace, useMessage } from 'naive-ui';
import { addOrgCoursePacks, fetchContentList } from '@/service/api';
import { $t } from '@/locales';
import { useTable } from '@/hooks/common/table';

interface Props {
  orgId: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'success'): void;
}>();

const message = useMessage();

// 选中的课程包和价格
const checkedRowKeys = ref<number[]>([]);
const priceMap = ref<Map<number, number>>(new Map());
const searchName = ref<string>('');
const classify = ref<Api.Cms.CategoryClassifyType>('course_pack');

// 添加抽屉控制变量
const visible = ref(false);

// 使用 useTable hook 管理表格
const { columns, data, loading, pagination, getData, updateSearchParams, resetSearchParams } = useTable<
  Api.Cms.Content,
  typeof fetchContentList,
  'index' | 'operate'
>({
  apiFn: fetchContentList,
  immediate: false,
  apiParams: {
    current: 1,
    size: 10,
    keyword: undefined,
    filter_ids: [],
    classify: classify.value,
    status: [2, 3],
    category_id: null
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};
    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;
    updateSearchParams({
      current: page,
      size: pageSize
    });
    getData();
  },
  columns: () => [
    { type: 'selection' },
    {
      key: 'id',
      title: '课程包ID',
      align: 'center',
      width: 100
    },
    {
      key: 'title',
      title: '课程包名称',
      align: 'center',
      width: 200
    },
    {
      key: 'charge_amount',
      title: '原价（机构最低售价）',
      align: 'center',
      width: 150,
      render: row => (
        <NSpace vertical justify="center" align="center">
          <NInputNumber
            value={priceMap.value.get(row.id)}
            min={0}
            precision={2}
            style="width: 120px"
            placeholder="请输入机构课程原价"
            onUpdateValue={(val: number | null) => {
              if (val !== null) {
                priceMap.value.set(row.id, val);
              }
            }}
          />
        </NSpace>
      )
    }
  ]
});

// 初始化价格Map
watch(
  () => data.value,
  newData => {
    newData.forEach((item: any) => {
      if (!priceMap.value.has(item.id)) {
        priceMap.value.set(item.id, Number(item.charge_amount) || 0);
      }
    });
  },
  { immediate: true }
);

// 处理搜索
function handleSearch() {
  updateSearchParams({
    name: searchName.value
  });
  getData();
}

// 处理重置
function handleReset() {
  resetSearchParams();
  updateSearchParams({
    name: undefined
  });
  searchName.value = '';
  getData();
}

// 修改open函数
function open(ids: number[]) {
  // 设置选中的题库ID
  updateSearchParams({
    filter_ids: ids
  });

  // 获取数据
  getData();

  // 显示抽屉
  visible.value = true;
}

// 添加关闭函数
function handleClose() {
  visible.value = false;
}

// 提交添加题库
async function handleSubmit() {
  if (!checkedRowKeys.value.length) {
    message.warning('请选择要添加的课程包');
    return;
  }

  loading.value = true;
  try {
    const selectedIds = [...checkedRowKeys.value];
    const course_packs = selectedIds.map(id => ({
      id,
      price: priceMap.value.get(id) || 0
    }));

    await addOrgCoursePacks(props.orgId, { course_packs });

    message.success('添加成功');
    emit('success');
    handleClose();
    await nextTick(() => {
      checkedRowKeys.value = [];
    });
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  nextTick(() => {
    getData();
  });
});

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" :width="800">
    <NDrawerContent title="添加课程包">
      <NSpace vertical size="large">
        <NSpace justify="end">
          <NInput
            v-model:value="searchName"
            placeholder="请输入课程包名称搜索"
            @keyup="(e: KeyboardEvent) => e.key === 'Enter' && handleSearch()"
          />
          <NButton @click="handleReset">{{ $t('common.reset') }}</NButton>
          <NButton type="primary" ghost @click="handleSearch">
            {{ $t('common.search') }}
          </NButton>
        </NSpace>

        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="data"
          size="small"
          :scroll-x="702"
          :loading="loading"
          remote
          :pagination="pagination"
          :row-key="(item: Api.Cms.Content) => item.id"
          class="sm:h-full"
        />
      </NSpace>
      <template #footer>
        <NSpace>
          <NButton @click="handleClose">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确认添加</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
