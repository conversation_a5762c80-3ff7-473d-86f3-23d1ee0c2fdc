<script setup lang="tsx">
import {nextTick, ref, watch} from 'vue';
import {
  NButton,
  NDataTable,
  NDrawer,
  NDrawerContent,
  NFormItemGi,
  NGrid,
  NInput,
  NInputNumber,
  NSelect,
  NSpace,
  useMessage
} from 'naive-ui';
import { addOrgCourses, fetchContentList } from '@/service/api';
import { $t } from '@/locales';
import { useTable } from '@/hooks/common/table';
import { fetchCategoryList } from '@/service/api/cms';

interface Props {
  orgId: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'success'): void;
}>();

const message = useMessage();

// 选中的课程和价格
const checkedRowKeys = ref<number[]>([]);
const priceMap = ref<Map<number, number>>(new Map());
const classify = ref<Api.Cms.CategoryClassifyType>('course');
const categoryList = ref<Api.Cms.Category[]>([]);
const keyword = ref<string>();
const category_id = ref<number | null>(null);

// 添加抽屉控制变量
const visible = ref(false);

// 使用 useTable hook 管理表格
const { columns, data, loading, pagination, getData, updateSearchParams, resetSearchParams } = useTable<
  Api.Cms.Content,
  typeof fetchContentList,
  'index' | 'operate'
>({
  apiFn: fetchContentList,
  immediate: false,
  apiParams: {
    current: 1,
    size: 10,
    keyword: undefined,
    filter_ids: [],
    classify: classify.value,
    status: [2, 3],
    category_id: null
  },
  transformer: res => {
    const { records = [], current = 1, size = 10, total = 0 } = res.data || {};

    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateSearchParams({
      current: page,
      size: pageSize
    });

    getData();
  },
  columns: () => [
    { type: 'selection' },
    {
      key: 'id',
      title: '课程ID',
      align: 'center',
      width: 100
    },
    {
      key: 'title',
      title: '课程名称',
      align: 'center',
      width: 200
    },
    {
      key: 'charge_amount',
      title: '原价（机构最低售价）',
      align: 'center',
      width: 150,
      render: row => (
        <NSpace vertical justify="center" align="center">
          <NInputNumber
            value={priceMap.value.get(row.id)}
            min={0}
            precision={2}
            style="width: 120px"
            placeholder="请输入机构课程原价"
            onUpdateValue={(val: number | null) => {
              if (val !== null) {
                priceMap.value.set(row.id, val);
              }
            }}
          />
        </NSpace>
      )
    }
  ]
});

// 初始化价格Map
watch(
  () => data.value,
  newData => {
    newData.forEach(item => {
      if (!priceMap.value.has(item.id)) {
        priceMap.value.set(item.id, Number(item.charge_amount) || 0);
      }
    });
  },
  { immediate: true }
);

// 处理搜索
function handleSearch() {
  updateSearchParams({
    keyword: keyword.value,
    category_id: category_id.value
  });
  getData();
}

// 处理重置
function handleReset() {
  resetSearchParams();
  updateSearchParams({
    keyword: undefined,
    category_id: null
  });
  keyword.value = '';
  category_id.value = null;
  getData();
}

async function getCategoryList() {
  const { data: category, error } = await fetchCategoryList({ classify: classify.value });

  if (!error) {
    categoryList.value = category;
  }
}

// 修改open函数
function open(ids: number[]) {
  updateSearchParams({
    filter_ids: ids
  });

  getData();
  getCategoryList();

  visible.value = true;
}

// 添加关闭函数
function handleClose() {
  visible.value = false;
}

// 提交添加课程
async function handleSubmit() {
  if (!checkedRowKeys.value.length) {
    message.warning('请选择要添加的课程');
    return;
  }

  loading.value = true;
  try {
    const selectedIds = [...checkedRowKeys.value];
    const courses = selectedIds.map(id => ({
      id,
      price: priceMap.value.get(id) || 0
    }));

    await addOrgCourses(props.orgId, { courses });

    message.success('添加成功');
    emit('success');
    handleClose();
    await nextTick(() => {
      checkedRowKeys.value = [];
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" :width="800">
    <NDrawerContent title="添加课程">
      <NSpace vertical size="large">
        <NGrid :cols="24" :x-gap="12">
          <NFormItemGi label="课程分类" :span="9">
            <NSelect
              v-model:value="category_id"
              :options="categoryList.map(item => ({ label: item.name, value: item.id }))"
              placeholder="请选择课程分类"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="课程名称" :span="9">
            <NInput
              v-model:value="keyword"
              placeholder="请输入课程名称搜索"
              clearable
              @keyup="(e: KeyboardEvent) => e.key === 'Enter' && handleSearch()"
            />
          </NFormItemGi>
          <NFormItemGi :span="6">
            <NSpace justify="end">
              <NButton @click="handleReset">{{ $t('common.reset') }}</NButton>
              <NButton type="primary" ghost @click="handleSearch">
                {{ $t('common.search') }}
              </NButton>
            </NSpace>
          </NFormItemGi>
        </NGrid>

        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="data"
          size="small"
          :scroll-x="702"
          :loading="loading"
          remote
          :pagination="pagination"
          :row-key="(item: Api.Cms.Content) => item.id"
          class="sm:h-full"
        />
      </NSpace>
      <template #footer>
        <NSpace>
          <NButton @click="handleClose">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确认添加</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
