<script setup lang="tsx">
import { nextTick, onMounted, ref } from 'vue';
import { NButton, NCard, NDataTable, NInput, NInputNumber, NPopconfirm, NSpace, useMessage } from 'naive-ui';
import { deleteOrgCourse, fetchOrgCourses, updateOrgCoursePrice } from '@/service/api';
import { $t } from '@/locales';
import DateFormat from '@/components/common/date/date-format.vue';
import { useTable } from '@/hooks/common/table';
import AddCourse from './add-course.vue';
import { debounce } from 'lodash-es';

const props = defineProps<{
  orgId: number;
}>();

const message = useMessage();
const addCourseRef = ref();
const selected_course_ids = ref<number[]>([]);
const updatingRows = ref<Record<string, boolean>>({});
// 用于存储每行的防抖函数
const debounceFunctions = ref<Record<string, Function>>({});

// 课程列表相关
const {
  columns: courseColumns,
  data: courseData,
  loading: courseLoading,
  pagination: coursePagination,
  getData: getTableData,
  searchParams: courseSearchParams,
  updateSearchParams: updateCourseSearchParams,
  resetSearchParams: resetCourseSearchParams
} = useTable<Api.Org.Course, typeof fetchOrgCourses, 'index' | 'operate'>({
  apiFn: fetchOrgCourses,
  immediate: false,
  apiParams: {
    org_id: props.orgId,
    current: 1,
    size: 20,
    keywords: undefined
  },
  transformer: res => {
    const { records = [], current = 1, size = 20, total = 0 } = res.data || {};
    selected_course_ids.value = res.data?.selected_course_ids || [];
    return {
      data: records,
      pageNum: current,
      pageSize: size,
      total
    };
  },
  onPaginationChanged(pg) {
    const { page, pageSize } = pg;

    updateCourseSearchParams({
      current: page,
      size: pageSize
    });

    getCourseData();
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      render: row => row.id,
      align: 'center',
      width: 80
    },
    {
      key: 'course_id',
      title: '课程ID',
      align: 'center',
      width: 120,
      render: row => row.course_id
    },
    {
      key: 'course_name',
      title: '课程名称',
      align: 'center',
      width: 200
    },
    {
      key: 'hour',
      title: '课时',
      align: 'center',
      width: 120,
      render: row => row.hour
    },
    {
      key: 'price_original',
      title: '原价',
      align: 'center',
      width: 120,
      render: (row: Api.Org.Course) => {
        // 确保每行有自己的防抖函数
        if (!debounceFunctions.value[`course_${row.course_id}`]) {
          debounceFunctions.value[`course_${row.course_id}`] = debounce(async (price: number) => {
            await handleUpdateCoursePrice(row, price);
          }, 800);
        }

        return (
          <div>
            <NInputNumber
              defaultValue={Number(row.price_original)}
              size="small"
              min={0}
              precision={2}
              placeholder="请输入课程原价"
              loading={updatingRows.value[`${row.course_id}`]}
              onUpdateValue={(value: number | null) => {
                if (value !== null) {
                  row.price_original = value;
                  // 使用该行专属的防抖函数
                  debounceFunctions.value[`course_${row.course_id}`](value);
                }
              }}
            />
          </div>
        );
      }
    },
    {
      key: 'price_sell',
      title: '售价',
      align: 'center',
      width: 120,
      render: row => row.price_sell
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 120,
      render: row => <DateFormat date={row.created_at} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 80,
      render: row => (
        <n-space justify="center">
          <NPopconfirm onPositiveClick={() => handleDeleteCourse(row)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </n-space>
      )
    }
  ]
});

async function getCourseData() {
  updateCourseSearchParams({
    org_id: props.orgId
  });
  await getTableData();
}

function handleSearch() {
  getCourseData();
}

function handleReset() {
  resetCourseSearchParams();
  updateCourseSearchParams({
    keywords: undefined
  });
  getCourseData();
}

async function handleDeleteCourse(row: Api.Org.Course) {
  const { error } = await deleteOrgCourse(props.orgId, row.course_id);
  if (error) {
    return;
  }

  message.success($t('common.deleteSuccess'));
  await getCourseData();
}

function handleAddCourse() {
  addCourseRef.value.open(selected_course_ids.value);
}

async function handleUpdateCoursePrice(row: Api.Org.Course, price: number) {
  if (Number.isNaN(price) || price < 0) {
    message.error('价格必须是大于等于0的数字');
    return;
  }

  // 设置更新状态
  updatingRows.value = {
    ...updatingRows.value,
    [`${row.course_id}`]: true
  };

  try {
    const { error } = await updateOrgCoursePrice(props.orgId, row.course_id, price);
    if (error) {
      return;
    }

    message.success('课程价格更新成功');
  } finally {
    // 无论成功失败都清除更新状态
    updatingRows.value = {
      ...updatingRows.value,
      [`${row.course_id}`]: false
    };
  }
}

onMounted(() => {
  nextTick(() => {
    getCourseData();
  });
});
</script>

<template>
  <NSpace vertical>
    <NCard :bordered="false" size="small">
      <NSpace justify="space-between">
        <NButton type="success" ghost @click="handleAddCourse()">添加课程资源</NButton>
        <NSpace>
          <NInput v-model:value="courseSearchParams.keywords" placeholder="请输入课程ID/名称搜索" clearable />
          <NButton @click="handleReset">
            {{ $t('common.reset') }}
          </NButton>
          <NButton type="primary" ghost @click="handleSearch">
            {{ $t('common.search') }}
          </NButton>
        </NSpace>
      </NSpace>
    </NCard>

    <NDataTable
      :columns="courseColumns"
      :data="courseData"
      size="small"
      :scroll-x="702"
      :loading="courseLoading"
      remote
      :pagination="coursePagination"
      :row-key="(item: Api.Org.Course) => item.id"
      class="sm:h-full"
    />

    <AddCourse ref="addCourseRef" :org-id="orgId" @success="getCourseData" />
  </NSpace>
</template>

<style scoped></style>
