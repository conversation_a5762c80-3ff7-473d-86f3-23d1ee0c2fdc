/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
      /** created_at */
      created_at: string[];
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T extends NonNullable<unknown>> extends PaginatingCommonParams {
      records: T[];
    }

    /**
     * enable status
     *
     * - 0: disabled
     * - 1: enabled
     */
    type EnableStatus = number;

    /** common record */
    type CommonRecord<T extends NonNullable<unknown>> = {
      /** record id */
      id: number;
      /** record creator */
      created_at: string;
      /** record updater */
      updated_at: string;
      deleted_at?: string;
      /** record update time */
      status: number;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
      refreshToken: string;
    }

    interface UserInfo {
      userId: string;
      userName: string;
      roles: string[];
      permissions: {
        cms: boolean;
        ers_order: boolean;
      };
      menus: Api.SystemManage.Menu[];
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  /**
   * namespace SystemManage
   *
   * backend api module: "systemManage"
   */
  namespace SystemManage {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size' | 'created_at'>;

    /** role */
    type Role = Common.CommonRecord<{
      /** role name */
      name: string;
      /** role code */
      code: string;
      /** role description */
      desc: string;
      system: number;
      permissions: string[];
      menus?: RoleMenu[];
    }>;

    type RoleMenu = {
      id: number;
      role_id: number;
      menu_id: number;
      checked: number;
      created_at: string;
      menu?: Menu;
    };

    /** role search params */
    type RoleSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.Role, 'code' | 'status'> & CommonSearchParams
    >;

    /** role list */
    type RoleList = Common.PaginatingQueryRecord<Role>;

    /** all role */
    type AllRole = Pick<Role, 'id' | 'name' | 'code'>;

    /**
     * user gender
     *
     * - "1": "male"
     * - "2": "female"
     */
    type UserGender = '1' | '2';

    /** user */
    type User = Common.CommonRecord<{
      /** user name */
      userName: string;
      /** user gender */
      userGender: UserGender | null;
      /** user nick name */
      nickName: string;
      /** user phone */
      userPhone: string;
      /** user email */
      userEmail: string;
      /** user role code collection */
      userRoles: string[];
    }>;

    /** user search params */
    type UserSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.User, 'userName' | 'userGender' | 'nickName' | 'userPhone' | 'userEmail' | 'status'> &
        CommonSearchParams
    >;

    /** user list */
    type UserList = Common.PaginatingQueryRecord<User>;

    /**
     * menu type
     *
     * - "1": menu
     * - "2": event
     */
    type MenuType = 0 | 1;

    type Menu = Common.CommonRecord<{
      /** parent menu id */
      parent_id: number;
      /** menu type */
      type: MenuType;
      /** menu name */
      name: string;
      /** route name */
      route_name: string;
      /** route path */
      route_path: string;
      /** component */
      component?: string;
      /** iconify icon name or local icon name */
      icon: string;
      /** icon type */
      method: string;
      /** menu order */
      sort: number;
      /** whether to hide the route in the menu */
      status: number;
      /** children menu */
      children?: Menu[];
    }>;

    type Admin = Common.CommonRecord<{
      username: string;
      real_name: string;
      phone: string;
      email: string;
      is_admin: number;
      last_logged_at: string;
      last_logged_ip: string;
      last_active_at: string;
      last_active_ip: string;
      roles?: AdminRole[];
      stats?: {
        admin_id: number;
        consume_credit: number;
        consume_credit_content: number;
        consume_credit_special: number;
        payment_amount: string;
        content_material: number;
        content_course: number;
        content_special: number;
      }
    }>;

    type AdminRole = {
      id: number;
      admin_id: number;
      role_id: number;
      created_at: string;
      role?: Role;
    };
  }

  namespace User {
    type User = Common.CommonRecord<{
      uuid: string;
      nickname: string;
      avatar: string;
      phone: string;
      org_id: number;
      credit: number;
      balance: string;
      last_logged_at: string;
      last_logged_ip: string;
      last_active_at: string;
      last_active_ip: string;
      org?: Org.Org;
      students?: Students[];
    }>;

    type Students = Common.CommonRecord<{
      org?: Org.Org;
    }>;

    type Property = {
      user_id: number;
      tips: any;
      current_topic_id: number;
      info_edited_at: string | null;
      created_at: string;
      updated_at: string;
      user?: User;
    };

    type CreditLog = {
      id: number;
      user_id: number;
      origin_credit: number;
      change_credit: number;
      real_change_credit: number;
      type: string;
      business_type: string;
      business_id: number;
      remark: string;
      created_at: string;
      updated_at: string;
      user?: User;
    };

    type BalanceRecord = {
      id: number;
      user_id: number;
      type: string;
      origin_balance: number;
      amount: number;
      business_type: string;
      business_id: number;
      remark: string;
      created_at: string;
      updated_at: string;
      user?: User;
    };

    type SmsRecord = {
      phone: string;
      type: string;
      content: string;
      created_at: string;
    };

    type Bind = {
      id: number;
      user_id: number;
      platform: string;
      open_id: string;
      union_id: string;
      access_token: string;
      nickname: string;
      avatar: string;
      last_logged_at: string;
      created_at: string;
      updated_at: string;
      user?: User;
    };

    type Favorite = {
      id: number;
      user_id: number;
      business_type: string;
      business_id: number;
      created_at: string;
      user?: User;
    };

    type Attitude = {
      id: number;
      user_id: number;
      business_type: string;
      business_id: number;
      attitude: number;
      created_at: string;
      user?: User;
    };

    type ContentDownload = {
      id: number;
      user_id: number;
      content_id: number;
      created_at: string;
      user?: User;
      content?: Api.Cms.Content;
    };

    type OwnContent = {
      id: number;
      user_id: number;
      content_id: number;
      classify: string;
      status: number;
      created_at: string;
      expired_at: string;
      user?: User;
      content?: Cms.Content;
    };

    type OwnTopic = {
      id: number;
      user_id: number;
      topic_id: number;
      status: number;
      created_at: string;
      expired_at: string;
      user?: User;
      topic?: Train.Topic;
    };

    type Visitor = Common.CommonRecord<{
      user_id: number;
      token: string;
      last_active_at: string;
      user?: User;
    }>;

    type Invitation = Common.CommonRecord<{
      referral_id: number;
      invitee_id: number;
      credit: number;
      send_at: string;
      referral?: User;
      invitee?: User;
    }>;

    type OpenCourseRecord = {
      id: number;
      batch_id: number;
      user_id: number;
      org_id: number;
      phone: string;
      course_ids: string[];
      topic_ids: string[];
      packs: string[];
      courses: object[];
      topics: object[];
      created_at: string;
      user?: User;
      org?: Org.Org;
    };

    type OpenCourseBatch = {
      id: number;
      count: number;
      admin_id: number;
      created_at: string;
      admin?: SystemManage.Admin;
    };
  }

  namespace Order {
    type Order = Common.CommonRecord<{
      user_id: number;
      order_no: string;
      total_amount: string;
      payment_amount: string;
      title: string;
      business_type: string;
      business_id: number;
      payment_id: number;
      payment_at: number;
      payment?: Payment;
      user?: Api.User.User;
    }>;

    type Payment = Common.CommonRecord<{
      user_id: number;
      order_id: number;
      out_trade_no: string;
      transaction_no: string;
      platform: string;
      client: string;
      amount: string;
      payment_at: number;
    }>;

    type Refund = Common.CommonRecord<{
      user_id: number;
      order_id: number;
      payment_id: number;
      refund_no: string;
      refund_amount: string;
      remark: string;
      finished_at: string;
    }>;
  }

  namespace Cms {
    /** 分类 */
    type CategoryClassifyType = 'material' | 'course' | 'course_pack' | 'news';

    type Category = Common.CommonRecord<{
      pid: number;
      name: string;
      intro: string;
      logo: string;
      logo_src: string;
      path: string;
      classify: CategoryClassifyType;
      visible: number;
      allow_types: number[];
      sort: number;
      hot_position: number;
      cascades_value?: number[];
      cascades_label?: string[];
      level?: number;
      children?: Category[];
    }>;

    /** 内容类型 */
    type ContentType = 1 | 2 | 3 | 4 | 5;

    type Content = Common.CommonRecord<{
      id: number;
      title: string;
      charge_amount: string;
      status: number;
      resource: any;
    }>;

    interface ContentListData extends Common.PaginatingCommonParams {
      records: Content[];
    }

    type ContentVideo = {
      content_id: number;
      filepath: string;
      filepath_src: string;
      filesize: string;
      duration: number;
      extend: any;
      created_at: string;
      updated_at: string;
    };

    type ContentDoc = {
      content_id: number;
      format: string;
      page_count: number;
      filesize: number;
      filepath: string;
      filename: string;
      filepath_src: string;
      download_count: number;
      download_count_add: number;
      preview_images: string[];
      preview_images_src: string[];
      content: string;
      created_at: string;
      updated_at: string;
    };

    type ContentRichText = {
      content_id: number;
      content: string;
      created_at: string;
      updated_at: string;
    };

    type ContentCourse = {
      id: number;
      content_id: number;
      teacher_name: string;
      learning_count: number;
      learning_count_add: number;
      sections_count: number;
      try_view_count: number;
      topic_id: number;
      hour_per_minutes: number;
      created_at: string;
      updated_at: string;
      buy_course_users?: number;
      chapters?: ContentCourseChapter[];
      topic?: Train.Topic;
    };

    type ContentCourseChapter = Common.CommonRecord<{
      content_id: number;
      name: string;
      sections_count: number;
      sort: number;
      sections?: ContentCourseSection[];
    }>;

    type ContentCourseSection = Common.CommonRecord<{
      content_id: number;
      chapter_id: number;
      ref_video_id: number;
      name: string;
      filepath: string;
      filepath_src: string;
      filesize: number;
      extend: any;
      play_count: number;
      play_complete_count: number;
      duration: number;
      sort: number;
      video?: ContentVideo;
    }>;

    type ContentCourseChapterSection = ContentCourseSection & {
      type: string;
      name_desc: string;
      index: string;
      hour: string;
      duration: string;
      children?: ContentCourseChapterSection[];
    };

    type ContentCourseDoc = Common.CommonRecord<{
      id: number;
      content_id: number;
      filename: string;
      filepath: string;
      filepath_src: string;
      sort: number;
      download_count: number;
    }>;

    type ContentCourseProgress = Common.CommonRecord<{
      user_id: number;
      content_id: number;
      chapter_id: number;
      section_id: number;
      duration: number;
      sort: number;
      pos: number;
      finished: number;
    }>;

    type ContentCoursePack = {
      content_id: number;
      topic_id: number;
      created_at: string;
      updated_at: string;
      courses?: ContentCourse[];
      topic?: Train.Topic;
    };

    type ContentCoursePackList = {
      id: number;
      content_id: number;
      course_id: number;
      order: number;
      created_at: string;
      updated_at: string;
      content?: Content;
      course?: ContentCourse;
    };

    type ContentRelation = {
      id: number;
      content_id: number;
      related_id: number;
      created_at: string;
    };

    type Special = Common.CommonRecord<{
      name: string;
      intro: string;
      cover: string;
      cover_src: string;
      charge_credit: number;
      recommend_at: string;
      admin_id: number;
      contents?: SpecialContent[];
      admin?: Api.SystemManage.Admin;
    }>;

    type SpecialContent = {
      id: number;
      special_id: number;
      content_id: number;
      created_at: string;
      content?: Content;
    };

    type CourseStatistic = {
      buy_count: number;
      studying_count: number;
      study_finish_count: number;
    };

    type CourseProgress = {
      phone: number;
      nickname: string;
      org_name?: string;
      progress_status: number;
      progress: string;
      created_at: string;
      user?: User.User;
    };
  }

  namespace Train {
    type TopicExamConfigType = 'single_choice' | 'multiple_choice' | 'judge';

    type TopicExamConfig = Record<TopicExamConfigType, {
      count: number;
      score: number;
    }>;

    type TopicSubjectTypeCount = Record<TopicExamConfigType, number>;

    type Topic = Common.CommonRecord<{
      course_category_id: number;
      course_content_id: number;
      name: string;
      amount: number;
      exam_time: number;
      pass_score: number;
      exam_config: Api.Train.TopicExamConfig | null;
      next_exam_at: string;
      sort: number;
      subjects_count?: number;
    }>;

    type Chapter = Common.CommonRecord<{
      topic_id: number;
      name: string;
      example: number;
      sections_count?: number;
      topic?: Topic;
      sections?: Section[];
    }>;

    type Section = Common.CommonRecord<{
      topic_id: number;
      chapter_id: number;
      name: string;
      subjects_count?: number;
      chapter?: Chapter;
      example?: Example;
    }>;

    type Example = Common.CommonRecord<{
      topic_id: number;
      chapter_id: number;
      section_id: number;
      content: string;
    }>;

    type Subject = Common.CommonRecord<{
      topic_id: number;
      chapter_id: number;
      section_id: number;
      intro: string;
      image: string;
      type: number;
      judge_correct: number;
      answer: string;
      analysis: string;
      deleted_at: string;
      topic?: Topic;
      options?: SubjectOption[];
      attachments?: Api.System.AttachmentRelation[];
    }>;

    type SubjectOption = Common.CommonRecord<{
      subject_id: number;
      sn: number;
      name: string;
      image: string;
      is_correct: number;
    }>;

    type Test = Common.CommonRecord<{
      user_id: number;
      topic_id: number;
      current_subject_id: number;
      type: number;
      end_at: string;
      subject_count: number;
      subject_completed_count: number;
      subject_correct_count: number;
      score: number;
    }>;

    type TestSubject = Common.CommonRecord<{
      user_id: number;
      topic_id: number;
      test_id: number;
      subject_id: number;
      option_id: number;
      correct: number;
      wrong_removed: number;
    }>;
  }

  namespace Punish {
    type Topic = Common.CommonRecord<{
      name: string;
      sort: number;
    }>;

    type Subject = Common.CommonRecord<{
      topic_id: number;
      intro: string;
      statute: string;
      punishment: string;
      remark: string;
      topic?: Topic;
      options?: SubjectOption[];
    }>;

    type SubjectOption = Common.CommonRecord<{
      subject_id: number;
      name: string;
      level: string;
      standard: string;
      amount: number;
    }>;

    type Test = Common.CommonRecord<{
      user_id: number;
      topic_id: number;
      amount_tol: number;
    }>;

    type TestSubject = Common.CommonRecord<{
      user_id: number;
      topic_id: number;
      test_id: number;
      subject_id: number;
      option_id: number;
      subject?: Subject;
      option?: SubjectOption;
    }>;
  }

  namespace Qa {
    type Question = Common.CommonRecord<{
      user_id: number;
      title: string;
      content: string;
      anonymous: number;
      answer_count: number;
      recommend_at: string;
      user?: Api.User.User;
    }>;

    type Answer = Common.CommonRecord<{
      user_id: number;
      question_id: number;
      content: string;
      anonymous: number;
      like_count: number;
      dislike_count: number;
      user?: Api.User.User;
      question?: Question;
    }>;
  }

  namespace Chat {
    type Session = Common.CommonRecord<{
      user_id: number;
      title: string;
      model: string;
      messages_count?: number;
      user?: Api.User.User;
    }>;

    type Message = Common.CommonRecord<{
      user_id: number;
      session_id: number;
      prompt: string;
      completion: string;
      like: number;
      Session?: Session;
    }>;
  }

  namespace Ers {
    // 表单类型
    type FormType = 'group' | 'text' | 'textarea' | 'image' | 'file' | 'select' | 'checkbox';

    // 行业类别
    type Industry = Common.CommonRecord<{
      name: string;
      sort: number;
    }>;

    // 企业类别
    type Enterprise = Common.CommonRecord<{
      name: string;
      sort: number;
    }>;

    type Flow = Common.CommonRecord<{
      name: string;
      steps?: FlowStep[];
    }>;

    type FlowStep = Common.CommonRecord<{
      flow_id: number;
      name: string;
      module: string;
      step: number;
    }>;

    type Project = Common.CommonRecord<{
      title: string;
      sort: number;
      icon: string;
      icon_src: string;
      intro: string;
      status: number;
      is_bind_category: boolean;
      flow_id: number;
      flow?: Flow;
    }>;

    type ProjectForm = Common.CommonRecord<{
      project_id: number;
      flow_id: number;
      step_id: number;
      industry_id: number;
      enterprise_id: number;
      inputs?: ProjectInput[]
    }>;

    type ProjectInput = Common.CommonRecord<{
      project_id: number;
      flow_id: number;
      step_id: number;
      project_form_id: number;
      form_library_id: number;
      title: string;
      type: FormType;
      is_required: boolean;
      desc: string;
      options: any;
      sort: number;
    }>;

    // 服务工单
    type ServiceOrder = Common.CommonRecord<{
      sid: string;
      user_id: number;
      project_id: number;
      flow_id: number;
      industry_id: number;
      enterprise_id: number;
      status: number;
      admin_id: number;
      finished_at: string;
      user?: Api.User.User;
      project?: Project;
      industry?: Industry;
      enterprise?: Enterprise;
      operator?: SystemManage.Admin;
      flows?: object[];
    }>;

    // 基础组件
    type FormLibrary = Common.CommonRecord<{
      title: string;
      type: FormType;
      is_required: boolean;
      desc: string;
      options: {
        type?: 'text' | 'number' | 'tel';
        max?: number;
        min?: number;
        limit?: number;
        ext?: string[];
        size?: number;
      } | null;
    }>;
  }

  namespace Expert {
    type Expert = Common.CommonRecord<{
      user_id: number;
      name: string;
      gender: number;
      phone: string;
      photo: string;
      major: string;
      residence: string;
      photo_url?: string;
      education: string;
      occupation: string;
      industry: string;
      work_year: number;
      fields: string[];
      services: string[];
      certs: object[];
      scene_photos: object[];
      course_scopes: string;
      typical_cases: string;
      serve_customers: string;
      teaching_styles: string;
      remark: string;
      reason: string;
      status: number;
      is_visible: number;
      sort: number;
      pass_at: string;
      safety_work_experience: string;
      user?: Api.User.User;
    }>;

    type SelectOptions = {
      educations: string[];
      services: string[];
      fields: string[];
    }
  }

  namespace Stat {
    type Permission = {
      permission: 'root' | 'guest';
    };

    type Statistic = {
      user_register: number;
      user_active: number;
      payment_order: number;
      payment_amount: string;
      payment_user: number;
      payment_credit_order: number;
      payment_credit_amount: string;
      payment_credit_user: number;
      payment_course_order: number;
      payment_course_amount: string;
      payment_course_user: number;
      payment_topic_order: number;
      payment_topic_amount: string;
      payment_topic_user: number;
      consume_credit: number;
      content: number;
      content_material: number;
      content_course: number;
      content_news: number;
      content_view: number;
      content_download: number;
      content_special: number;
      platform_content: number;
      platform_content_material: number;
      platform_content_course: number;
      platform_content_news: number;
      practise: number;
      question: number;
      answer: number;
      search: number;
      collect: number;
    };

    /** 排行分类 */
    type RankType = 'download' | 'search';

    type Search = {
      id: number;
      date: string;
      rank: number;
      keyword: string;
      search_count: number;
    };
    interface SearchView extends Search {
      rank: number;
      keyword: string;
      search_count: number;
    }

    interface DailyOverview extends Statistic {
      id: number;
      date: string;
      created_at: string;
      updated_at: string;
    }

    interface SubtotalOverview {
      subtotal: Statistic;
      today: Statistic;
      yesterday: Statistic;
      attachment: {
        num: number;
        size: number;
      };
    }

    interface DailyPromoter extends Statistic {
      id: number;
      admin_id: number;
      date: string;
      created_at: string;
      updated_at: string;
    }

    interface SubtotalPromoter {
      subtotal: Statistic;
      today: Statistic;
      yesterday: Statistic;
      attachment: {
        num: number;
        size: number;
      };
    }
  }

  namespace System {
    type AsyncTask = {
      id: number;
      type: number;
      task_id: string;
    };

    type SettingBoothType = 0 | 1;

    type SettingBooth = {
      id: number;
      type: SettingBoothType;
      name: string;
      image: string;
      image_url: string;
      sort: number;
      url: string;
      enable: number;
      created_at: string;
      updated_at: string;
    };

    type UploadStorage = 'pub' | 'priv' | 'local';

    type UploadConfig = {
      max_size_kb: number;
      method: string;
      name: string;
      url: string;
      form_params: {
        token: string;
      };
      allow_mime_types: string | string[];
    };

    type UploaderFile = {
      filename: string;
      key: string;
      mime: string;
      size: number;
      url: string;
      created_at?: string;
    };

    type AttachmentFile = Common.CommonRecord<{
      path: string;
      filename: string;
      mime: string;
      etag: string;
      filesize: number;
      width: number;
      height: number;
      relations_count?: number;
    }>;

    type AttachmentRelation = Common.CommonRecord<{
      file_id: number;
      target_id: number;
      target_type: string;
      file: AttachmentFile;
    }>;
  }

  namespace Search {
    type Stat = {
      id: number;
      date: string;
      keyword: string;
      search_count: number;
    };
  }

  namespace Org {
    type Org = {
      id: number;
      name: string;
      alias: string;
      need_photo: number;
      enable_enroll: number;
      contact: string;
      area_code: string;
      area_text: string[];
      balance: number;
      total_students: number;
      total_trained: number;
      total_classes: number;
      merchant_id?: string;
      merchant_name?: string;
      merchant_enable?: number;
      main_admin?: Admin;
      created_at?: string;
      updated_at?: string;
    };

    type Admin = {
      id: number;
      username: string;
      last_logged_at?: string;
      created_at?: string;
      updated_at?: string;
    };

    type Course = {
      id: number;
      course_id: number;
      course_name: string;
      hour: number;
      price_original: number;
      price_sell: number;
      created_at?: string;
    };

    interface CourseListData extends Common.PaginatingCommonParams {
      selected_course_ids: number[];
      records: Course[];
    }

    type Topic = {
      id: number;
      topic_id: number;
      topic_name: string;
      price_original_30: number;
      price_sell_30: number;
      price_original_60: number;
      price_sell_60: number;
      price_sell: number;
      created_at?: string;
    };

    interface TopicListData extends Common.PaginatingCommonParams {
      selected_topic_ids: number[];
      records: Topic[];
    }

    type CoursePack = {
      id: number;
      course_pack_id: number;
      course_pack_name: string;
      price_original: number;
      price_sell: number;
      created_at?: string;
    };

    interface CoursePackListData extends Common.PaginatingCommonParams {
      selected_ids: number[];
      records: CoursePack[];
    }

    type BalanceRecord = {
      id: number;
      org_id: number; // 机构 ID
      enroll_id: number; // 关联报名 ID
      ref_id: number; // 关联记录 ID
      amount: number; // 变动金额
      origin_balance: number; // 原始余额
      type: 'income' | 'expense' | 'refund'; // 类型
      status: number; // 状态
      remark: string; // 备注
      created_at: string; // 创建时间
      updated_at: string; // 更新时间
    };
  }
}
