/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

declare module "@elegant-router/types" {
  type ElegantConstRoute = import('@elegant-router/vue').ElegantConstRoute;

  /**
   * route layout
   */
  export type RouteLayout = "base" | "blank";

  /**
   * route map
   */
  export type RouteMap = {
    "root": "/";
    "not-found": "/:pathMatch(.*)*";
    "exception": "/exception";
    "exception_403": "/exception/403";
    "exception_404": "/exception/404";
    "exception_500": "/exception/500";
    "403": "/403";
    "404": "/404";
    "500": "/500";
    "cms": "/cms";
    "cms_category": "/cms/category";
    "cms_content": "/cms/content";
    "cms_content-draft": "/cms/content-draft";
    "cms_content-recycle": "/cms/content-recycle";
    "cms_course": "/cms/course";
    "cms_course-pack": "/cms/course-pack";
    "cms_course-statistic": "/cms/course-statistic";
    "cms_material": "/cms/material";
    "cms_news": "/cms/news";
    "cms_special": "/cms/special";
    "ers": "/ers";
    "ers_category": "/ers/category";
    "ers_clue": "/ers/clue";
    "ers_flow": "/ers/flow";
    "ers_form-library": "/ers/form-library";
    "ers_order": "/ers/order";
    "ers_order-detail": "/ers/order-detail";
    "ers_project": "/ers/project";
    "ers_project-setting": "/ers/project-setting";
    "expert": "/expert";
    "expert_index": "/expert/index";
    "home": "/home";
    "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?";
    "manage": "/manage";
    "manage_admin": "/manage/admin";
    "manage_attachment": "/manage/attachment";
    "manage_menu": "/manage/menu";
    "manage_role": "/manage/role";
    "manage_user": "/manage/user";
    "manage_user-detail": "/manage/user-detail/:id";
    "operation": "/operation";
    "operation_banner": "/operation/banner";
    "order": "/order";
    "order_index": "/order/index";
    "order_payment": "/order/payment";
    "order_refund": "/order/refund";
    "org": "/org";
    "org_index": "/org/index";
    "org_resource": "/org/resource";
    "punish": "/punish";
    "punish_test": "/punish/test";
    "qa": "/qa";
    "qa_answer": "/qa/answer";
    "qa_chat": "/qa/chat";
    "qa_question": "/qa/question";
    "stat": "/stat";
    "stat_content-rank": "/stat/content-rank";
    "stat_index": "/stat/index";
    "stat_promoter": "/stat/promoter";
    "train": "/train";
    "train_chapter": "/train/chapter";
    "train_subject": "/train/subject";
    "train_subject-edit": "/train/subject-edit";
    "train_topic": "/train/topic";
    "user": "/user";
    "user_balance-records": "/user/balance-records";
    "user_credit-log": "/user/credit-log";
    "user_detail": "/user/detail";
    "user_index": "/user/index";
    "user_invitation": "/user/invitation";
    "user_open-course-batches": "/user/open-course-batches";
    "user_open-course-records": "/user/open-course-records";
    "user_sms-records": "/user/sms-records";
  };

  /**
   * route key
   */
  export type RouteKey = keyof RouteMap;

  /**
   * route path
   */
  export type RoutePath = RouteMap[RouteKey];

  /**
   * custom route key
   */ 
  export type CustomRouteKey = Extract<
    RouteKey,
    | "root"
    | "not-found"
    | "exception"
    | "exception_403"
    | "exception_404"
    | "exception_500"
  >;

  /**
   * the generated route key
   */ 
  export type GeneratedRouteKey = Exclude<RouteKey, CustomRouteKey>;

  /**
   * the first level route key, which contain the layout of the route
   */
  export type FirstLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "cms"
    | "ers"
    | "expert"
    | "home"
    | "login"
    | "manage"
    | "operation"
    | "order"
    | "org"
    | "punish"
    | "qa"
    | "stat"
    | "train"
    | "user"
  >;

  /**
   * the custom first level route key
   */
  export type CustomFirstLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
    | "exception"
  >;

  /**
   * the last level route key, which has the page file
   */
  export type LastLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "login"
    | "cms_category"
    | "cms_content-draft"
    | "cms_content-recycle"
    | "cms_content"
    | "cms_course-pack"
    | "cms_course-statistic"
    | "cms_course"
    | "cms_material"
    | "cms_news"
    | "cms_special"
    | "ers_category"
    | "ers_clue"
    | "ers_flow"
    | "ers_form-library"
    | "ers_order-detail"
    | "ers_order"
    | "ers_project-setting"
    | "ers_project"
    | "expert_index"
    | "home"
    | "manage_admin"
    | "manage_attachment"
    | "manage_menu"
    | "manage_role"
    | "manage_user-detail"
    | "manage_user"
    | "operation_banner"
    | "order_index"
    | "order_payment"
    | "order_refund"
    | "org_index"
    | "org_resource"
    | "punish_test"
    | "qa_answer"
    | "qa_chat"
    | "qa_question"
    | "stat_content-rank"
    | "stat_index"
    | "stat_promoter"
    | "train_chapter"
    | "train_subject-edit"
    | "train_subject"
    | "train_topic"
    | "user_balance-records"
    | "user_credit-log"
    | "user_detail"
    | "user_index"
    | "user_invitation"
    | "user_open-course-batches"
    | "user_open-course-records"
    | "user_sms-records"
  >;

  /**
   * the custom last level route key
   */
  export type CustomLastLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
    | "exception_403"
    | "exception_404"
    | "exception_500"
  >;

  /**
   * the single level route key
   */
  export type SingleLevelRouteKey = FirstLevelRouteKey & LastLevelRouteKey;

  /**
   * the custom single level route key
   */
  export type CustomSingleLevelRouteKey = CustomFirstLevelRouteKey & CustomLastLevelRouteKey;

  /**
   * the first level route key, but not the single level
  */
  export type FirstLevelRouteNotSingleKey = Exclude<FirstLevelRouteKey, SingleLevelRouteKey>;

  /**
   * the custom first level route key, but not the single level
   */
  export type CustomFirstLevelRouteNotSingleKey = Exclude<CustomFirstLevelRouteKey, CustomSingleLevelRouteKey>;

  /**
   * the center level route key
   */
  export type CenterLevelRouteKey = Exclude<GeneratedRouteKey, FirstLevelRouteKey | LastLevelRouteKey>;

  /**
   * the custom center level route key
   */
  export type CustomCenterLevelRouteKey = Exclude<CustomRouteKey, CustomFirstLevelRouteKey | CustomLastLevelRouteKey>;

  /**
   * the center level route key
   */
  type GetChildRouteKey<K extends RouteKey, T extends RouteKey = RouteKey> = T extends `${K}_${infer R}`
    ? R extends `${string}_${string}`
      ? never
      : T
    : never;

  /**
   * the single level route
   */
  type SingleLevelRoute<K extends SingleLevelRouteKey = SingleLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}$view.${K}`;
      }
    : never;

  /**
   * the last level route
   */
  type LastLevelRoute<K extends GeneratedRouteKey> = K extends LastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `view.${K}`;
      }
    : never;
  
  /**
   * the center level route
   */
  type CenterLevelRoute<K extends GeneratedRouteKey> = K extends CenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the multi level route
   */
  type MultiLevelRoute<K extends FirstLevelRouteNotSingleKey = FirstLevelRouteNotSingleKey> = K extends string
    ? ElegantConstRoute & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}`;
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;
  
  /**
   * the custom first level route
   */
  type CustomSingleLevelRoute<K extends CustomFirstLevelRouteKey = CustomFirstLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `layout.${RouteLayout}$view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom last level route
   */
  type CustomLastLevelRoute<K extends CustomRouteKey> = K extends CustomLastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom center level route
   */
  type CustomCenterLevelRoute<K extends CustomRouteKey> = K extends CustomCenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the custom multi level route
   */
  type CustomMultiLevelRoute<K extends CustomFirstLevelRouteNotSingleKey = CustomFirstLevelRouteNotSingleKey> =
    K extends string
      ? ElegantConstRoute & {
          name: K;
          path: RouteMap[K];
          component: `layout.${RouteLayout}`;
          children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
        }
      : never;

  /**
   * the custom route
   */
  type CustomRoute = CustomSingleLevelRoute | CustomMultiLevelRoute;

  /**
   * the generated route
   */
  type GeneratedRoute = SingleLevelRoute | MultiLevelRoute;

  /**
   * the elegant route
   */
  type ElegantRoute = GeneratedRoute | CustomRoute;
}
