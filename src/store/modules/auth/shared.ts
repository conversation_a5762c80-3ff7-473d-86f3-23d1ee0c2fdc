import {localStg} from '@/utils/storage';

/** Get token */
export function getToken() {
  return localStg.get('token') || '';
}

/** Get user info */
export function getUserInfo() {
  const emptyInfo: Api.Auth.UserInfo = {
    userId: '',
    userName: '',
    roles: [],
    permissions: {
      cms: false,
      ers_order: false,
    },
    menus: []
  };
  return localStg.get('userInfo') || emptyInfo;
}

export function getRoles() {
  const userInfo = getUserInfo();

  return userInfo.roles;
}

export function getPermissions() {
  const userInfo = getUserInfo();

  return userInfo.permissions;
}

/** Clear auth storage */
export function clearAuthStorage() {
  localStg.remove('token');
  localStg.remove('refreshToken');
  localStg.remove('userInfo');
}
