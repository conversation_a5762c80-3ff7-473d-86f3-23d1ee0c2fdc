<?php
declare(strict_types=1);

use ElasticMigrations\Facades\Index;
use ElasticMigrations\MigrationInterface;

final class CreateCmsContentIndex implements MigrationInterface
{
    /**
     * Run the migration.
     */
    public function up(): void
    {
        $mapping = [
            'properties' => [
                'id' => [
                    'type' => 'unsigned_long'
                ],
                'category_id' => [
                    'type' => 'integer'
                ],
                'type' => [
                    'type' => 'text'
                ],
                'doc_type' => [
                    'type' => 'text'
                ],
                'status' => [
                    'type' => 'integer'
                ],
                'title' => [
                    'type' => 'text',
                    'analyzer' => 'ik_max_word',
                    'search_analyzer' => "ik_smart"
                ],
                'intro' => [
                    'type' => 'text',
                    'analyzer' => 'ik_max_word',
                    'search_analyzer' => "ik_smart"
                ],
                'release_at' => [
                    'type' => 'date',
                    "format" => "yyyy-MM-dd HH:mm:ss"
                ]
            ]
        ];
        $settings = [
            'max_result_window' => 10000000
        ];

        Index::createIfNotExistsRaw('cms_contents', $mapping, $settings);
    }

    /**
     * Reverse the migration.
     */
    public function down(): void
    {
        Index::dropIfExists('cms_contents');
    }
}
