<?php
namespace App\Models\Org;

use App\Services\Common\AttachmentService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property int $org_id 机构ID
 * @property int $enroll_id 报名表ID
 * @property int $user_id 用户ID
 * @property int $course_id 课程ID
 * @property int $section_id 小节ID
 * @property string $photo 照片文件路径，存储在 priv 空间
 * @property Carbon $created_at
 *
 * @property-read string $photo_url 抓拍照片URL
 */
class LearnCapture extends Model
{
    const UPDATED_AT = null;

    /**
     * The table associated with the model.
     */
    protected $table = 'org_learn_captures';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'org_id',
        'enroll_id',
        'user_id',
        'course_id',
        'section_id',
        'photo',
        'created_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'enroll_id' => 'integer',
        'user_id' => 'integer',
        'course_id' => 'integer',
        'section_id' => 'integer',
        'created_at' => 'datetime'
    ];

    public function getPhotoUrlAttribute(): string
    {
        return $this->photo ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->photo) : '';
    }
}
