<?php

namespace App\Models\Ers;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


/**
 * @property int $id
 * @property int $order_id 工单ID
 * @property int $flow_id 流程ID
 * @property int $step_id 步骤ID
 * @property int $status 状态 0 未办，1 用户待办，2 后台待办，3 已完结
 * @property string $module 模块
 * @property int $data_id 关联工单模块数据ID
 * @property \Illuminate\Support\Carbon|null $last_admin_handled_at 后台最后处理时间
 * @property \Illuminate\Support\Carbon|null $last_user_handled_at 用户最后处理时间
 * @property int|null $finished_by 由谁完成，0 用户，1 后台
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 * @property-read ServiceOrder $order
 * @property-read FlowStep $step
 * @property-read mixed $data 关联的步骤在表单中的数据
 */
class ServiceOrderStep extends Model
{
    use HasFactory;

    protected $table = 'ers_service_order_steps';

    protected $casts = [
        'order_id' => 'integer',
        'flow_id' => 'integer',
        'step_id' => 'integer',
        'status' => 'integer',
        'data_id' => 'integer',
        'last_admin_handled_at' => 'datetime',
        'last_user_handled_at' => 'datetime'
    ];

    protected $fillable = [
        'order_id',
        'flow_id',
        'step_id',
        'status',
        'module',
        'data_id'
    ];

    /** @var int 未办 */
    const STATUS_PENDING = 0;

    /** @var int 用户等待 */
    const STATUS_USER_PENDING = 1;

    /** @var int 后台等待 */
    const STATUS_ADMIN_PENDING = 2;

    /** @var int 已完成 */
    const STATUS_FINISH = 3;

    /** @var int 由用户完成 */
    const FINISHED_BY_USER = 0;

    /** @var int 由后台完成 */
    const FINISHED_BY_ADMIN = 1;

    /**
     * 状态描述
     *
     * @param int $status
     * @return string
     */
    public static function statusLabel(int $status): string
    {
        return match ($status) {
            self::STATUS_PENDING => '草稿',
            self::STATUS_USER_PENDING => '用户待办',
            self::STATUS_ADMIN_PENDING => '后台待办',
            self::STATUS_FINISH => '已完成'
        };
    }

    public function order()
    {
        return $this->belongsTo(ServiceOrder::class, 'order_id');
    }

    public function step()
    {
        return $this->belongsTo(FlowStep::class, 'step_id')->withTrashed();
    }

    public function data()
    {
        return $this->morphTo(__FUNCTION__, 'module', 'data_id');
    }

    /**
     * 转换为工单中显示的步骤数据
     * @return array
     */
    public function toStepArray()
    {
        return [
            'id' => $this->step_id,
            'name' => $this->step->name,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

}
