<?php

namespace App\Models\Punish;

use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property int $topic_id
 * @property string $intro 违法行为
 * @property string $statute 法律法规
 * @property string $punishment 处罚依据
 * @property string $remark 备注
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubject newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubject newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubject query()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubject whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubject whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubject whereIntro($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubject whereTopicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubject whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class PunishSubject  extends Model
{
    protected $table = 'punish_subjects';

    public function options()
    {
        return $this->hasMany(PunishSubjectOption::class, 'subject_id', 'id');
    }
}
