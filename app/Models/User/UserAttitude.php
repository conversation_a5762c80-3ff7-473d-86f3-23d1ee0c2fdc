<?php

namespace App\Models\User;

use App\Core\Enums\BusinessType;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property BusinessType $business_type 业务类型
 * @property int $business_id 业务ID
 * @property int $attitude 态度  1 赞，2 踩
 * @property \Carbon\Carbon $created_at
 */
class UserAttitude extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'user_attitudes';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'business_type', 'business_id', 'attitude', 'created_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'business_type'=>BusinessType::class, 'business_id' => 'integer', 'attitude' => 'integer', 'created_at' => 'datetime'];

    protected $hidden = ['business_id'];

    public function setUpdatedAt($value)
    {
        return $this;
    }
    /** @var int 点赞 */
    const ATTITUDE_LIKE = 1;
    /** @var int 点踩 */
    const ATTITUDE_DISLIKE = 2;


    public function resource()
    {
        return $this->morphTo('resource', 'business_type', 'business_id');
    }
}
