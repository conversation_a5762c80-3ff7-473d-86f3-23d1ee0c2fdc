<?php

namespace App\Http\Controllers\Api\Chat;

use App\Http\Controllers\Api\Controller;
use App\Models\Chat\ChatMessage;
use App\Models\Chat\ChatSession;
use App\Services\Chat\ChatSessionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class ChatSessionController extends Controller
{
    public function index()
    {
        $buildQuery = ChatSession::query()->where('user_id', auth()->id())->orderBy('id', 'desc');

        return $buildQuery->paginate();
    }

    public function update(Request $request)
    {
        $params = $request->validate([
            'title' => ['required', 'string'],
        ]);

        /** @var ChatSession $session */
        $session = ChatSession::query()->where('user_id', auth()->id())->find($request->id);

        if (!$session) {
            throw new BadRequestHttpException('会话不存在');
        }

        $session->title = $params['title'];
        $session->save();

        return $session;
    }

    public function destroy(Request $request)
    {
        $params = $request->validate([
            'id' => ['required', 'integer'],
        ]);

        $userId = auth()->id();

        if ($params['id'] > 0) {
            ChatSession::query()->where('id', $params['id'])->where('user_id', $userId)->delete();
            ChatMessage::query()->where('session_id', $params['id'])->where('user_id', $userId)->delete();
        } else {
            ChatSession::query()->where('user_id', $userId)->delete();
            ChatMessage::query()->where('user_id', $userId)->delete();
        }

        return ['success' => true, 'data' => []];
    }

    public function recommend()
    {
        $questions = Cache::get(app(ChatSessionService::class)->getRecommendCacheIndex());

        if (!$questions) {
            $questions = app(ChatSessionService::class)->getRecommendDefault();
        }

        shuffle($questions);

        return array_slice($questions, 0, 5);
    }
}
