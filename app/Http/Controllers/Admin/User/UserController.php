<?php

namespace App\Http\Controllers\Admin\User;

use App\Core\Enums\BusinessType;
use App\Http\Controllers\Admin\Controller;
use App\Models\Order\Order;
use App\Models\User;
use App\Models\User\UserCreditLog;
use App\Services\Admin\OperateLogService;
use App\Services\Common\OrderService;
use App\Services\User\BalanceService;
use App\Services\User\CreditService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class UserController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'uuid' => 'string',
            'org_id' => 'integer',
            'nickname' => 'string',
            'phone' => 'string',
            'status' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = User::query()->with(['students' => fn ($q) => $q->with('org'), 'org']);
        $this->builderWhere($builder, $params, ['id', 'uuid', 'phone', 'status', 'created_at']);
        $this->builderOrderBy($request, $builder);

        if (isset($params['nickname']) && $params['nickname']) {
            $builder->where('nickname', 'like', "%{$params['nickname']}%");
        }

        if (isset($params['org_id']) && $params['org_id']) {
            $builder->whereHas('students', function($q) use ($params) {
                $q->where('org_id', $params['org_id']);
            });
        }

        return $this->apiPaginate($request, $builder, hidden: ['password', 'remember_token']);
    }

    public function show(int $id): User
    {
        /** @var User $user */
        $user = User::query()->with(['org'])->find($id);

        if (!$user) {
            throw new NotFoundHttpException('用户不存在');
        }

        $user->setHidden(['password', 'remember_token']);

        return $user;
    }

    public function update(Request $request, int $id): array
    {
        $params = $request->validate([
            'status' => 'integer',
        ]);

        User::query()->where('id', $id)->update($params);

        return $this->success();
    }

    public function search(Request $request): array
    {
        $params = $request->validate([
            'keyword' => 'required|string'
        ]);

        $query = User::query();

        if (preg_match('/^[1-9]\d*$/', $params['keyword'])) {
            // 用户id
            $query->where('id', $params['keyword']);
        } elseif (preg_match('/^[1-9]\d｛10｝$/', $params['keyword'])) {
            // 手机号码
            $query->where('phone', $params['keyword']);
        } else {
            // 昵称
            $query->where('nickname', 'like', "{$params['keyword']}%");
        }

        return $query->limit(20)->get()->setHidden([])->toArray();
    }

    /**
     * 修改用户积分
     */
    public function credit($uid)
    {
        $params = request()->validate([
            'amount' => 'required|integer|min:1',
            'amountType' => 'required|integer',
            'isStatistics' => 'required|nullable',
        ],[],["amount" => "金额"]);

        $credit = CreditService::calcAmountCredit($params['amount']);
        if ($params['amountType'] == 1) {
            if ($params['isStatistics']) {
                $order = new Order();
                $order->user_id = $uid;
                $order->order_no = OrderService::generateUniqueOrderNo($uid);
                $order->total_amount = $params['amount'];
                $order->title = "管理员修改积分";
                $order->business_type = BusinessType::Credit;
                $order->business_id = 0;
                $order->extend = "";
                $order->save();
            } else {
                $order = new Order();
                $order->id = 0;
            }

            CreditService::recharge($uid, $credit, BusinessType::Order, $order->id, '管理员修改增加积分');
        } else {
            CreditService::consume($uid, $credit, BusinessType::Credit, 0, '管理员修改扣除积分');
        }

        OperateLogService::create(auth('admin')->id(),'修改用户积分', ['user_id' => $uid, 'params' => $params]);
    }

    public function changeBalance(int $userId, Request $request): array
    {
        $params = $request->validate([
            'type' => 'required|in:add,sub',
            'amount' => 'required|numeric',
            'remark' => 'required|string'
        ]);

        if ($params['type'] == 'add') {
            BalanceService::recharge($userId, $params['amount'],BusinessType::Order, 0, $params['remark']);
        } else {
            BalanceService::consume($userId, $params['amount'],BusinessType::Order, 0, $params['remark']);
        }

        OperateLogService::create(auth('admin')->id(),'修改用户金额', ['user_id' => $userId, 'params' => $params]);

        return $this->success();
    }
}
