<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Http\Controllers\Admin\Controller;
use App\Models\Inspect\Clues;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

/**
 * 客户线索
 */
class CustomerCluesController extends Controller
{
    public function index(Request $request)
    {
        $params = $request->validate([
            'keyword'     => 'string',
            'created_at'  => 'array',
        ]);

        $keyword    = Arr::get($params, 'keyword');
        $created_at = Arr::get($params, 'created_at');

        $builder = Clues::query()
            ->when(filled($keyword), function ($query) use ($keyword) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                        ->orWhere('phone', 'like', "%{$keyword}%")
                        ->orWhere('company', 'like', "%{$keyword}%");
                });
            })
            ->when($created_at, function ($query) use ($created_at) {
                $query->whereBetween('created_at', $created_at);
            })
            ->orderByDesc('id');

        return $this->apiPaginate($request, $builder);
    }
}
