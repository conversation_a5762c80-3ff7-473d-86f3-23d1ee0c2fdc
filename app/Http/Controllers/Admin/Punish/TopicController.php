<?php

namespace App\Http\Controllers\Admin\Punish;

use App\Http\Controllers\Admin\Controller;
use App\Models\Punish\PunishTopic;
use Illuminate\Http\Request;

class TopicController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'name' => 'string',
            'created_at' => 'array',
        ]);

        $builder = PunishTopic::query()->withCount(['subjects']);
        $this->builderWhere($builder, $params, ['id', 'created_at']);
        $this->builderOrderBy($request, $builder, 'sort desc,id asc');

        if (!empty($params['name'])) {
            $builder->where('name', 'like', "%{$params['name']}%");
        }

        return $this->apiPaginate($request, $builder);
    }


    public function search(Request $request): array
    {
        $params = $request->validate([
            'name' => 'string',
        ]);

        $builder = PunishTopic::query();
        $this->builderOrderBy($request, $builder);

        if (!empty($params['name'])) {
            $builder->where('name', 'like', "%{$params['name']}%");
        }

        return $builder->get()->setHidden([])->toArray();
    }
}
