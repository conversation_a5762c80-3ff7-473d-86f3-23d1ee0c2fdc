<?php

namespace App\Http\Controllers\Admin;

use App\Models\Invitation;
use Illuminate\Http\Request;

class InvitationController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'referral_id' => 'integer',
            'invitee_id' => 'integer',
            'status' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = Invitation::query()->with(['referral', 'invitee']);
        $this->builderWhere($builder, $params, ['id', 'referral_id', 'invitee_id', 'status', 'created_at']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }
}
