<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Core\Enums\BusinessType;
use App\Http\Controllers\Admin\Controller;
use App\Libs\AsyncTasks\WpsDocScreenshotTask;
use App\Models\AsyncTask;
use App\Models\Cms\ContentDoc;
use App\Services\Admin\OperateLogService;
use App\Services\Cms\ContentDocService;
use App\Services\Common\AsyncTaskService;
use Illuminate\Http\Request;

class ContentDocController extends Controller
{
    public function show(int $id): ?ContentDoc
    {
        /** @var ContentDoc $doc */
        $doc = ContentDoc::query()->where('content_id', $id)->first();

        if ($doc) {
            $doc->download_count = $doc->getRawOriginal('download_count');
            $doc->setHidden([]);
        }

        return $doc;
    }

    public function update(Request $request, int $id): ContentDoc
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
            'page_count' => 'required|integer',
            'filepath' => 'required|string',
            'filename' => 'required|string',
            'preview_images' => 'required|array',
            'is_send' => 'required|integer',
            'download_count_add' => 'integer'
        ]);

        $doc = ContentDocService::update($id, $params, boolval($params['is_send']))->setHidden([]);
        $doc->download_count = $doc->getRawOriginal('download_count');

        OperateLogService::create(auth('admin')->id(), '修改资料', $params);

        return $doc;
    }

    public function createScreenshotTask(Request $request): AsyncTask
    {
        $params = $request->validate([
            'filepath' => 'required|string',
            'filename' => 'required|string'
        ]);

        $task = new WpsDocScreenshotTask($params['filepath'], $params['filename']);

        return AsyncTaskService::start($task, BusinessType::Content, 0);
    }

    public function getScreenshotResult(Request $request): array
    {
        $params = $request->validate([
            'task_id' => 'required|integer',
        ]);

        $result = [
            'success' => false,
            'data' => []
        ];

        $complete = AsyncTaskService::complete($params['task_id']);

        if ($complete) {
            $result['success'] = true;
            $result['data'] = $complete;
        }

        return $result;
    }
}
