<?php

namespace App\Http\Middleware;

use App\Models\PersonalAccessToken;
use Closure;
use Illuminate\Http\Request;

class CheckActiveToken
{
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();
        if ($user) {
            $currentToken = $request->bearerToken();
            $hashCurrentToken = hash('sha256', $currentToken);

            /** @var PersonalAccessToken $currentPersonal */
            $currentPersonal = $user->tokens()->where('token', $hashCurrentToken)->first();

            // 获取该用户最新的token
            /** @var PersonalAccessToken $latestPersonal */
            $latestPersonal = $user->tokens()->where('platform', $currentPersonal->platform)->latest('created_at')->first();

            // 如果当前token不是最新的token，则表示在其他设备登录
            if ($latestPersonal && $latestPersonal->token !== $hashCurrentToken) {
                // 删除当前token
                $currentPersonal->delete();

                return response()->json(['message' => '您的账号已在其他设备登录'], 401);
            }
        }

        return $next($request);
    }
}
