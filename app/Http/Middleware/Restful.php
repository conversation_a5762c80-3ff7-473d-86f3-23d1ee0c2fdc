<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;

/**
 * Restful 格式化控制器
 */
class Restful
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, \Closure $next)
    {
        /**
         * @var Response $response
         */
        $response = $next($request);

        if ($response instanceof JsonResponse) {
            if (($response->getEncodingOptions() & JSON_UNESCAPED_UNICODE) == 0) {
                $response->setEncodingOptions(JSON_UNESCAPED_UNICODE);
            }
        } else {
            //要兼容某些自定义数据 Response 的场景，不能全 JOSN 化，非数组的就原样返回
            $content = $response->getContent();
            if (is_array($content)) {
                $response = new JsonResponse($response->getContent(), 200, $response->headers, JSON_UNESCAPED_UNICODE);
            }
        }

        return $response;
    }
}
