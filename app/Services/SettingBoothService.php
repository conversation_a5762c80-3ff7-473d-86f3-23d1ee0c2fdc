<?php

namespace App\Services;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\SettingBooth;
use App\Services\Admin\AttachmentRelationService;

class SettingBoothService
{
    public static function create(string $name, int $type, array $params = []): SettingBooth
    {
        $booth = new SettingBooth();
        $booth->name = $name;
        $booth->type = $type;

        foreach ($params as $key => $val) {
            if (in_array($key, $booth->getFillable()) && $key != 'image') {
                $booth->{$key} = $val;
            }
        }

        $booth->save();

        // 处理封面
        AttachmentRelationService::saveAttachment($booth, $params, 'setting', BusinessType::Booth, true, 'image', 'image');

        return $booth;
    }

    public static function update(int $id, array $params): SettingBooth
    {
        /** @var SettingBooth $booth */
        $booth = SettingBooth::query()->find($id);

        if (!$booth) {
            throw new ServiceException('资源不存在');
        }


        foreach ($params as $key => $val) {
            if (in_array($key, $booth->getFillable()) && $key != 'image') {
                $booth->{$key} = $val;
            }
        }

        $booth->save();

        // 处理图标
        AttachmentRelationService::saveAttachment($booth, $params, 'setting', BusinessType::Booth, false,  'image', 'image');

        return $booth;
    }

    public static function remove(int $id): void
    {
        /** @var SettingBooth $booth */
        $booth = SettingBooth::query()->find($id);

        if (!$booth) {
            throw new ServiceException('资源不存在');
        }

        // 删除附件
        AttachmentRelationService::removeAttachment(BusinessType::Booth, $booth->id);

        $booth->delete();
    }
}
