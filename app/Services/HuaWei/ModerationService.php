<?php

namespace App\Services\HuaWei;

use App\Exceptions\ServiceException;
use HuaweiCloud\SDK\Core\Auth\BasicCredentials;
use HuaweiCloud\SDK\Core\Exceptions\ConnectionException;
use HuaweiCloud\SDK\Core\Exceptions\RequestTimeoutException;
use HuaweiCloud\SDK\Core\Exceptions\ServiceResponseException;
use HuaweiCloud\SDK\Core\Http\HttpConfig;
use HuaweiCloud\SDK\Moderation\V3\Model\CheckImageModerationRequest;
use HuaweiCloud\SDK\Moderation\V3\Model\ImageDetectionReq;
use HuaweiCloud\SDK\Moderation\V3\Model\RunTextModerationRequest;
use HuaweiCloud\SDK\Moderation\V3\Model\TextDetectionDataReq;
use HuaweiCloud\SDK\Moderation\V3\Model\TextDetectionReq;
use HuaweiCloud\SDK\Moderation\V3\ModerationClient;

class ModerationService
{
    protected string $ak;
    protected string $sk;
    protected string $projectId;
    protected string $endpoint;

    public function __construct() {
        $moderRation = config('services.huawei.moderRation');
        $this->ak = $moderRation['ak'];
        $this->sk = $moderRation['sk'];
        $this->projectId = $moderRation['projectId'];
        $this->endpoint = "https://moderation.cn-north-4.myhuaweicloud.com";
    }

    /**
     * 获取客户端
     */
    public function getClient()
    {
        $credentials = new BasicCredentials($this->ak, $this->sk, $this->projectId);
        $config = HttpConfig::getDefaultConfig();
        $config->setIgnoreSslVerification(true);

        return ModerationClient::newBuilder(new ModerationClient)
            ->withHttpConfig($config)
            ->withEndpoint($this->endpoint)
            ->withCredentials($credentials)
            ->build();
    }

    /**
     * 内容审核
     *
     * @param string $content 评论内容
     * @return mixed
     */
    public function contentCheck(string $content): mixed
    {
        $client = $this->getClient();
        $request = new RunTextModerationRequest();

        $body = new TextDetectionReq();
        $dataBody = new TextDetectionDataReq();
        $dataBody->setText($content);
        $body->setData($dataBody);
        $body->setEventType("comment");
        $request->setBody($body);

        try {
            return $client->RunTextModeration($request);
        } catch (ConnectionException $e) {
            throw new ServiceException($e->getMessage());
        } catch (RequestTimeoutException $e) {
            throw new ServiceException($e->getMessage());
        } catch (ServiceResponseException $e) {
            throw new ServiceException("code：" . $e->getHttpStatusCode() . '，errorCode：' . $e->getErrorCode() . '，errorMsg：' . $e->getErrorMsg());
        }
    }

    /**
     * 图片审核
     *
     * @param string $url 图片地址
     * @return mixed
     */
    public function imageCheck(string $url): mixed
    {
        $client = $this->getClient();
        $request = new CheckImageModerationRequest();

        $body = new ImageDetectionReq();
        $listBodyCategories = ["terrorism", "porn"];// 检测场景 terrorism：暴恐元素的检测 porn：涉黄元素的检测
        $body->setUrl($url);
        $body->setCategories($listBodyCategories);
        $body->setEventType("album");
        $request->setBody($body);

        try {
            return $client->CheckImageModeration($request);
        } catch (ConnectionException $e) {
            throw new ServiceException($e->getMessage());
        } catch (RequestTimeoutException $e) {
            throw new ServiceException($e->getMessage());
        } catch (ServiceResponseException $e) {
            throw new ServiceException("code：" . $e->getHttpStatusCode() . '，errorCode：' . $e->getErrorCode() . '，errorMsg：' . $e->getErrorMsg());
        }
    }
}
