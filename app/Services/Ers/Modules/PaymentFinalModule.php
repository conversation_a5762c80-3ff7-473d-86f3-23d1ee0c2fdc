<?php

namespace App\Services\Ers\Modules;

use App\Models\Ers\PaymentOrderPayment;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;

class PaymentFinalModule extends PaymentModule
{

    public static function configure(): ModuleConfigure
    {
        return new ModuleConfigure('payment_final', '付尾款模块', '付尾款', PaymentOrderPayment::class);
    }

    public static function subFlows(ServiceOrder $order, ServiceOrderStep $step)
    {
        return [
            new SubFlow(
                '付尾款',
                SubFlow::status($step, SubFlow::WHO_USER),
                $order->user,
                'payment:pay',
                $step->last_user_handled_at
            )
        ];
    }

}
