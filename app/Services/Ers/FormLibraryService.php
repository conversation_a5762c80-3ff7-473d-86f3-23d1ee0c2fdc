<?php

namespace App\Services\Ers;

use App\Exceptions\ServiceException;
use App\Models\Ers\FormLibrary;
use App\Models\Ers\FormProjectInput;

class FormLibraryService
{
    public static function create(array $params): FormLibrary
    {
        $model = new FormLibrary();

        if ($params['type'] == 'group') {
            $params['options'] = [];
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $model->getFillable())) {
                $model->{$key} = $val;
            }
        }

        $model->save();

        return $model;
    }

    public static function update(int $id, array $params): FormLibrary
    {
        /** @var FormLibrary $model */
        $model = FormLibrary::query()->find($id);

        if (!$model) {
            throw new ServiceException('对象不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $model->getFillable())) {
                $model->{$key} = $val;
            }
        }

        $model->save();

        // 同步工单表单数据
        FormProjectInput::query()->where('form_library_id', $model->id)->update($params);

        return $model;
    }

    public static function remove(int $id): void
    {
        /** @var FormLibrary $model */
        $model = FormLibrary::query()->find($id);

        if (!$model) {
            throw new ServiceException('对象不存在');
        }

        $model->delete();
    }
}
