<?php

namespace App\Services\Org\Export;

/**
 * 导出处理器接口
 * 所有导出处理器必须实现此接口
 */
interface ExporterInterface
{

    /**
     * @param int $orgId 机构ID
     * @param int $checkedId 勾选的ID，如果没有勾选则为0
     * @param array $extra 导出相关参数
     */
    public function __construct(int $orgId, int $checkedId, array $extra);

    /**
     * 控制器入参验证规则
     *
     * @return array
     */
    public static function validateRules(): array;

    /**
     * 生成导出描述文字
     *
     * @return string
     */
    public function generateDesc(): string;

    /**
     * 处理导出任务
     *
     * @return string[]|null 返回导出显示文件名和临时文件路劲，如果为 null 代表不符合导出条件
     * 返回值示例: ['机构1_一期一档_班级1_20250303.docx', 'app/storage/tmp/export/20230303/1234567890.docx']
     */
    public function handle(): array|null;
}
