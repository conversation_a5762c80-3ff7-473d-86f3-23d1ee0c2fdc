<?php

namespace App\Console\Commands;

use App\Core\Enums\BusinessType;
use App\Models\Org\Enrollment;
use App\Services\Common\AttachmentService;
use Illuminate\Console\Command;

class EnrollmentRemoveHourCertCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'enrollment:remove-hour-cert {enroll-id : 学员 ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '移除学时证明图片';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $enrollId = $this->argument('enroll-id');

        /** @var Enrollment $enrollment */
        $enrollment = Enrollment::query()->find($enrollId);

        if (!$enrollment) {
            $this->error("学员 {$enrollId} 不存在");
            return;
        }

        if (!$enrollment->hour_cert) {
            $this->warn("学员 {$enrollId} 没有学时证明图片，无需删除。");
            return;
        }

        AttachmentService::removeRelations(BusinessType::Enrollment, $enrollment->id, $enrollment->hour_cert);

        $enrollment->hour_cert = '';
        $enrollment->save();

        $this->info("已删除学员 {$enrollId} 的学时证明图片。");
    }
}
