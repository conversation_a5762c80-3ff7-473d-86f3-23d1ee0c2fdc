<?php

namespace App\Console\Commands;

use App\Models\AsyncTask;
use Illuminate\Console\Command;

class AsyncTaskPullCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'async-task:pull {--force : 确认执行，否则是 dirty 模式，只展示会涉及到的任务列表} {--taskId= : 指定具体的任务 ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '对超过2小时，48小时内处理未能成功回调的异步任务，尝试拉取并重新处理。';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');

        $taskId = $this->option('taskId');

        $query = AsyncTask::query()
            ->where('status', AsyncTask::STATUS_PROCESSING)
            ->when(
                $taskId,
                fn($query) => $query->where('id', $taskId),
                fn($query) => $query->whereBetween('created_at', [now()->subHours(48)->toDateTimeString(), now()->subHours(2)->toDateTimeString()])
            );


        if ($force) {
            $query->eachById(function (AsyncTask $task) use ($force) {
                if ($force) {
                    $realTask = unserialize($task->task);
                    if ($realTask instanceof \App\Libs\AsyncTasks\AsyncTask) {
                        $taskId = $realTask->start();
                        $task->task_id = $taskId;
                        $task->description = '已重新启动';
                        $task->save();
                        $this->info("{$task->id}. 任务 {$taskId} 已重新发起。");
                    }
                }
            });
        } else {
            $this->table(
                ['Id', 'TaskId', 'BusinessType', 'BusinessId', 'Task', 'Description', 'CreatedAt'],
                $query->get()->map(function (AsyncTask $task) {
                    return [
                        $task->id,
                        $task->task_id,
                        $task->business_type->name,
                        $task->business_id,
                        unserialize($task->task)::class,
                        $task->description,
                        $task->created_at->toDateTimeString()
                    ];
                })->toArray()
            );
        }
    }
}
