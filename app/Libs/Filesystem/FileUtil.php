<?php

namespace App\Libs\Filesystem;

use FileEye\MimeMap\Extension;
use FileEye\MimeMap\MappingException;
use FileEye\MimeMap\Type;
use League\MimeTypeDetection\ExtensionMimeTypeDetector;

class FileUtil
{

    /**
     * 混合文件类型
     *
     * 这种类型没有明确的文件类型，它内部可能包装成其它的多种格式。
     * 比如 apk, pptx 有可能被认为是 applicaton/zip，ppt 有可能是 application/x-ole-storage。
     * 这些类型要做特殊的处理，通过能得到的文件后缀来反推 Mime。
     */
    private static $compositeMimes = [
        'application/octet-stream',
        'application/zip',
        'application/x-ole-storage'
    ];

	/**
	 * 从 URL 中检索出文件的一些信息
	 *
	 * 注意：检索出的文件名并非存储在本地的文件名，而是根据 URL 猜测出来的文件名
	 *
	 * @param string $url
	 * @param string $filePath 文件在本地的路径，当 mime 不能提供文件格式信息时会通过 fileinfo 扩展尝试读取文件类型
	 * @param string $fileBuffer 文件的内容，同上
	 * @param string $mime
     * @param string $contentDisposition 下载文件时的 Content-Disposition 头信息
	 * @return array{basename: string, filename: string, extension: string, mime: string}
     * - basename（有后缀文件名）
     * - filename（无后缀文件名）
     * - extension（后缀，如 jpg）
     * - mime (文件类型，如 image/jpeg)
	 */
	public static function detectFile($url, $filePath=null, $fileBuffer=null, $mime='', $contentDisposition='')
	{
		$ext = '';

		if ((!$mime || in_array($mime, self::$compositeMimes)) && ($filePath || $fileBuffer) && extension_loaded('fileinfo')) {
			$mime = self::detectMimeType($filePath, $fileBuffer);
		}

        if ($contentDisposition) {
            $filename = self::filenameFromContentDisposition($contentDisposition);
        }

        $info = isset($filename) ? pathinfo($filename) : pathinfo(parse_url($url, PHP_URL_PATH));

        //对混合类型通过后缀来反推 mime
        if (!empty($info['extension']) && in_array($mime, self::$compositeMimes)) {
            try {
                $mime = (new Extension($info['extension']))->getDefaultType();
            } catch (MappingException $e) {
                //ignore
            }
        } else {
            //非混合类型通过 mime 推后缀
            if ($mime) {
                try {
                    $ext = (new Type($mime))->getDefaultExtension();
                    $ext == 'jpeg' && $ext = 'jpg';
                } catch (MappingException $e) {
                    //ignore
                }
            }
		}

        //实在推不出，则使用路径中的 ext
        if (!$ext && isset($info['extension'])) {
            $ext = $info['extension'];
        }

        //补上后缀
        $ext && !isset($info['extension']) && $info['basename'] .= '.'.$ext;

		return [
			'basename' => $info['basename'],
			'filename' => $info['filename'],
			'extension' => $ext,
			'mime' => $mime
		];
	}

	/**
	 * 获取文件的 MimeType
	 *
	 * @param null|string $filePath 文件路径名称
	 * @param null|string $buffer 文件缓冲内容，与 $filePath 二选一
	 * @return string
	 */
	public static function detectMimeType($filePath=null, $buffer=null)
	{
		if ($filePath === null && $buffer === null) {
			throw new \InvalidArgumentException('$filePath and $buffer must not all be empty.');
		}

		//优先使用 league/mime-type-detection 解析，对某些新类型解析更准确
		if (class_exists(ExtensionMimeTypeDetector::class)) {
			$detector = new ExtensionMimeTypeDetector();
			return $buffer === null ? $detector->detectMimeTypeFromPath($filePath) : $detector->detectMimeTypeFromBuffer($buffer);
		}

		if (!extension_loaded('fileinfo')) {
			throw new \RuntimeException('Extension fileinfo not loaded.');
		}

		$fi = new \finfo(FILEINFO_MIME_TYPE);
		$mime = $buffer === null ? $fi->file($filePath) : $fi->buffer($buffer);
		unset($fi);

		return $mime;
	}

    /**
     * 从 HTTP 头信息 Content-Disposition 中获取文件名
     *
     * @param string $contentDisposition
     * @return string|null
     */
    public static function filenameFromContentDisposition($contentDisposition)
    {
        $arr = self::parseContentDisposition($contentDisposition);

        if (isset($arr['filename*'])) {
            [$encode, $encodedName] = explode('\'\'', $arr['filename*']);
            $arr['filename'] = rawurldecode($encodedName);
            if ($encode != 'utf-8') {
                $arr['filename'] = iconv($encode, 'utf-8', $arr['filename']);
            }
        }

        return $arr['filename'] ?? null;
    }

    /**
     * 解析 HTTP 头信息 Content-Disposition 为键值对数组
     *
     * @param string $contentDisposition
     * @return array
     */
    private static function parseContentDisposition($contentDisposition)
    {
        [$type, $parameters] = explode(';', $contentDisposition, 2);

        $arr = [$type];

        foreach (explode(';', $parameters) as $row) {
            [$key, $value] = explode('=', trim($row), 2);
            $arr[$key] = $value;
        }

        return $arr;
    }

}
