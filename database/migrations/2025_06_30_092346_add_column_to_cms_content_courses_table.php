<?php

use App\Models\Cms\ContentCourse;
use App\Services\Cms\ContentCourseService;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * cms_content_courses 表新增 hour, duration 字段存储有效的学时、时⻓
     */
    public function up(): void
    {
        $tableName = 'cms_content_courses';
        Schema::table($tableName, function (Blueprint $table) use ($tableName) {
            if (!Schema::hasColumn($tableName, 'hour')) {
                $table->unsignedTinyInteger('hour')->default(0)->after('hour_per_minutes')->comment('学时');
            }

            if (!Schema::hasColumn($tableName, 'duration')) {
                $table->unsignedSmallInteger('duration')->default(0)->after('hour')->comment('课程视频时长（单位：秒）');
            }
        });

        // 迁移数据
        ContentCourse::query()->select(['hour_per_minutes', 'content_id'])->chunkById(100, function ($courses) {
            $courseIds = $courses->pluck('content_id')->toArray();

            $courseDurations = ContentCourseService::getTotalDurations($courseIds);

            foreach ($courses as $course) {
                $course->hour = $course->studyHour($courseDurations[$course->content_id] ?? 0, false);
                $course->duration = $courseDurations[$course->content_id] ?? 0;
                
                $course->save();
            }

        }, 'content_id');

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms_content_courses', function (Blueprint $table) {
            $table->dropColumn('hour');
            $table->dropColumn('duration');
        });
    }
};
